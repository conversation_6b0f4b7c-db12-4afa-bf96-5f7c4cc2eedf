package za.co.ipay.metermng.shared.integration.bulkfreeissue;

import java.io.Serializable;
import java.math.BigDecimal;

import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.shared.dto.BulkParamRecord;

/**
 * Parameter record for bulk free issue operations
 */
public class BulkFreeIssueParamRecord extends BulkParamRecord implements Serializable, IsSerializable {
    
    private static final long serialVersionUID = 1L;
    
    private BigDecimal maxUnitsPerMeter;
    private String defaultDescription;
    private String defaultReference;
    private String defaultReason;
    
    public BulkFreeIssueParamRecord() {
    }
    
    public BigDecimal getMaxUnitsPerMeter() {
        return maxUnitsPerMeter;
    }
    
    public void setMaxUnitsPerMeter(BigDecimal maxUnitsPerMeter) {
        this.maxUnitsPerMeter = maxUnitsPerMeter;
    }
    
    public String getDefaultDescription() {
        return defaultDescription;
    }
    
    public void setDefaultDescription(String defaultDescription) {
        this.defaultDescription = defaultDescription;
    }
    
    public String getDefaultReference() {
        return defaultReference;
    }
    
    public void setDefaultReference(String defaultReference) {
        this.defaultReference = defaultReference;
    }
    
    public String getDefaultReason() {
        return defaultReason;
    }
    
    public void setDefaultReason(String defaultReason) {
        this.defaultReason = defaultReason;
    }
    
    @Override
    public String toString() {
        return "BulkFreeIssueParamRecord{" +
                "maxUnitsPerMeter=" + maxUnitsPerMeter +
                ", defaultDescription='" + defaultDescription + '\'' +
                ", defaultReference='" + defaultReference + '\'' +
                ", defaultReason='" + defaultReason + '\'' +
                '}';
    }
}
