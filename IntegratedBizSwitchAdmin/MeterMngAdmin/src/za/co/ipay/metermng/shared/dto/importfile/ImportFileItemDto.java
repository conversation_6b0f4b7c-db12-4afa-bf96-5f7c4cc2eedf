package za.co.ipay.metermng.shared.dto.importfile;

import java.io.Serializable;

import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.shared.integration.GenericImportRecord;
import za.co.ipay.metermng.shared.integration.bulkblocking.BulkBlockingImportRecord;
import za.co.ipay.metermng.shared.integration.bulkkeychange.BulkKeyChangeImportRecord;
import za.co.ipay.metermng.shared.integration.bulkmdc.BulkMdcImportRecord;
import za.co.ipay.metermng.shared.integration.bulkpricingstructurechange.BulkPricingStructureChangeImportRecord;
import za.co.ipay.metermng.shared.integration.bulkstoremovement.BulkStoreMovementImportRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;
import za.co.ipay.metermng.shared.integration.ipay.IpayDebtImportRecord;
import za.co.ipay.metermng.shared.integration.pricingstructurechange.PricingStructureChangeImportRecord;
import za.co.ipay.metermng.shared.integration.registerreading.RegisterReadingImportRecord;
import za.co.ipay.metermng.shared.integration.samras.SamrasDebtImportRecord;
import za.co.ipay.metermng.shared.integration.sap.CustomerMovementRecord;
import za.co.ipay.metermng.shared.integration.sap.DebtImportRecord;
import za.co.ipay.metermng.shared.integration.solar.SolarDebtImportRecord;
import za.co.ipay.metermng.shared.integration.tariffexport.TariffPsImportRecord;

public class ImportFileItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private boolean selectForImport = false;
    private ImportFileItem importFileItem;
    private String importFileRecordString;
    private GenericImportRecord genericImportRecord;

    private String importFileItemImportComment;

    public ImportFileItemDto() {
    }

    public ImportFileItemDto(ImportFileItem importFileItem) {
        this.importFileItem = importFileItem;
        selectForImport = false;
    }

    public boolean isSelectForImport() {
        return selectForImport;
    }

    public void setSelectForImport(boolean selectForImport) {
        this.selectForImport = selectForImport;
    }

    public ImportFileItem getImportFileItem() {
        return importFileItem;
    }

    public void setImportFileItem(ImportFileItem importFileItem) {
        this.importFileItem = importFileItem;
    }

    public String getImportFileItemImportComment() {
        return importFileItemImportComment;
    }

    public void setImportFileItemImportComment(String importFileItemImportComment) {
        this.importFileItemImportComment = importFileItemImportComment;
    }

    public String getImportFileRecordStr() {
        return importFileRecordString;
    }

    public void setImportFileRecordStr(String importFileRecordStr) {
        this.importFileRecordString = importFileRecordStr;
    }

    public GenericImportRecord getGenericImportRecord() {
        return genericImportRecord;
    }

    public void setGenericImportRecord(GenericImportRecord genericImportRecord) {
        this.genericImportRecord = genericImportRecord;
    }

    public CustomerMovementRecord getCustomerMovementRecord() {
        return (CustomerMovementRecord) genericImportRecord;
    }

    public SamrasDebtImportRecord getSamrasDebtImportRecord() {
        return (SamrasDebtImportRecord) genericImportRecord;
    }

    public SolarDebtImportRecord getSolarDebtImportRecord() {
        return (SolarDebtImportRecord)   genericImportRecord;
    }

    public DebtImportRecord getSapDebtImportRecord() {
        return (DebtImportRecord) genericImportRecord;
    }

    public RegisterReadingImportRecord getRegReadImportRecord() {
        return (RegisterReadingImportRecord) genericImportRecord;
    }

    public TariffPsImportRecord getTariffPsImportRecord() {
        return (TariffPsImportRecord) genericImportRecord;
    }

    public BulkKeyChangeImportRecord getBulkKeyChangeImportRecord() {
        return (BulkKeyChangeImportRecord) genericImportRecord;
    }

    public BulkPricingStructureChangeImportRecord getBulkPricingStructureChangeImportRecord() {
        return (BulkPricingStructureChangeImportRecord) genericImportRecord;
    }

    public BulkBlockingImportRecord getBulkBlockingImportRecord() {
        return (BulkBlockingImportRecord) genericImportRecord;
    }

    public BulkStoreMovementImportRecord getBulkStoreMovementImportRecord() {
        return (BulkStoreMovementImportRecord) genericImportRecord;
    }

    public IpayDebtImportRecord getIpayDebtImportRecord() {
        return (IpayDebtImportRecord) genericImportRecord;
    }

    public PricingStructureChangeImportRecord getPricingStructureChangeImportRecord() {
        return (PricingStructureChangeImportRecord) genericImportRecord;
    }

    public BulkMdcImportRecord getBulkMdcImportRecord() {
        return (BulkMdcImportRecord) genericImportRecord;
    }

    public BulkFreeIssueImportRecord getBulkFreeIssueImportRecord() {
        return (BulkFreeIssueImportRecord) genericImportRecord;
    }
}
