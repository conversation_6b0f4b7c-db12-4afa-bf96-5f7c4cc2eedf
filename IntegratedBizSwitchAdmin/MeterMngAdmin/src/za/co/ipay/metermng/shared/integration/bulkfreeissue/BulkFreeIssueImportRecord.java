package za.co.ipay.metermng.shared.integration.bulkfreeissue;

import java.io.Serializable;
import java.math.BigDecimal;

import com.google.gwt.user.client.rpc.IsSerializable;

import za.co.ipay.metermng.shared.integration.GenericImportRecord;

/**
 * Import record for bulk free issue operations
 */
public class BulkFreeIssueImportRecord implements GenericImportRecord, Serializable, IsSerializable {
    
    private static final long serialVersionUID = 1L;
    
    private String meterNumber;
    private BigDecimal units;
    private String description;
    private String reference;
    private String reason;
    
    public BulkFreeIssueImportRecord() {
    }
    
    public BulkFreeIssueImportRecord(String meterNumber, BigDecimal units, String description, String reference, String reason) {
        this.meterNumber = meterNumber;
        this.units = units;
        this.description = description;
        this.reference = reference;
        this.reason = reason;
    }
    
    public String getMeterNumber() {
        return meterNumber;
    }
    
    public void setMeterNumber(String meterNumber) {
        this.meterNumber = meterNumber;
    }
    
    public BigDecimal getUnits() {
        return units;
    }
    
    public void setUnits(BigDecimal units) {
        this.units = units;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getReference() {
        return reference;
    }
    
    public void setReference(String reference) {
        this.reference = reference;
    }
    
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    @Override
    public String toString() {
        return "BulkFreeIssueImportRecord{" +
                "meterNumber='" + meterNumber + '\'' +
                ", units=" + units +
                ", description='" + description + '\'' +
                ", reference='" + reference + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}
