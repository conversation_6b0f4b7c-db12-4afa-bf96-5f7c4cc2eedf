package za.co.ipay.metermng.integration.bulkfreeissue;

import java.math.BigDecimal;

import za.co.ipay.metermng.fileimport.ImportItemDataConverter;
import za.co.ipay.metermng.shared.integration.GenericImportRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;

/**
 * Converter for bulk free issue import data between string and object formats
 */
public class BulkFreeIssueItemDataConverter implements ImportItemDataConverter {
    
    private static final String DELIMITER = ",";
    
    @Override
    public GenericImportRecord convertToObject(String itemData) {
        if (itemData == null || itemData.trim().isEmpty()) {
            return null;
        }
        
        String[] fields = itemData.split(DELIMITER, -1); // -1 to include empty trailing fields
        BulkFreeIssueImportRecord record = new BulkFreeIssueImportRecord();
        
        try {
            // MeterNumber (required)
            if (fields.length > 0) {
                record.setMeterNumber(cleanField(fields[0]));
            }
            
            // Units (required)
            if (fields.length > 1) {
                String unitsStr = cleanField(fields[1]);
                if (unitsStr != null && !unitsStr.isEmpty()) {
                    record.setUnits(new BigDecimal(unitsStr));
                }
            }
            
            // Description (optional)
            if (fields.length > 2) {
                record.setDescription(cleanField(fields[2]));
            }
            
            // Reference (optional)
            if (fields.length > 3) {
                record.setReference(cleanField(fields[3]));
            }
            
            // Reason (optional)
            if (fields.length > 4) {
                record.setReason(cleanField(fields[4]));
            }
            
        } catch (NumberFormatException e) {
            throw new RuntimeException("Invalid number format in bulk free issue data: " + itemData, e);
        }
        
        return record;
    }
    
    @Override
    public String convertToString(GenericImportRecord record) {
        if (!(record instanceof BulkFreeIssueImportRecord)) {
            throw new IllegalArgumentException("Expected BulkFreeIssueImportRecord, got: " + 
                (record != null ? record.getClass().getSimpleName() : "null"));
        }
        
        BulkFreeIssueImportRecord bulkRecord = (BulkFreeIssueImportRecord) record;
        StringBuilder sb = new StringBuilder();
        
        // MeterNumber
        sb.append(escapeField(bulkRecord.getMeterNumber()));
        sb.append(DELIMITER);
        
        // Units
        sb.append(escapeField(bulkRecord.getUnits() != null ? bulkRecord.getUnits().toString() : ""));
        sb.append(DELIMITER);
        
        // Description
        sb.append(escapeField(bulkRecord.getDescription()));
        sb.append(DELIMITER);
        
        // Reference
        sb.append(escapeField(bulkRecord.getReference()));
        sb.append(DELIMITER);
        
        // Reason
        sb.append(escapeField(bulkRecord.getReason()));
        
        return sb.toString();
    }
    
    private String cleanField(String field) {
        if (field == null) {
            return null;
        }
        
        // Remove quotes if present
        field = field.trim();
        if (field.startsWith("\"") && field.endsWith("\"") && field.length() > 1) {
            field = field.substring(1, field.length() - 1);
        }
        
        return field.isEmpty() ? null : field;
    }
    
    private String escapeField(String field) {
        if (field == null) {
            return "";
        }
        
        // If field contains comma, quote, or newline, wrap in quotes and escape internal quotes
        if (field.contains(DELIMITER) || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        
        return field;
    }
}
