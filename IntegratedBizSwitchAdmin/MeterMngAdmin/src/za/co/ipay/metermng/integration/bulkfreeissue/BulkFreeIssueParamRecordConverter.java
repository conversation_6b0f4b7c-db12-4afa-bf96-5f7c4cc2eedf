package za.co.ipay.metermng.integration.bulkfreeissue;

import java.math.BigDecimal;

import za.co.ipay.metermng.fileimport.ImportParamRecordConverter;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParamRecord;

/**
 * Converter for bulk free issue parameter records between string and object formats
 */
public class BulkFreeIssueParamRecordConverter implements ImportParamRecordConverter {
    
    private static final String DELIMITER = "|";
    private static final String FIELD_SEPARATOR = "=";
    
    @Override
    public BulkParamRecord convertToObject(String paramData) {
        if (paramData == null || paramData.trim().isEmpty()) {
            return new BulkFreeIssueParamRecord();
        }
        
        BulkFreeIssueParamRecord record = new BulkFreeIssueParamRecord();
        String[] fields = paramData.split("\\" + DELIMITER);
        
        for (String field : fields) {
            if (field.contains(FIELD_SEPARATOR)) {
                String[] keyValue = field.split(FIELD_SEPARATOR, 2);
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();
                    
                    switch (key) {
                        case "maxUnitsPerMeter":
                            if (!value.isEmpty()) {
                                try {
                                    record.setMaxUnitsPerMeter(new BigDecimal(value));
                                } catch (NumberFormatException e) {
                                    // Log warning but continue
                                }
                            }
                            break;
                        case "defaultDescription":
                            record.setDefaultDescription(value.isEmpty() ? null : value);
                            break;
                        case "defaultReference":
                            record.setDefaultReference(value.isEmpty() ? null : value);
                            break;
                        case "defaultReason":
                            record.setDefaultReason(value.isEmpty() ? null : value);
                            break;
                    }
                }
            }
        }
        
        return record;
    }
    
    @Override
    public String convertToString(BulkParamRecord record) {
        if (!(record instanceof BulkFreeIssueParamRecord)) {
            throw new IllegalArgumentException("Expected BulkFreeIssueParamRecord, got: " + 
                (record != null ? record.getClass().getSimpleName() : "null"));
        }
        
        BulkFreeIssueParamRecord bulkRecord = (BulkFreeIssueParamRecord) record;
        StringBuilder sb = new StringBuilder();
        
        // Max units per meter
        if (bulkRecord.getMaxUnitsPerMeter() != null) {
            sb.append("maxUnitsPerMeter").append(FIELD_SEPARATOR).append(bulkRecord.getMaxUnitsPerMeter().toString());
            sb.append(DELIMITER);
        }
        
        // Default description
        if (bulkRecord.getDefaultDescription() != null && !bulkRecord.getDefaultDescription().trim().isEmpty()) {
            sb.append("defaultDescription").append(FIELD_SEPARATOR).append(bulkRecord.getDefaultDescription());
            sb.append(DELIMITER);
        }
        
        // Default reference
        if (bulkRecord.getDefaultReference() != null && !bulkRecord.getDefaultReference().trim().isEmpty()) {
            sb.append("defaultReference").append(FIELD_SEPARATOR).append(bulkRecord.getDefaultReference());
            sb.append(DELIMITER);
        }
        
        // Default reason
        if (bulkRecord.getDefaultReason() != null && !bulkRecord.getDefaultReason().trim().isEmpty()) {
            sb.append("defaultReason").append(FIELD_SEPARATOR).append(bulkRecord.getDefaultReason());
            sb.append(DELIMITER);
        }
        
        // Remove trailing delimiter
        if (sb.length() > 0 && sb.charAt(sb.length() - 1) == DELIMITER.charAt(0)) {
            sb.setLength(sb.length() - 1);
        }
        
        return sb.toString();
    }
}
