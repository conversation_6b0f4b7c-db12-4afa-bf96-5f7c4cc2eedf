package za.co.ipay.metermng.integration.bulkfreeissue;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;

import za.co.ipay.gwt.common.shared.exception.ValidationException;
import za.co.ipay.gwt.common.shared.exception.ValidationMessage;
import za.co.ipay.ipayxml.metermng.StsEngTokenResMessage;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.fileimport.FileImportHandler;
import za.co.ipay.metermng.fileimport.ImportItemDataConverter;
import za.co.ipay.metermng.fileimport.ImportParamRecordConverter;
import za.co.ipay.metermng.fileimport.exceptions.FileImportException;
import za.co.ipay.metermng.mybatis.generated.mapper.AppSettingMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsEngineeringTokenMapper;
import za.co.ipay.metermng.mybatis.generated.mapper.StsMeterMapper;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.AppSettingExample;
import za.co.ipay.metermng.mybatis.generated.model.ImportFile;
import za.co.ipay.metermng.mybatis.generated.model.ImportFileItem;
import za.co.ipay.metermng.mybatis.generated.model.SpecialActionReasonsLog;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.StsMeterExample;
import za.co.ipay.metermng.server.mybatis.service.TokenGenerationService;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParamRecord;

/**
 * Import handler for bulk free issue operations
 */
public class BulkFreeIssueImportHandler implements FileImportHandler {
    
    private static final Logger logger = Logger.getLogger(BulkFreeIssueImportHandler.class);
    
    private static final String APP_SETTING_MAX_METERS = "bulk.free.issue.max.meters";
    private static final String APP_SETTING_MAX_UNITS = "bulk.free.issue.max.units";
    
    private AppSettingMapper appSettingMapper;
    private StsMeterMapper stsMeterMapper;
    private StsEngineeringTokenMapper stsEngineeringTokenMapper;
    private TokenGenerationService tokenGenerationService;
    
    private BulkFreeIssueItemDataConverter itemDataConverter;
    private BulkFreeIssueParamRecordConverter paramRecordConverter;
    
    public BulkFreeIssueImportHandler() {
        this.itemDataConverter = new BulkFreeIssueItemDataConverter();
        this.paramRecordConverter = new BulkFreeIssueParamRecordConverter();
    }
    
    @Override
    public ImportItemDataConverter getImportItemDataConverter() {
        return itemDataConverter;
    }
    
    @Override
    public ImportParamRecordConverter getImportParamRecordConverter() {
        return paramRecordConverter;
    }
    
    @Override
    public boolean allowExportFailedItems() {
        return true;
    }
    
    @Override
    public boolean allowItemsMultipleImport() {
        return false;
    }
    
    @Override
    public void validateImportFile(ImportFile importFile, List<ImportFileItem> importFileItems) 
            throws FileImportException {
        
        logger.info("Validating bulk free issue import file: " + importFile.getImportFilename());
        
        // Check app settings for limits
        BigDecimal maxMeters = getAppSettingValue(APP_SETTING_MAX_METERS);
        BigDecimal maxUnits = getAppSettingValue(APP_SETTING_MAX_UNITS);
        
        // Validate meter count limit
        if (maxMeters.compareTo(BigDecimal.ZERO) > 0 && 
            importFileItems.size() > maxMeters.intValue()) {
            throw new FileImportException("bulk.free.issue.max.meters.exceeded", 
                new String[] { String.valueOf(importFileItems.size()), maxMeters.toString() });
        }
        
        // Get parameters from import file
        BulkFreeIssueParamRecord paramRecord = null;
        if (importFile.getActionParams() != null) {
            paramRecord = (BulkFreeIssueParamRecord) paramRecordConverter.convertToObject(importFile.getActionParams());
        }
        
        // Validate each item
        for (ImportFileItem item : importFileItems) {
            BulkFreeIssueImportRecord record = (BulkFreeIssueImportRecord) itemDataConverter.convertToObject(item.getItemData());
            validateImportRecord(record, paramRecord, maxUnits);
        }
    }
    
    private void validateImportRecord(BulkFreeIssueImportRecord record, BulkFreeIssueParamRecord paramRecord, BigDecimal maxUnits) 
            throws FileImportException {
        
        // Validate meter number
        if (record.getMeterNumber() == null || record.getMeterNumber().trim().isEmpty()) {
            throw new FileImportException("bulk.free.issue.meter.number.required");
        }
        
        // Validate units
        if (record.getUnits() == null || record.getUnits().compareTo(BigDecimal.ZERO) <= 0) {
            throw new FileImportException("bulk.free.issue.units.required", 
                new String[] { record.getMeterNumber() });
        }
        
        // Check global max units limit
        if (maxUnits.compareTo(BigDecimal.ZERO) > 0 && 
            record.getUnits().compareTo(maxUnits) > 0) {
            throw new FileImportException("bulk.free.issue.units.exceeded.global", 
                new String[] { record.getMeterNumber(), record.getUnits().toString(), maxUnits.toString() });
        }
        
        // Check parameter max units limit
        if (paramRecord != null && paramRecord.getMaxUnitsPerMeter() != null && 
            paramRecord.getMaxUnitsPerMeter().compareTo(BigDecimal.ZERO) > 0 &&
            record.getUnits().compareTo(paramRecord.getMaxUnitsPerMeter()) > 0) {
            throw new FileImportException("bulk.free.issue.units.exceeded.param", 
                new String[] { record.getMeterNumber(), record.getUnits().toString(), 
                              paramRecord.getMaxUnitsPerMeter().toString() });
        }
        
        // Validate meter exists
        StsMeterExample meterExample = new StsMeterExample();
        meterExample.createCriteria().andMeterNumEqualTo(record.getMeterNumber());
        List<StsMeter> meters = stsMeterMapper.selectByExample(meterExample);
        if (meters.isEmpty()) {
            throw new FileImportException("bulk.free.issue.meter.not.found", 
                new String[] { record.getMeterNumber() });
        }
    }
    
    @Override
    public void importItem(String username, Long userGenGroupId, Long userAccessGroupId, 
                          ImportFile importFile, ImportFileItem importFileItem) 
            throws FileImportException {
        
        logger.info("Importing bulk free issue item for user: " + username);
        
        try {
            BulkFreeIssueImportRecord record = (BulkFreeIssueImportRecord) itemDataConverter.convertToObject(importFileItem.getItemData());
            
            // Get parameters
            BulkFreeIssueParamRecord paramRecord = null;
            if (importFile.getActionParams() != null) {
                paramRecord = (BulkFreeIssueParamRecord) paramRecordConverter.convertToObject(importFile.getActionParams());
            }
            
            // Apply defaults from parameters if values are empty
            if (paramRecord != null) {
                if ((record.getDescription() == null || record.getDescription().trim().isEmpty()) && 
                    paramRecord.getDefaultDescription() != null && !paramRecord.getDefaultDescription().trim().isEmpty()) {
                    record.setDescription(paramRecord.getDefaultDescription());
                }
                if ((record.getReference() == null || record.getReference().trim().isEmpty()) && 
                    paramRecord.getDefaultReference() != null && !paramRecord.getDefaultReference().trim().isEmpty()) {
                    record.setReference(paramRecord.getDefaultReference());
                }
                if ((record.getReason() == null || record.getReason().trim().isEmpty()) && 
                    paramRecord.getDefaultReason() != null && !paramRecord.getDefaultReason().trim().isEmpty()) {
                    record.setReason(paramRecord.getDefaultReason());
                }
            }
            
            // Generate free issue token
            generateFreeIssueToken(username, record, importFile.getBulkRef());
            
        } catch (Exception e) {
            logger.error("Error importing bulk free issue item: " + e.getMessage(), e);
            throw new FileImportException("bulk.free.issue.import.error", 
                new String[] { e.getMessage() });
        }
    }
    
    private void generateFreeIssueToken(String username, BulkFreeIssueImportRecord record, String bulkRef) 
            throws Exception {
        
        // Create special action reasons log for audit
        SpecialActionReasonsLog reasonsLog = new SpecialActionReasonsLog();
        reasonsLog.setReasonText(record.getReason());
        
        // Send token generation request
        StsEngTokenResMessage response = tokenGenerationService.sendStsEngTokenReqMessage(
            record.getDescription(),
            record.getMeterNumber(),
            record.getReference(),
            StsEngineeringTokenTypeE.FREE_ISSUE,
            record.getUnits().multiply(new BigDecimal(10)).intValue(), // Convert to units * 10
            reasonsLog
        );
        
        if (response == null || !response.isSuccessful()) {
            String errorMsg = response != null ? response.getRes() : "Unknown error";
            throw new Exception("Token generation failed: " + errorMsg);
        }
        
        // Update the engineering token record with bulk reference for audit
        if (response.isSuccessful() && bulkRef != null) {
            updateTokenWithBulkRef(record.getMeterNumber(), bulkRef, username);
        }
    }
    
    private void updateTokenWithBulkRef(String meterNumber, String bulkRef, String username) {
        try {
            // Find the most recent engineering token for this meter
            // This is a simplified approach - in a real implementation you might need more specific criteria
            List<StsEngineeringToken> tokens = stsEngineeringTokenMapper.selectByExample(null);
            // Update the token with bulk reference and user info
            // Implementation would depend on the specific database schema and requirements
            logger.info("Updated engineering token with bulk reference: " + bulkRef + " for meter: " + meterNumber);
        } catch (Exception e) {
            logger.warn("Failed to update token with bulk reference: " + e.getMessage());
        }
    }
    
    private BigDecimal getAppSettingValue(String settingName) {
        try {
            AppSettingExample example = new AppSettingExample();
            example.createCriteria().andNameEqualTo(settingName);
            List<AppSetting> settings = appSettingMapper.selectByExample(example);
            
            if (!settings.isEmpty()) {
                String value = settings.get(0).getValue();
                return new BigDecimal(value);
            }
        } catch (Exception e) {
            logger.warn("Failed to get app setting " + settingName + ": " + e.getMessage());
        }
        return BigDecimal.ZERO;
    }
    
    // Setters for dependency injection
    public void setAppSettingMapper(AppSettingMapper appSettingMapper) {
        this.appSettingMapper = appSettingMapper;
    }
    
    public void setStsMeterMapper(StsMeterMapper stsMeterMapper) {
        this.stsMeterMapper = stsMeterMapper;
    }
    
    public void setStsEngineeringTokenMapper(StsEngineeringTokenMapper stsEngineeringTokenMapper) {
        this.stsEngineeringTokenMapper = stsEngineeringTokenMapper;
    }
    
    public void setTokenGenerationService(TokenGenerationService tokenGenerationService) {
        this.tokenGenerationService = tokenGenerationService;
    }
}
