package za.co.ipay.metermng.client.view.component.meter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.mybatis.custom.model.RegisterReadingExt;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.UsagePointData;

public class RegisterReadingsView extends BaseComponent implements IpayDataProviderFilter<RegisterReadingExt> {
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static RegisterReadingsViewUiBinder uiBinder = GWT.create(RegisterReadingsViewUiBinder.class);

    interface RegisterReadingsViewUiBinder extends UiBinder<Widget, RegisterReadingsView> {
    }

    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;

    @UiField FormElement startElement;
    @UiField DateBox startBox;
    @UiField FormElement endElement;
    @UiField DateBox endBox;
    @UiField Button viewBtn;
    @UiField Button clearBtn;
    @UiField HorizontalPanel totalConsumptionHP;
    @UiField HTML totalConsumption;

    @UiField(provided=true) CellTable<RegisterReadingExt> clltblTransactions;
    @UiField TablePager smplpgrTransactions;

    private MeterData meterData;
    private UsagePointData usagePointData;
    private ArrayList<RegisterReadingExt> theTransactiondata;

    private Column<RegisterReadingExt, String> readingTimeStampCol;
    private TextColumn<RegisterReadingExt> channelCol;
    private TextColumn<RegisterReadingExt> determinantCol;

    private IPayDataProvider<RegisterReadingExt> dataProvider = new IPayDataProvider<RegisterReadingExt>(this);
    private ListHandler<RegisterReadingExt> columnSortHandler;
    private ColumnSortList columnSortList;

    private boolean viewConstructed = false;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private Logger logger = Logger.getLogger(RegisterReadingsView.class.getName());
    private boolean isUsagePointTabSelected = false;

    public RegisterReadingsView(ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView) {
        setClientFactory(clientFactory);
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        initUi();
    }

    private void initUi() {
        setIsUsagePointTabSelected();
        initView();
        initTable();

        viewConstructed = true;
    }

    private void setIsUsagePointTabSelected() {
        isUsagePointTabSelected = usagePointWorkspaceView.getSelectedTab() == usagePointWorkspaceView.getUptabinx();
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initView() {
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage(""));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("register.reading.txn.channel"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("register.reading.txn.determinant"));
        if (isUsagePointTabSelected) {
            filterDropdown.addItem(MessagesUtil.getInstance().getMessage("register.reading.txn.meter"));
            dataDescription.setText(MessagesUtil.getInstance().getMessage("usage.point.register.reading.txn.description"));
        } else {
            dataDescription.setText(MessagesUtil.getInstance().getMessage("register.reading.txn.description"));
        }
        dataName.setText(MessagesUtil.getInstance().getMessage("register.reading.txn.label"));

        Format format = FormatUtil.getInstance();
        StrictDateFormat strictDateFormat = new StrictDateFormat(
                DateTimeFormat.getFormat(format.getDateFormat() + " " + format.getTimeFormat()));
        startBox.setFormat(strictDateFormat);
        endBox.setFormat(strictDateFormat);
        startBox.setValue(getInitStartDate());
        endBox.setValue(getInitEndDate());

        viewBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent arg0) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        onView();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        clearBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onClear();
            }
        });
    }

    private Date getInitStartDate() {
        String start = DateTimeFormat.getFormat("MM/yyyy").format(new Date());
        return DateTimeFormat.getFormat("dd/MM/yyyy").parse("01/" + start);
    }

    private Date getInitEndDate() {
        return new Date(); //now
    }

    protected void createTable() {
        clltblTransactions = new CellTable<RegisterReadingExt>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private void initTable() {
        readingTimeStampCol = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                return FormatUtil.getInstance().formatDateTime(data.getReadingTimestamp());
            }
        };
        readingTimeStampCol.setSortable(true);
        readingTimeStampCol.setDefaultSortAscending(false);

        channelCol = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                return data.getChannelName();
            }
        };
        channelCol.setSortable(true);

        determinantCol = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                StringBuilder bldr = new StringBuilder();
                for (String s: data.getDeterminantNameList()) {
                    bldr.append(s).append(", ");
                }
                String billingDetNames = bldr.toString();
                if (!billingDetNames.isEmpty()) {
                    billingDetNames = billingDetNames.substring(0, billingDetNames.length() - 2);
                }
                return billingDetNames;
            }

        };
        determinantCol.setSortable(true);

        TextColumn<RegisterReadingExt> readingTypeCol = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                return data.getMeterReadingTypeName();
            }
        };

        TextColumn<RegisterReadingExt> readingCol = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                return FormatUtil.getInstance().formatDecimal(data.getReadingValue().divide(new BigDecimal(1000)));
            }
        };

        TextColumn<RegisterReadingExt> receiptNumber = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                return data.getReceiptNum();
            }
        };

        TextColumn<RegisterReadingExt> createDateCol = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                return FormatUtil.getInstance().formatDateTime(data.getDateCreated());
            }
        };

        TextColumn<RegisterReadingExt> meterCol = new TextColumn<RegisterReadingExt>() {
            @Override
            public String getValue(RegisterReadingExt data) {
                return data.getMeterNum();
            }
        };

        if (isUsagePointTabSelected) {
            meterCol.setSortable(true);
        }

        clltblTransactions.addColumn(meterCol, MessagesUtil.getInstance().getMessage("register.reading.txn.meter"));
        clltblTransactions.addColumn(createDateCol, MessagesUtil.getInstance().getMessage("register.reading.txn.create_date"));
        clltblTransactions.addColumn(readingTimeStampCol, MessagesUtil.getInstance().getMessage("register.reading.txn.timestamp"));
        clltblTransactions.addColumn(channelCol, MessagesUtil.getInstance().getMessage("register.reading.txn.channel"));
        clltblTransactions.addColumn(determinantCol, MessagesUtil.getInstance().getMessage("register.reading.txn.determinant"));
        clltblTransactions.addColumn(readingTypeCol, MessagesUtil.getInstance().getMessage("register.reading.txn.readingtype"));
        clltblTransactions.addColumn(readingCol, MessagesUtil.getInstance().getMessage("register.reading.txn.readingvalue"));
        clltblTransactions.addColumn(receiptNumber, MessagesUtil.getInstance().getMessage("readings.table.receiptnum"));
        dataProvider.addDataDisplay(clltblTransactions);
        smplpgrTransactions.setDisplay(clltblTransactions);

    }

    private void onView() {
        if (endBox.getValue().before(startBox.getValue())) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("register.reading.txn.error.enddate"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    endBox.getAbsoluteLeft() + endBox.getOffsetWidth(),
                    endBox.getAbsoluteTop() - endBox.getOffsetHeight(),
                    null);
            return;
        }
        if (isUsagePointTabSelected) {
            clientFactory.getMeterRpc().getRegisterReadingsByUsagePointId(usagePointData.getId(), startBox.getValue(), endBox.getValue(),
                    new ClientCallback<List<RegisterReadingExt>>() {
                        @Override
                        public void onSuccess(List<RegisterReadingExt> result) {
                            if (result != null) {
                                handleResultData(result);
                            }
                        }
                    });
        } else {
            clientFactory.getMeterRpc().getRegisterReadingsByMeterId(meterData.getId(), startBox.getValue(), endBox.getValue(),
                    new ClientCallback<List<RegisterReadingExt>>() {
                        @Override
                        public void onSuccess(List<RegisterReadingExt> result) {
                            if (result != null) {
                                handleResultData(result);
                            }
                        }
                    });
        }
    }

    private void handleResultData(List<RegisterReadingExt> result) {
        theTransactiondata = (ArrayList<RegisterReadingExt>) result;
        logger.info("RegisterReadingExt found =" + result.size());
        dataProvider.setList(result);
        setColumnSortHandler();
        dataProvider.refresh();
        clltblTransactions.setPageStart(0);
        if (result.isEmpty()) {
            Dialogs.displayInformationMessage(
                    MessagesUtil.getInstance().getMessage("register.reading.txn.noreadings"),
                    MediaResourceUtil.getInstance().getInformationIcon(),
                    viewBtn.getAbsoluteLeft() + viewBtn.getOffsetWidth(),
                    viewBtn.getAbsoluteTop() + viewBtn.getOffsetHeight(),
                    null);
        }
    }

    private void onClear() {
        dataProvider.setList(new ArrayList<RegisterReadingExt>());
        getInitStartDate();
        getInitEndDate();
    }

    public void setMeterData(MeterData meterData) {
        this.meterData = meterData;
    }

    public void setUsagePointData(UsagePointData usagePointData) {
        this.usagePointData = usagePointData;
    }

    public void setRegisterReadings() {

    }

    private void setColumnSortHandler() {
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<RegisterReadingExt>(dataProvider.getList());
            columnSortHandler.setComparator(readingTimeStampCol, new Comparator<RegisterReadingExt>() {
                public int compare(RegisterReadingExt o1, RegisterReadingExt o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getReadingTimestamp().compareTo(o2.getReadingTimestamp()) : 1;
                    }
                    return -1;
                }
            });
            clltblTransactions.addColumnSortHandler(columnSortHandler);
            // We know that the data is sorted alphabetically by default.
            columnSortList = clltblTransactions.getColumnSortList();
            columnSortList.push(readingTimeStampCol);
            ColumnSortEvent.fire(clltblTransactions, columnSortList);
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(clltblTransactions, columnSortList);
        }
        List<RegisterReadingExt> registerReadings = columnSortHandler.getList();
        if (!registerReadings.isEmpty()) {
            setTotalConsumption(registerReadings);
        }
    }

    private void setTotalConsumption(List<RegisterReadingExt> registerReadings) {

        BigDecimal total = BigDecimal.ZERO;

        if (isUsagePointTabSelected) {
            // registerReadings may be for multiple meters
            Map<String, ArrayList<RegisterReadingExt>> registerReadingsHashMap = new HashMap<>();
            String meterNum;
            ArrayList<RegisterReadingExt> meterRegReads;
            for (RegisterReadingExt regReadExt : registerReadings) {
                meterNum = regReadExt.getMeterNum();
                if(registerReadingsHashMap.containsKey(meterNum)){
                    registerReadingsHashMap.get(meterNum).add(regReadExt);
                } else {
                    meterRegReads = new ArrayList<>();
                    meterRegReads.add(regReadExt);
                    registerReadingsHashMap.put(meterNum, meterRegReads);
                }
            }

            for (String meterNumber : registerReadingsHashMap.keySet()) {
                meterRegReads = registerReadingsHashMap.get(meterNumber);
                total = total.add(getMeterTotalConsumption(meterRegReads.get(0).getReadingValue(), meterRegReads.get(meterRegReads.size() - 1).getReadingValue()));
            }

        } else {
            // One meter
            total = getMeterTotalConsumption(registerReadings.get(0).getReadingValue(), registerReadings.get(registerReadings.size() - 1).getReadingValue());
        }

        totalConsumptionHP.setVisible(true);
        totalConsumption.setText(FormatUtil.getInstance().formatDecimal(total));
    }

    private BigDecimal getMeterTotalConsumption (BigDecimal lastReading, BigDecimal firstReading){
        return lastReading.divide(new BigDecimal(1000)).subtract(firstReading.divide(new BigDecimal(1000)));
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrTransactions.firstPage();
    }

    @Override
    public boolean isValid(RegisterReadingExt value, String filter) {
        if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("register.reading.txn.channel"))) {
            return value.getChannelName().toLowerCase().contains(filter.toLowerCase());
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("register.reading.txn.determinant"))) {
            for (String detStr : value.getDeterminantNameList()) {
                if (detStr.toLowerCase().contains(filter.toLowerCase())) {
                    return true;
                }
            }
            return false;
        } else if (isUsagePointTabSelected && filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("register.reading.txn.meter"))) {
            return value.getMeterNum().toLowerCase().contains(filter.toLowerCase());
        } else {
            return false;
        }
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        txtbxfilter.setText("");
    }

}

