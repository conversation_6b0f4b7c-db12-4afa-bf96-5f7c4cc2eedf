package za.co.ipay.metermng.client.view.component.meter;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.Window.Location;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HasHorizontalAlignment.HorizontalAlignmentConstant;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextArea;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.dao.AbstractIdentifiable;
import za.co.ipay.email.shared.EmailAddress;
import za.co.ipay.email.shared.EmailBuilder;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.UsagePointRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.PaymentModeE;
import za.co.ipay.metermng.datatypes.ServiceResourceE;
import za.co.ipay.metermng.datatypes.StsEngineeringTokenTypeE;
import za.co.ipay.metermng.datatypes.TransItemTypeE;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.StsEngineeringToken;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.mybatis.generated.model.UnitsTrans;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.CustomerUsagePointMiscInfo;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.util.MeterMngSharedUtils;

/**
 * <AUTHOR> href="mailto:<EMAIL>">Patrick Munyiri</a>
 *         Created on 3/21/17.
 */
public class TokenReprintWidget extends BaseComponent {

    private static TokenReprintWidgetUiBinder uiBinder = GWT.create(TokenReprintWidgetUiBinder.class);
    private static Logger logger = Logger.getLogger(TokenReprintWidget.class.getName());

    @UiField TextBox reprintEmailMessageRecipient;
    @UiField TextBox reprintSmsMessageRecipient;
    @UiField Button btnSendEmail;
    @UiField Button btnSendSms;
    @UiField TextArea reprintEmailMessageBox;
    @UiField TextArea reprintSmsMessageBox;
    @UiField FormElement reprintEmailMessageElement;
    @UiField FormElement reprintSmsMessageElement;
    @UiField FormElement reprintEmailMessageRecipientElement;
    @UiField FormElement reprintSmsMessageRecipientElement;
    @UiField VerticalPanel pnlCreditToken;
    @UiField FormGroupPanel pnlVendKeyChange;
    @UiField Label lblWarningLine2;
    @UiField Label lblWarningLine4;
    @UiField Label lblWarningLine5;
    @UiField Label lblUtilName;
    @UiField Label lblUtilDistId;
    @UiField Label lblUtilVatNo;
    @UiField Label lblUtilAddress;
    @UiField Label lblIssued;
    @UiField Label lblReference;
    @UiField Label lblCustomerNames;
    @UiField Label lblCustomerAgreementRef;
    @UiField Label lblUsagePointName;
    @UiField Label lblAddress;
    @UiField Label lblCreditMeterNumber;
    @UiField VerticalPanel pnlStsMeterInfo;
    @UiField Label lblTokenTech;
    @UiField Label lblAlg;
    @UiField Label lblSgc;
    @UiField Label lblTi;
    @UiField Label lblKrn;
    @UiField Label lblTariff;
    @UiField Label lblYourResourceToken;
    @UiField VerticalPanel pnlStandardToken;
    @UiField Label lblStandardToken;
    @UiField Label lblStandardTokenReceiptNr;
    @UiField Label lblStandardTokenUnitsHeader;
    @UiField Label lblStandardTokenUnits;
    @UiField Label lblStandardTokenAmount;
    @UiField Label lblStandardTokenTax;
    @UiField Label lblTariffBreakdown;
    @UiField VerticalPanel pnlFreeBasicResource;
    @UiField Label lblFreeBasicResourceHeader;
    @UiField Label lblFreeBasicResourceToken;
    @UiField Label lblFreeBasicResourceReceiptNr;
    @UiField Label lblFreeBasicResourceDate;
    @UiField Label lblFreeBasicResourceUnitsHeader;
    @UiField Label lblFreeBasicResourceUnits;
    @UiField VerticalPanel pnlDeposit;
    @UiField VerticalPanel pnlRefund;
    @UiField VerticalPanel pnlDebtItems;
    @UiField VerticalPanel pnlFixedItems;
    @UiField Label lblTotalTax;
    @UiField Label lblTotalTaxIncl;
    @UiField VerticalPanel pnlEngineeringToken;
    @UiField Label lblUtilityName;
    @UiField Label lblUtilityAddress;
    @UiField Label lblTokenType;
    @UiField Label lblDate;
    @UiField Label lblUsagePoint;
    @UiField Label lblMeterNumber;
    @UiField HorizontalPanel pnlUnits;
    @UiField Label lblUnitsHeader;
    @UiField Label lblUnits;
    @UiField VerticalPanel pnlKeyChange;
    @UiField Label lblOldSupplyGroupCode;
    @UiField Label lblNewSupplyGroupCode;
    @UiField Label lblOldKeyRevisionNumber;
    @UiField Label lblNewKeyRevisionNumber;
    @UiField Label lblOldTariffIndex;
    @UiField Label lblNewTariffIndex;
    @UiField Label lblDescription;
    @UiField Label lblUser;
    @UiField VerticalPanel pnlKeyChangeTokens;
    @UiField Label lblTokenCode1Header;
    @UiField Label lblTokenCode1;
    @UiField Label lblTokenCode2Header;
    @UiField Label lblTokenCode2;
    @UiField Label lblTokenCode3Header;
    @UiField Label lblTokenCode3;
    @UiField Label lblTokenCode4Header;
    @UiField Label lblTokenCode4;
    @UiField Button btnPrint;
    @UiField Button btnSaveToPdf;
    @UiField Button btnClose;
    @UiField HorizontalPanel pnlUnitsBalance;
    @UiField Label lblUnitsBalanceHeader;
    @UiField Label lblUnitsBalance;
    @UiField VerticalPanel pnlStsEngMeterInfo;
    @UiField Label lblEngSgc;
    @UiField Label lblEngTi;
    @UiField Label lblEngKrn;


    private String defaultSenderEmailAddress;
    private String defaultSenderName;
    private String reprintTokenEmailSubject = MessagesUtil.getInstance()
            .getMessage("messaging.token.reprint.email.subject");

    private Map<String, String> tokenInfo;
    private StsEngineeringTokenData tokenData;
    private CustomerTrans customerTrans;
    private Long customerTransId;
    private DateTimeFormat dateTimeFormat;

    private PopupPanel reprintPopup;
    private PopupPanel transItemsWidgetPopup;
    private ArrayList<CustomerTransItemData> customerTransItemData = null;
    private MeterData meterData = null;
    private String resourceText = null;
    private String resourceUnit = null;
    private Long meterId = null;
    private String utilityName;
    private String utilityAddress;

    interface TokenReprintWidgetUiBinder extends UiBinder<Widget, TokenReprintWidget> {
    }

    public TokenReprintWidget(ClientFactory clientFactory, PopupPanel transItemsWidgetPopup,
            Map<String, String> tokenInfo, AbstractIdentifiable data,
            ArrayList<CustomerTransItemData> customerTransItemData) {
        this.clientFactory = clientFactory;
        this.transItemsWidgetPopup = transItemsWidgetPopup;
        this.tokenInfo = tokenInfo;
        if (data instanceof StsEngineeringTokenData) {
            tokenData = (StsEngineeringTokenData) data;
            meterId = tokenData.getMeterId();
        } else {
            customerTrans = (CustomerTrans) data;
            customerTransId = customerTrans.getId();
            meterId = customerTrans.getMeterId();
        }
        this.customerTransItemData = customerTransItemData;
        initWidget(uiBinder.createAndBindUi(this));

        final Format format = FormatUtil.getInstance();
        dateTimeFormat = DateTimeFormat.getFormat(format.getDateFormat() + " " + format.getTimeFormat());

        clientFactory.getAppSettingRpc().getAppSettingsByKeys(Arrays.asList(MeterMngStatics.APP_SETTING_FROM_EMAIL_KEY, MeterMngStatics.APP_SETTING_FROM_NAME_KEY,
                MeterMngStatics.APP_SETTING_REPRINT_UTILITY_NAME, MeterMngStatics.APP_SETTING_REPRINT_UTILITY_ADDRESS), new ClientCallback<Map<String, AppSetting>>() {
                    @Override
                    public void onSuccess(Map<String, AppSetting> result) {
                        defaultSenderEmailAddress = result.get(MeterMngStatics.APP_SETTING_FROM_EMAIL_KEY).getValue();
                        defaultSenderName = result.get(MeterMngStatics.APP_SETTING_FROM_NAME_KEY).getValue();
                        utilityName = result.get(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_NAME).getValue();
                        utilityAddress = result.get(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_ADDRESS).getValue();

                        TokenReprintWidget.this.clientFactory.getMeterRpc().getMeter(meterId, new ClientCallback<MeterData>() {
                            @Override
                            public void onSuccess(MeterData result) {
                                if (result != null) {
                                    meterData = result;
                                    switch (ServiceResourceE.fromId(result.getMeterModelData().getServiceResourceId())) {
                                    case ELEC:
                                        resourceText = "reprint.electricity";
                                        resourceUnit = "unit.kilowatthour.symbol";
                                        break;
                                    case GAS:
                                        resourceText = "reprint.gas";
                                        resourceUnit = "unit.cubicmeter.symbol";
                                        break;
                                    case WATER:
                                        resourceText = "reprint.water";
                                        resourceUnit = "unit.kiloliter.symbol";
                                        break;
                                    }
                                    Messages messagesInstance = MessagesUtil.getInstance();
                                    resourceText = messagesInstance.getMessage(resourceText);
                                    resourceUnit = messagesInstance.getMessage(resourceUnit);
                                    if (customerTrans != null && customerTrans.isHasEngineeringTokens()) {
                                        TokenReprintWidget.this.clientFactory.getMeterRpc().getKeyChangeTokensForCustomerTrans(customerTransId, new ClientCallback<StsEngineeringToken>() {
                                            @Override
                                            public void onSuccess(StsEngineeringToken stsEngineeringToken) {
                                                if (stsEngineeringToken != null) {
                                                    initReprintWidget(stsEngineeringToken);
                                                }
                                            }
                                        });
                                    } else {
                                        initReprintWidget(null);
                                    }
                                }
                            }
                        });
                        displayPopup();
                        reprintSmsMessageRecipient.getElement().setPropertyString("placeholder", format.getCellphonePlaceholder());
                    }
                });
    }

    private void displayPopup() {
        transItemsWidgetPopup.setAutoHideEnabled(false);
        reprintPopup = new PopupPanel(false);
        reprintPopup.setStylePrimaryName("transpopup");
        reprintPopup.setWidget(this);
        reprintPopup.setGlassEnabled(true);
        reprintPopup.center();
        reprintPopup.show();
    }

    private void initReprintWidget(StsEngineeringToken stsEngineeringToken) {
        btnSendEmail.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        sendReprintEmail();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        btnSendSms.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        sendReprintSms();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        final StringBuilder tokenDetails = new StringBuilder();
        String meterNumber = null;
        final Date transactionDate;

        Messages messagesInstance = MessagesUtil.getInstance();
        lblWarningLine4.setText(messagesInstance.getMessage("reprint.warning.line.4",
                new String[] { dateTimeFormat.format(new Date()) }));
        lblWarningLine5.setText(messagesInstance.getMessage("reprint.warning.line.5",
                new String[] { clientFactory.getUser().getUserName() }));
        Format format = FormatUtil.getInstance();
        if (customerTrans == null) {
            tokenDetails.append(prepareTokenInfo(tokenInfo));

            lblUtilityName.setText(utilityName);
            lblUtilityAddress.setText(utilityAddress);

            meterNumber = tokenData.getMeterNumber();
            transactionDate = tokenData.getTransDate();
            String tokenType = tokenData.getTokenTypeName();
            lblTokenType.setText(tokenType);
            lblDate.setText(dateTimeFormat.format(transactionDate));
            lblUsagePoint.setText(tokenData.getUsagePointName());
            lblMeterNumber.setText(meterNumber);
            lblDescription.setText(tokenData.getDescription());
            lblUser.setText(tokenData.getUserRecEntered());
            lblTokenCode1.setText(getFormattedToken(tokenData.getToken1()));
            String unitsHeader = null;
            switch (StsEngineeringTokenTypeE.fromName(tokenType)) {
            case FREE_ISSUE:
                unitsHeader = messagesInstance.getMessage("meter.units", new String[] { resourceUnit });
                populateStsFields(tokenData);
                break;
            case SET_PHASE:
                unitsHeader = messagesInstance.getMessage("meter.units",
                        new String[] { messagesInstance.getMessage("unit.watts.symbol") });
                populateStsFields(tokenData);
                break;
            case POWER_LIMIT:
                unitsHeader = messagesInstance.getMessage("meter.powerlimit.units.w");
                populateStsFields(tokenData);
                break;
            case KEY_CHANGE:
                populateKeyChangeFields(tokenData);
                break;
            case CLEAR_CREDIT:
            case CLEAR_TAMPER:
                populateStsFields(tokenData);
                break;
            default:
            }
            if (unitsHeader != null) {
                lblUnitsHeader.setText(unitsHeader + ":");
                String unitsAmount = tokenData.getUnits().toPlainString();
                lblUnits.setText(unitsAmount);
                pnlUnits.setVisible(true);
                tokenDetails.append("\n" + unitsHeader + ": " + unitsAmount + "\n");
            }
            pnlEngineeringToken.setVisible(true);
        } else {
            if (stsEngineeringToken != null) {
                pnlVendKeyChange.setVisible(true);
                lblTokenCode1.setText(getFormattedToken(stsEngineeringToken.getToken1()));
                pnlVendKeyChange.add(pnlKeyChangeTokens);
                pnlVendKeyChange.add(pnlKeyChange);
                populateKeyChangeFields(stsEngineeringToken);
            }

            lblUtilName.setText(utilityName);

            clientFactory.getAppSettingRpc().getAppSettingsByKeys(Arrays.asList(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_DIST_ID, MeterMngStatics.APP_SETTING_REPRINT_UTILITY_TAX_REF),
                    new ClientCallback<Map<String, AppSetting>>() {
                        @Override
                        public void onSuccess(Map<String, AppSetting> result) {
                            lblUtilDistId.setText(result.get(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_DIST_ID).getValue());
                            lblUtilVatNo.setText(result.get(MeterMngStatics.APP_SETTING_REPRINT_UTILITY_TAX_REF).getValue());
                        }
                    });

            lblUtilAddress.setText(utilityAddress);

            meterNumber = customerTrans.getMeterNumber();
            transactionDate = customerTrans.getTransDate();
            pnlCreditToken.setVisible(true);
            lblIssued.setText(dateTimeFormat.format(customerTrans.getTransDate()));
            lblReference.setText(customerTrans.getVendRefReceived());
            UsagePointRpcAsync usagePointRpc = clientFactory.getUsagePointRpc();
            usagePointRpc.getLatestCustomerUsagePointMiscInfoByUsagePointId(customerTrans.getUsagePointId(),
                    transactionDate, new ClientCallback<CustomerUsagePointMiscInfo>() {
                        @Override
                        public void onSuccess(CustomerUsagePointMiscInfo result) {
                            if (result != null) {
                                lblCustomerAgreementRef.setText(result.getAgreementRef());
                                lblUsagePointName.setText(result.getUsagePointName());
                                String firstnames = result.getFirstnames();
                                if (firstnames == null) {
                                    firstnames = "";
                                }
                                lblCustomerNames.setText(firstnames + " " + result.getSurname());
                                lblAddress.setText(getFormattedAddress(result));
                            }
                        }
                    });
            lblCreditMeterNumber.setText(meterNumber);
            clientFactory.getMeterRpc().getFirstEngineeringTokenByCustomerTrans(customerTransId,
                    new ClientCallback<StsEngineeringToken>() {
                        @Override
                        public void onSuccess(StsEngineeringToken result) {
                            if (result != null) {
                                lblSgc.setText(result.getNewSupGroup());
                                lblTi.setText(result.getNewTariffIdx());
                                lblKrn.setText(result.getNewKeyRev().toString());
                                pnlStsMeterInfo.setVisible(true);
                            }
                            if (meterData != null) {
                                StsMeter stsMeter = meterData.getStsMeter();
                                if (stsMeter != null) {
                                    lblTokenTech.setText(stsMeter.getStsTokenTechCode());
                                    lblAlg.setText(stsMeter.getStsAlgorithmCode());
                                    if (result == null) {
                                        lblSgc.setText(stsMeter.getStsCurrSupplyGroupCode());
                                        lblTi.setText(stsMeter.getStsCurrTariffIndex());
                                        lblKrn.setText(stsMeter.getStsCurrKeyRevisionNum().toString());
                                    }
                                    pnlStsMeterInfo.setVisible(true);
                                }
                            }
                        }
                    });
            clientFactory.getPricingStructureRpc().getPricingStructure(customerTrans.getPricingStructureId(), new ClientCallback<PricingStructure>() {
                @Override
                public void onSuccess(PricingStructure result) {
                    // Label is called tariff as that is what the
                    // vendRes element is called, but it maps to
                    // pricing structure name
                    lblTariff.setText(result.getName());
                }
            });
            for (CustomerTransItemData item : customerTransItemData) {
                switch (TransItemTypeE.fromId(item.getTransItemType())) {
                case stdToken:
                    String token = getFormattedToken(item.getToken());
                    lblStandardToken.setText(token);
                    lblStandardTokenReceiptNr.setText(item.getReceiptNum());
                    String unitsHeader = messagesInstance.getMessage("meter.units", new String[] { resourceUnit })
                            + ":";
                    lblStandardTokenUnitsHeader.setText(unitsHeader);
                    String unitsAmount = item.getUnits().toPlainString();
                    lblStandardTokenUnits.setText(unitsAmount);
                    if (stsEngineeringToken != null) {
                        String token1 = stsEngineeringToken.getToken1();
                        if (token1 != null) {
                            tokenDetails.append("\n" + messagesInstance.getMessage("reprint.key.change.notice.line.1")
                                    + " " + messagesInstance.getMessage("reprint.key.change.notice.line.2") + "\n\n"
                                    + messagesInstance.getMessage("meter.token.code1") + ": " + token1 + "\n");
                            String token2 = stsEngineeringToken.getToken2();
                            if (token2 != null) {
                                tokenDetails.append(
                                        messagesInstance.getMessage("meter.token.code2") + ": " + token2 + "\n");
                            }
                        }
                    }
                    tokenDetails.append("\n"
                            + messagesInstance.getMessage("reprint.your.resource.token", new String[] { resourceText })
                            + ": " + token + "\n\n" + unitsHeader + " " + unitsAmount + "\n");
                    if (PaymentModeE.fromId(customerTrans.getPaymentModeId()) == PaymentModeE.THIN_UNITS) {
                        pnlUnitsBalance.setVisible(true);
                        final String unitsBalanceHeader = messagesInstance.getMessage("unitsacc.balance.with.symbol",
                                new String[] { resourceUnit }) + ":";
                        lblUnitsBalanceHeader.setText(unitsBalanceHeader);
                        usagePointRpc.getUnitsTransactionFromCustomerTransId(customerTransId,
                                new ClientCallback<UnitsTrans>() {
                                    @Override
                                    public void onSuccess(UnitsTrans unitsTrans) {
                                        if (unitsTrans != null) {
                                            String unitsBalance = unitsTrans.getResultantBalance()
                                                    .setScale(1, RoundingMode.HALF_UP).toPlainString();
                                            lblUnitsBalance.setText(unitsBalance);
                                            tokenDetails.append("\n" + unitsBalanceHeader + " " + unitsBalance + "\n");
                                            populateMessageBoxes(tokenDetails);
                                        }
                                    }
                                });
                    }
                    BigDecimal amtTax = item.getAmtTax();
                    lblStandardTokenAmount.setText(format.formatCurrency(item.getAmtInclTax().subtract(amtTax), true));
                    lblStandardTokenTax.setText(format.formatCurrency(amtTax, true));
                    lblTariffBreakdown.setText(item.getTariff());
                    showYourResourceTokenLabel();
                    pnlStandardToken.setVisible(true);
                    break;
                case fixed:
                    handleAmountAndTaxPanelCreation(pnlFixedItems, item);
                    break;
                case bsstToken:
                case bsstRepeat:
                    lblFreeBasicResourceHeader.setText(
                            messagesInstance.getMessage("reprint.free.basic.resource", new String[] { resourceText }));
                    token = getFormattedToken(item.getToken());
                    lblFreeBasicResourceToken.setText(token);
                    lblFreeBasicResourceReceiptNr.setText(item.getReceiptNum());
                    lblFreeBasicResourceDate.setText(dateTimeFormat.format(item.getBsstDate()));
                    unitsHeader = messagesInstance.getMessage("meter.units", new String[] { resourceUnit }) + ":";
                    lblFreeBasicResourceUnitsHeader
                            .setText(messagesInstance.getMessage("meter.units", new String[] { resourceUnit }) + ":");
                    unitsAmount = item.getUnits().toPlainString();
                    lblFreeBasicResourceUnits.setText(item.getUnits().toPlainString());
                    showYourResourceTokenLabel();
                    pnlFreeBasicResource.setVisible(true);
                    tokenDetails.append("\n" + messagesInstance.getMessage("meter.online.bulk.free.issue.title") + ": "
                            + token + "\n\n" + unitsHeader + " " + unitsAmount + "\n");
                    break;
                case aux:
                    handleAmountAndTaxPanelCreation(pnlDebtItems, item);
                    break;
                case dep:
                    handleAmountAndTaxPanelCreation(pnlDeposit, item);
                    break;
                case refund:
                    handleAmountAndTaxPanelCreation(pnlRefund, item);
                    break;
                default:
                }
            }

            BigDecimal amtInclTax = customerTrans.getAmtInclTax();
            lblTotalTax.setText(format.formatCurrency(customerTrans.getAmtTax(), true));
            lblTotalTaxIncl.setText(format.formatCurrency(amtInclTax, true));
        }
        String date = format.formatDate(transactionDate);
        lblWarningLine2.setText(messagesInstance.getMessage("reprint.warning.line.2", new String[] { date }));
        if (meterNumber != null) {
            tokenDetails
                    .append("\n" + messagesInstance.getMessage("messaging.txn.meter_no") + ": " + meterNumber + "\n");
        }
        if (transactionDate != null) {
            tokenDetails.append("\n" + messagesInstance.getMessage("messaging.txn.date") + ": " + date + "\n");
        }
        populateMessageBoxes(tokenDetails);
    }

    private void showYourResourceTokenLabel() {
        lblYourResourceToken.setText(
                MessagesUtil.getInstance().getMessage("reprint.your.resource.token", new String[] { resourceText }));
        lblYourResourceToken.setVisible(true);
    }

    private void populateKeyChangeFields(StsEngineeringToken stsEngineeringToken) {
        pnlKeyChange.setVisible(true);
        lblOldSupplyGroupCode.setText(stsEngineeringToken.getOldSupGroup());
        lblNewSupplyGroupCode.setText(stsEngineeringToken.getNewSupGroup());
        lblOldKeyRevisionNumber.setText(stsEngineeringToken.getOldKeyRev().toString());
        lblNewKeyRevisionNumber.setText(stsEngineeringToken.getNewKeyRev().toString());
        lblOldTariffIndex.setText(stsEngineeringToken.getOldTariffIdx());
        lblNewTariffIndex.setText(stsEngineeringToken.getNewTariffIdx());
        Messages messagesInstance = MessagesUtil.getInstance();
        lblTokenCode1Header.setText(messagesInstance.getMessage("meter.token.code1") + ":");
        lblTokenCode2Header.setText(messagesInstance.getMessage("meter.token.code2") + ":");
        lblTokenCode2.setText(getFormattedToken(stsEngineeringToken.getToken2()));
        lblTokenCode2Header.setVisible(true);
        lblTokenCode2.setVisible(true);
        if (stsEngineeringToken.getToken3() != null && !stsEngineeringToken.getToken3().isEmpty()) {
            lblTokenCode3Header.setText(messagesInstance.getMessage("meter.token.code3") + ":");
            lblTokenCode3.setText(getFormattedToken(stsEngineeringToken.getToken3()));
            lblTokenCode3Header.setVisible(true);
            lblTokenCode3.setVisible(true);
        }
        if (stsEngineeringToken.getToken4() != null && !stsEngineeringToken.getToken4().isEmpty()) {
            lblTokenCode4Header.setText(messagesInstance.getMessage("meter.token.code4") + ":");
            lblTokenCode4.setText(getFormattedToken(stsEngineeringToken.getToken4()));
            lblTokenCode4Header.setVisible(true);
            lblTokenCode4.setVisible(true);
        }
    }

    private void populateStsFields(StsEngineeringToken stsEngineeringToken) {
        lblEngSgc.setText(stsEngineeringToken.getNewSupGroup());
        lblEngTi.setText(stsEngineeringToken.getNewTariffIdx());
        lblEngKrn.setText(stsEngineeringToken.getNewKeyRev().toString());

        pnlStsEngMeterInfo.setVisible(true);
    }

    private void populateMessageBoxes(StringBuilder messageDetails) {
        String messageDetailsString = messageDetails.toString();
        reprintSmsMessageBox.setText(messageDetailsString.trim());
        reprintEmailMessageBox.setText(MessagesUtil.getInstance().getMessage("reprint.default.email.message",
                new String[] { messageDetailsString, defaultSenderName}));
    }

    private void handleAmountAndTaxPanelCreation(VerticalPanel pnlItems, CustomerTransItemData item) {
        Label lblHeader = createLabel(item.getToken(), HasHorizontalAlignment.ALIGN_CENTER);
        lblHeader.setStyleName("{style.bold}");
        pnlItems.add(lblHeader);
        FormGroupPanel pnlGroup = new FormGroupPanel();
        if (item.getDescription() != null && !(item.getDescription().trim().isEmpty())) {
            pnlGroup.add(createHorizontalPanel("reprint.desc", item.getDescription()));
        }
        if (item.getReceiptNum() != null && !(item.getReceiptNum().trim().isEmpty())) {
            pnlGroup.add(createHorizontalPanel("reprint.receipt.nr", item.getReceiptNum()));
        }
        BigDecimal amtTax = item.getAmtTax();
        pnlGroup.add(createHorizontalPanel("usagepoint.txn.amt", item.getAmtInclTax().subtract(amtTax)));
        pnlGroup.add(createHorizontalPanel("customer.txn.tax", amtTax));
        if (item.getRemBalance() != null) {
            pnlGroup.add(createHorizontalPanel("reprint.remaining.balance", item.getRemBalance()));
        }
        pnlItems.add(pnlGroup);
        pnlItems.setVisible(true);
    }

    private HorizontalPanel createHorizontalPanel(String name, BigDecimal value) {
        HorizontalPanel pnlItem = new HorizontalPanel();
        pnlItem.setWidth("300px");
        pnlItem.add(createLabel(MessagesUtil.getInstance().getMessage(name) + ":", HasHorizontalAlignment.ALIGN_LEFT));
        pnlItem.add(
                createLabel(FormatUtil.getInstance().formatCurrency(value, true), HasHorizontalAlignment.ALIGN_RIGHT));
        return pnlItem;
    }

    private HorizontalPanel createHorizontalPanel(String name, String value) {
        HorizontalPanel pnlItem = new HorizontalPanel();
        pnlItem.setWidth("300px");
        pnlItem.add(createLabel(MessagesUtil.getInstance().getMessage(name) + ":", HasHorizontalAlignment.ALIGN_LEFT));
        pnlItem.add(createLabel(value, HasHorizontalAlignment.ALIGN_RIGHT));
        return pnlItem;
    }

    private Label createLabel(String text, HorizontalAlignmentConstant align) {
        Label lblItem = new Label(text);
        lblItem.setHorizontalAlignment(align);
        return lblItem;
    }

    private void sendReprintEmail() {
        if (isValidEmailInfo()) {
            String message = reprintEmailMessageBox.getText().trim();
            for (String recipient : reprintEmailMessageRecipient.getText().split(",|;")) {
                if (!recipient.isEmpty()) {
                    final EmailBuilder emailBuilder = new EmailBuilder()
                            .setFrom(EmailAddress.create(defaultSenderName, defaultSenderEmailAddress))
                            .setSubject(reprintTokenEmailSubject).setTo(EmailAddress.create(null, recipient))
                            .setMessage(message);
                    btnSendEmail.setEnabled(false);
                    // Send Email
                    clientFactory.getNotificationRpcAsync().sendEmail(emailBuilder, new ClientCallback<Void>() {
                        @Override
                        public void onSuccess(Void result) {
                            // Show that message has been sent
                            Dialogs.displayInformationMessage(
                                    MessagesUtil.getInstance().getMessage("notification.message.send.status.email"),
                                    MediaResourceUtil.getInstance().getInformationIcon());
                            btnSendEmail.setEnabled(true);
                        }

                        @Override
                        public void onFailure(Throwable caught) {
                            logger.log(Level.WARNING,
                                    "Could not send email. [Subject: " + emailBuilder.getSubject() + "].");
                            super.onFailure(caught);
                            btnSendEmail.setEnabled(true);
                        }
                    });
                }
            }
        }
    }

    private void sendReprintSms() {
        if (isValidSmsInfo()) {
            final String message = reprintSmsMessageBox.getText().trim();
            for (String recipient : reprintSmsMessageRecipient.getText().split(",|;")) {
                if (!recipient.isEmpty()) {
                    btnSendSms.setEnabled(false);
                    // Send SMS
                    clientFactory.getNotificationRpcAsync().sendSms(message, recipient, new ClientCallback<Void>() {
                        @Override
                        public void onSuccess(Void result) {
                            // Message sent
                            Dialogs.displayInformationMessage(
                                    MessagesUtil.getInstance().getMessage("notification.message.send.status.sms"),
                                    MediaResourceUtil.getInstance().getInformationIcon());
                            btnSendSms.setEnabled(true);
                        }

                        @Override
                        public void onFailure(Throwable caught) {
                            logger.log(Level.WARNING, "Could not send SMS. [Message: " + message + "]");
                            super.onFailure(caught);
                            btnSendSms.setEnabled(true);
                        }
                    });
                }
            }
        }
    }

    private String prepareTokenInfo(Map<String, String> tokenInfo) {

        StringBuilder messageBuilder = new StringBuilder("\n");
        boolean isSingleToken = tokenInfo.size() == 1;
        String token;
        for (Map.Entry<String, String> entry : tokenInfo.entrySet()) {
            // for engineeringTokens ALWAYS display the token type from getValue()
            messageBuilder.append((isSingleToken && tokenData == null)
                            ? MessagesUtil.getInstance().getMessage("messaging.txn.token")
                            : entry.getValue());
            token = getFormattedToken(entry.getKey().split("\\^")[0].trim());//added ^count to each token so if token numbers are the same it will be 2 separate keys
            messageBuilder.append(": ").append(token).append("\n");
        }

        return messageBuilder.toString();
    }

    private String getFormattedToken(String key) {
        int keyLength = key.length();
        int a = keyLength / 4;
        String token = "";
        for (int i = 0; i < a; i++) {
            token += key.substring(i * 4, (i + 1) * 4).concat(" ");
        }
        return token + key.substring(a * 4, keyLength);
    }

    private boolean isValidEmailInfo() {
        reprintEmailMessageRecipientElement.clearErrorMsg();
        reprintEmailMessageElement.clearErrorMsg();
        String emailRecipientsText = reprintEmailMessageRecipient.getText();
        boolean valid = true;
        if (ValidateUtil.isNotNullOrBlank(emailRecipientsText)) {
            boolean hasValidRecipient = false;
            for (String recipient : emailRecipientsText.split(",|;")) {
                if (!ValidateUtil.isValidEmail(recipient)) {
                    reprintEmailMessageRecipientElement
                            .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.validity.email"));
                    valid = false;
                } else {
                    hasValidRecipient = true;
                }
            }
            if (!hasValidRecipient) {
                reprintEmailMessageRecipientElement
                        .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.validity.email"));
                valid = false;
            }
        } else {
            reprintEmailMessageRecipientElement
                    .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required.recipient.email"));
            valid = false;
        }
        if (reprintEmailMessageBox.getValue().trim().isEmpty()) {
            reprintEmailMessageElement
                    .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.validity.message.content"));
            valid = false;
        }
        return valid;
    }

    private boolean isValidSmsInfo() {
        reprintSmsMessageRecipientElement.clearErrorMsg();
        reprintSmsMessageElement.clearErrorMsg();
        String smsRecipientsText = reprintSmsMessageRecipient.getText();
        boolean valid = true;
        if (ValidateUtil.isNotNullOrBlank(smsRecipientsText)) {
            boolean hasValidRecipient = false;
            for (String recipient : smsRecipientsText.split(",|;")) {
                if (!MeterMngSharedUtils.isValidCellPhone(recipient.trim())) {
                    String errorMsgKey = "cellPhone.pattern.description";
                    if (ValidateUtil.TELEPHONE_NUMBER
                            .equals(FormatUtil.getInstance().getCellRegexPattern().getSource())) {
                        errorMsgKey = "error.field.validity.phone";
                    }
                    reprintSmsMessageRecipientElement.setErrorMsg(MessagesUtil.getInstance().getMessage(errorMsgKey));
                    valid = false;
                } else {
                    hasValidRecipient = true;
                }
            }
            if (!hasValidRecipient) {
                reprintSmsMessageRecipientElement
                        .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.validity.phone"));
                valid = false;
            }
        } else {
            reprintSmsMessageRecipientElement
                    .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required.recipient.phone"));
            valid = false;
        }
        if (reprintSmsMessageBox.getValue().trim().isEmpty()) {
            reprintSmsMessageElement
                    .setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.validity.message.content"));
            valid = false;
        }
        return valid;
    }

    private String getFormattedAddress(CustomerUsagePointMiscInfo customerUsagePointMiscInfo) {
        String addressLine1 = customerUsagePointMiscInfo.getAddressLine1();
        String addressLine2 = customerUsagePointMiscInfo.getAddressLine2();
        String addressLine3 = customerUsagePointMiscInfo.getAddressLine3();
        StringBuilder sb = new StringBuilder();
        if (addressLine1 != null && !addressLine1.isEmpty()) {
            sb.append(addressLine1).append(", ");
        }
        if (addressLine2 != null && !addressLine2.isEmpty()) {
            sb.append(addressLine2).append(", ");
        }
        if (addressLine3 != null && !addressLine3.isEmpty()) {
            sb.append(addressLine3).append(", ");
        }

        if (sb.length() > 2) {
            return sb.substring(0, sb.length() - 2);
        }
        return "";
    }

    @UiHandler("btnPrint")
    void handlePrintReceipt(ClickEvent e) {
        generatePdf("print");
    }

    @UiHandler("btnSaveToPdf")
    void handleSaveReceiptPdf(ClickEvent e) {
        generatePdf("save");
    }

    private void generatePdf(String action) {
        GetRequestBuilder url = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                .withTargetUrl("reprintreceiptpdf")
                .addParam("action", action)
                .addParam("meterid", meterId.toString())
                .addParam("usingaccessgroups", "" + clientFactory.isEnableAccessGroups())
                .addParam("locale", clientFactory.getLocaleName());
        if (customerTransId == null) {
            url.addParam("stsengineeringtokenid", tokenData.getId().toString());
        } else {
            url.addParam("customertransid", customerTransId.toString());
        }
        if ("print".equals(action)) {
            Window.open(url.toEncodedUrl(), "_blank", "");
        } else {
            Location.assign(url.toEncodedUrl());
        }
    }

    @UiHandler("btnClose")
    void handleClose(ClickEvent e) {
        transItemsWidgetPopup.setAutoHideEnabled(true);
        reprintPopup.hide();
    }
}
