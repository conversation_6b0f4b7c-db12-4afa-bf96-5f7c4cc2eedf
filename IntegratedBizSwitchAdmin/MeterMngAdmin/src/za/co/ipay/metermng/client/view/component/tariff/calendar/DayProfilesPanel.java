package za.co.ipay.metermng.client.view.component.tariff.calendar;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.metermng.client.event.DayProfileUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouDayProfileData;
import za.co.ipay.metermng.shared.TouSpecialDayData;

import com.google.gwt.cell.client.ImageResourceCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.resources.client.ImageResource;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

public class DayProfilesPanel extends BaseComponent {

    @UiField(provided=true) CellTable<TouDayProfileData> dayProfilesTable;
    @UiField TablePager daysPager;

    @UiField FlowPanel formPanel;
    //day profiles
    @UiField TextBox nameTextBox;
    @UiField TextBox codeTextBox;
    @UiField FormElement dayProfileNameElement;
    @UiField FormElement dayProfileCodeElement;

    @UiField HorizontalPanel buttons;
    @UiField Button btnCancel;
    @UiField Button btnSave;
    @UiField Button btnDelete;

    @UiField(provided=true) DayProfilesTimePanel timesInDayProfilePanel;
    @UiField FlowPanel timesPanel;

    private ListDataProvider<TouDayProfileData> dataProvider;
    private SingleSelectionModel<TouDayProfileData> selectionModel;
    private TextColumn<TouDayProfileData> dayProfileName;
    private TextColumn<TouDayProfileData> dayProfileCode;
    private Column<TouDayProfileData, ImageResource> dayProfileComplete;

    private TouCalendarData calendarData = new TouCalendarData();
    private TouDayProfileData dayProfile = new TouDayProfileData();

    private ArrayList<TouDayProfileData> daysList;

    protected HasDirtyData hasDirtyData;
    protected boolean readOnly = false;

    private static Logger logger = Logger.getLogger(DayProfilesPanel.class.getName());
    private CalendarContainer parentContainer;

    private static PeriodsInDayUiBinder uiBinder = GWT.create(PeriodsInDayUiBinder.class);

    interface PeriodsInDayUiBinder extends UiBinder<Widget, DayProfilesPanel> {
    }

    public DayProfilesPanel(ClientFactory clientFactory, CalendarContainer parentContainer) {
        this.clientFactory = clientFactory;
        this.parentContainer = parentContainer;
        createTable();
        timesInDayProfilePanel = new DayProfilesTimePanel(clientFactory, parentContainer);
        initWidget(uiBinder.createAndBindUi(this));
        this.hasDirtyData = ((Workspace) parentContainer).createAndRegisterHasDirtyData();
        initDayProfilesTable();
        addFieldHandlers();
    }

    public void setTouCalendarData(TouCalendarData calendarData) {
        this.calendarData = calendarData;
        refreshTable(calendarData);
    }

    protected void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            dayProfilesTable = new CellTable <TouDayProfileData>(15, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            dayProfilesTable = new CellTable <TouDayProfileData>(15);
        }
    }

    protected void initDayProfilesTable() {
        if (dataProvider == null) {
            dayProfileName = new TextColumn<TouDayProfileData>() {
                @Override
                public String getValue(TouDayProfileData data) {
                    if (data.getName() != null) {
                        return data.getName();
                    }
                    return " ? ";
                }
            };
            dayProfileName.setSortable(true);

            dayProfileCode = new TextColumn<TouDayProfileData>() {
                @Override
                public String getValue(TouDayProfileData data) {
                    if (data.getCode() != null) {
                        return data.getCode();
                    }
                    return " ? ";
                }
            };
            dayProfileCode.setSortable(true);

            dayProfileComplete = new Column<TouDayProfileData, ImageResource>(new ImageResourceCell()) {
                @Override
                public ImageResource getValue(TouDayProfileData data) {
                    if (data.isDayProfileComplete()) {
                        return MediaResourceUtil.getInstance().getTickImage();
                    }
                    return MediaResourceUtil.getInstance().getTransparentImage();
                }
            };

            // Add the columns.
            dayProfilesTable.addColumn(dayProfileName, MessagesUtil.getInstance().getMessage("calendar.dayprofile.field.name"));
            dayProfilesTable.addColumn(dayProfileCode, MessagesUtil.getInstance().getMessage("calendar.dayprofile.field.code"));
            dayProfilesTable.addColumn(dayProfileComplete,  MessagesUtil.getInstance().getMessage("calendar.complete"));
            dayProfilesTable.getColumn(2).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);

            dataProvider = new ListDataProvider<TouDayProfileData>();
            dataProvider.addDataDisplay(dayProfilesTable);
            daysPager.setDisplay(dayProfilesTable);
            dayProfilesTable.setPageSize(getPageSize());

            logger.info("Created Day Profile table");
        }

        selectionModel = new SingleSelectionModel<TouDayProfileData>();
        CellPreviewEvent.Handler<TouDayProfileData> handler = new CellPreviewEvent.Handler<TouDayProfileData>() {
            final CellPreviewEvent.Handler<TouDayProfileData> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<TouDayProfileData> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (hasDirtyData.isDirtyData()) {
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    hasDirtyData.setDirtyData(false);
                                                    event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                    logger.info("Discarding changes for selection event");
                                                } else {
                                                    event.setCanceled(true);
                                                    logger.info("Cancelled selection event, staying with current selection");
                                                }

                                            }});
                            } else {
                                selectionEventManager.onCellPreview(event);
                            }

                        }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }
        };
        dayProfilesTable.setSelectionModel(selectionModel, handler);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final TouDayProfileData selected = selectionModel.getSelectedObject();
                if (selected != null) {
                    setDayProfile(selected);
                    dayProfilesTable.getSelectionModel().setSelected(selected, true);
                }
            }
        });
    }

    public void clearTableSelection() {
        TouDayProfileData selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    public void refreshTable() {
        if (calendarData != null && calendarData.getId() != null) {
            clientFactory.getCalendarRpc().getDayProfilesByCalendar(calendarData.getId(), new ClientCallback<ArrayList<TouDayProfileData>>() {
                @Override
                public void onSuccess(ArrayList<TouDayProfileData> result) {
                    calendarData.setAssignedDayProfiles(result);
                    refreshTable(calendarData);
                }
            });
        } else {
            refreshTable(null);
        }
    }

    public void refreshTable(TouCalendarData calendarData) {
        if (calendarData != null && calendarData.getId() != null) {
            ArrayList<TouDayProfileData> result = calendarData.getAssignedDayProfileList();
            logger.info("Got seasons: " + result.size());
            if (dataProvider != null && dataProvider.getList() != null) {
                dataProvider.getList().clear();
                dataProvider.getList().addAll(result);
                setDayProfilesList(result);
                parentContainer.setSetupDayProfilesComplete(isDayProfileTimesComplete());
                if (dayProfile != null) {
                    selectionModel.setSelected(dayProfile, true);
                }
            }
        } else {
            setDayProfile(null);
            if (dataProvider.getList() != null) {
                dataProvider.getList().clear();
            }
            if (daysList != null) {
                daysList.clear();
            }
            clearFields();
        }
        dayProfilesTable.setPageStart(0);
    }

    public void setDayProfilesList(ArrayList<TouDayProfileData> thedata) {
        logger.info("Displaying day profiles: "+thedata.size());
        daysList = thedata;

        calendarData.setAssignedDayProfiles(daysList);
        dataProvider.getList().clear();
        dataProvider.getList().addAll(thedata);
        dataProvider.refresh();

        ListHandler<TouDayProfileData> columnSortHandler = new ListHandler<TouDayProfileData>(dataProvider.getList());
        columnSortHandler.setComparator(dayProfileName, new Comparator<TouDayProfileData>() {
            public int compare(TouDayProfileData o1, TouDayProfileData o2) {
                if (o1 == o2) {
                    return 0;
                }
                if (o1 != null && o1.getName() != null) {
                    return (o2 != null && o2.getName() != null) ? o1.getName().compareTo(o2.getName()) : 1;
                }
                return -1;
            }
        });
        columnSortHandler.setComparator(dayProfileCode, new Comparator<TouDayProfileData>() {
            public int compare(TouDayProfileData o1, TouDayProfileData o2) {
                if (o1 == o2) {
                    return 0;
                }
                if (o1 != null && o1.getCode() != null) {
                    return (o2 != null && o2.getCode() != null) ? o1.getCode().compareTo(o2.getCode()) : 1;
                }
                return -1;
            }
        });
        dayProfilesTable.addColumnSortHandler(columnSortHandler);
        dayProfilesTable.getColumnSortList().push(dayProfileName);
        ColumnSortEvent.fire(dayProfilesTable, dayProfilesTable.getColumnSortList());
    }

    public void setDayProfile(TouDayProfileData theprofile) {
        this.dayProfile = theprofile;
        if (dayProfile == null) {
            dayProfile = new TouDayProfileData();
            clearTableSelection();
        }
        mapDataToForm();
    }


    protected void mapDataToForm() {
        clearErrorMessages();
        clearFields();
        if (dayProfile != null && dayProfile.getId() != null) {
            this.nameTextBox.setText(dayProfile.getName());
            this.codeTextBox.setText(dayProfile.getCode());
            this.timesInDayProfilePanel.setTouDayProfileData(dayProfile);
            timesPanel.setVisible(true);
            btnDelete.setVisible(!readOnly);
        } else {
            timesPanel.setVisible(false);
            btnDelete.setVisible(false);
        }
    }

    public void clearFields() {
        this.nameTextBox.setText("");
        this.codeTextBox.setText("");
    }

    @UiHandler("btnSave")
    void handleSaveButton(ClickEvent event) {
        if (readOnly) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.readOnly", new String[] {parentContainer.getPricingStructureNames()}), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft()+btnSave.getOffsetWidth(), btnSave.getAbsoluteTop()-btnSave.getOffsetHeight(), null);
            return;
        }
        // Map form fields to data object
        clearErrorMessages();
        mapFormToData();
        if (validate()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (clientFactory != null) {
                        clientFactory.getCalendarRpc().updateDayProfiles(dayProfile,  new  ClientCallback<TouDayProfileData>() {

                            @Override
                            public void onSuccess(TouDayProfileData result) {
                                hasDirtyData.setDirtyData(false);
                                setDayProfile(result);
                                refreshTable();
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance().getMessage("calendar.dayprofile.saved"),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(),null);
                                DayProfileUpdatedEvent dayProfileUpdatedEvent = new DayProfileUpdatedEvent();
                                dayProfileUpdatedEvent.setDayProfileId(result.getId());
                                clientFactory.getEventBus().fireEvent(dayProfileUpdatedEvent);
                            }
                        });
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @UiHandler("btnCancel")
    void handleClearButton(ClickEvent event) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        setDayProfile(null);
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancel.getAbsoluteLeft()+btnCancel.getOffsetWidth(), btnCancel.getAbsoluteTop()-btnCancel.getOffsetHeight(), null);
                    }
                }});
        } else {
            setDayProfile(null);
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancel.getAbsoluteLeft()+btnCancel.getOffsetWidth(), btnCancel.getAbsoluteTop()-btnCancel.getOffsetHeight(), null);
        }
    }

    @UiHandler("btnDelete")
    void handleDeleteButton(ClickEvent event) {
        //ToDo double check with popup

        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getCalendarRpc().getCalendarSeasons(calendarData.getId(), new  ClientCallback<ArrayList<TouCalendarSeason>>() {

                    @Override
                    public void onSuccess(ArrayList<TouCalendarSeason> result) {
                        boolean dayprofileassigned = false;
                        for (TouCalendarSeason tcs : result) {
                            if ( (tcs.getTouDayProfileId1() != null && tcs.getTouDayProfileId1().equals(dayProfile.getId()) )
                                    || (tcs.getTouDayProfileId2() != null && tcs.getTouDayProfileId2().equals(dayProfile.getId()))
                                    || (tcs.getTouDayProfileId3() != null && tcs.getTouDayProfileId3().equals(dayProfile.getId()))
                                    || (tcs.getTouDayProfileId4() != null && tcs.getTouDayProfileId4().equals(dayProfile.getId()))
                                    || (tcs.getTouDayProfileId5() != null && tcs.getTouDayProfileId5().equals(dayProfile.getId()))
                                    || (tcs.getTouDayProfileId6() != null && tcs.getTouDayProfileId6().equals(dayProfile.getId()))
                                    || (tcs.getTouDayProfileId7() != null && tcs.getTouDayProfileId7().equals(dayProfile.getId()))
                            ){
                                dayprofileassigned = true;
                                break;
                            }
                        }
                        if (dayprofileassigned) {
                            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.error.first.unassign"), MediaResourceUtil.getInstance().getErrorIcon(), btnDelete.getAbsoluteLeft()+btnDelete.getOffsetWidth(), btnDelete.getAbsoluteTop()-btnDelete.getOffsetHeight(), null);
                        } else {
                            checkDayProfileSpecialDay();
                        }
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void checkDayProfileSpecialDay() {
        final int aleft = btnDelete.getAbsoluteLeft();
        final int atop = btnDelete.getAbsoluteTop();

        //check if dayProfile is in use in special days then also cannot delete yet

        clientFactory.getCalendarRpc().getSpecialDays(calendarData.getId(), new ClientCallback<ArrayList<TouSpecialDayData>>() {
            @Override
            public void onSuccess(ArrayList<TouSpecialDayData> result) {
                boolean dayProfileSpecialDay = false;

                for (TouSpecialDayData tsdd : result) {
                    if (tsdd.getTouDayProfileId().equals(dayProfile.getId())) {
                        dayProfileSpecialDay = true;
                        break;
                    }
                }

                if (dayProfileSpecialDay) {
                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.error.special.day.first.unassign"),
                            MediaResourceUtil.getInstance().getErrorIcon(),
                            btnDelete.getAbsoluteLeft()+btnDelete.getOffsetWidth(),
                            btnDelete.getAbsoluteTop()-btnDelete.getOffsetHeight(),
                            null);
                } else {
                    Dialogs.confirm(
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("dayprofile.question.delete"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                            ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                            new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        hasDirtyData.setDirtyData(false);

                                        clientFactory.getCalendarRpc().deleteDayProfile(dayProfile, new ClientCallback<Boolean>(aleft, atop) {
                                            @Override
                                            public void onSuccess(Boolean result) {
                                                if (result) {
                                                    setDayProfile(null);
                                                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.deleted"), MediaResourceUtil.getInstance().getInformationIcon(), aleft, atop, MessagesUtil.getInstance().getMessage("button.close"));
                                                    refreshTable();
                                                    clientFactory.getEventBus().fireEvent(new DayProfileUpdatedEvent());
                                                }
                                            }
                                        });
                                    }
                                }
                            }, aleft, atop );
                }   //end else if (dayProfileSpecialDay)
            }  // end onSuccess
        });
    }

    private void mapFormToData() {
        if (dayProfile==null) {
            dayProfile = new TouDayProfileData();
        }
        dayProfile.setName(nameTextBox.getText());
        dayProfile.setCode(codeTextBox.getText());
        dayProfile.setTouCalendarId(calendarData.getId());

    }

    private boolean validate() {
        boolean validated = true;
        if (!ClientValidatorUtil.getInstance().validateField(dayProfile, "name", dayProfileNameElement)) {
            validated = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(dayProfile, "code", dayProfileCodeElement)) {
            validated = false;
        }
        return validated;
    }


    public void clearErrorMessages() {
        dayProfileNameElement.clearErrorMsg();
        dayProfileCodeElement.clearErrorMsg();
    }

    public boolean isDayProfileTimesComplete() {
        boolean cmplte = true;
        if (daysList.isEmpty()) {
            return false;
        }
        for (TouDayProfileData dp: daysList) {
            if (!dp.isDayProfileComplete()) {
                cmplte = false;
                break;
            }
        }
        return cmplte;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
        formPanel.setVisible(!readOnly);
        buttons.setVisible(!readOnly);
        timesInDayProfilePanel.setReadOnly(readOnly);
    }

    protected void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        codeTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

}
