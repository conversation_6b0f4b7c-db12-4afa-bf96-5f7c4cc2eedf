package za.co.ipay.metermng.client.view.component.tariff;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.i18n.client.TimeZone;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.PricingStructure;
import za.co.ipay.metermng.mybatis.generated.model.Tariff;
import za.co.ipay.metermng.mybatis.generated.model.TariffClass;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;
import za.co.ipay.metermng.shared.tariff.TariffWithData;

public class TariffPanel extends SimpleFormPanel {

    @UiField FormElement nameElement;
    @UiField FormElement descElement;
    @UiField FormElement typeElement;
    @UiField FormElement startDateElement;

    @UiField TextBox nameTextBox;
    @UiField TextBox descTextBox;
    @UiField ListBox typeListBox;
    @UiField DateBox startDateBox;

    @UiField FlowPanel pnlTariffType;
    @UiField Label readOnlyLabel;

    private TariffWithData tariffWithData;
    private ITariffUIClass tariffUIClass;
    private List<TariffClass> tariffClassList;
    private ClientFactory clientFactory;
    private PricingStructure pricingStructure;

    private static Logger logger = Logger.getLogger(TariffPanel.class.getName());
    private static TariffPanelUiBinder uiBinder = GWT.create(TariffPanelUiBinder.class);

    interface TariffPanelUiBinder extends UiBinder<Widget, TariffPanel> {
    }

    public TariffPanel(SimpleForm form, ClientFactory clientFactory) {
        super(form);
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
        addFieldHandlers();
    }

    public void initData(PricingStructure pricingStructure, List<TariffClass> tariffClassList) {
        this.pricingStructure = pricingStructure;
        this.tariffClassList = tariffClassList;
        typeListBox.clear();
        if (tariffClassList.size() > 0) {
            Collections.sort(tariffClassList, new Comparator<TariffClass>() {
                @Override
                public int compare(TariffClass o1, TariffClass o2) {
                    return o1.getId().compareTo(o2.getId());
                }
            });
            populateTariffTypeItems();
            applyTariffSelection();
        } else {
            removeTariffForm();
        }
    }

    private void initUi() {
        startDateBox.setFormat(
                new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat())));
    }

    @UiHandler("clearBtn")
    public void onClickClearButton(ClickEvent e) {
        if(tariffUIClass != null) {
            tariffUIClass.clearForm();
        }
    }

    @Override
    public void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        descTextBox.addChangeHandler(new FormDataChangeHandler(form));
        typeListBox.addChangeHandler(new FormDataChangeHandler(form));
        typeListBox.addChangeHandler(new ChangeHandler() {
            @Override
            public void onChange(ChangeEvent event) {
                applyTariffSelection();
            }
        });
        startDateBox.addValueChangeHandler(new ValueChangeHandler<Date>() {
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                form.setDirtyData(true);
                startDateElement.clearErrorMsg();
            }
        });

        //put a seperate valuechangeHandler on the DATEPICKER - will always add now time.
        //But when change time manually on startDatebox, this changehandler is not triggered, so user can change time only
        startDateBox.getDatePicker().addValueChangeHandler(new ValueChangeHandler<Date>() {
            @SuppressWarnings("deprecation")
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                Date now = new Date();
                Date selectedDate = event.getValue();
                //if chosen date is today; set the time to now time
                //else choosing a date in the future; leave the time as 00:00:00.0
                //NOTE NOTE GWT doesn't use Calendar, so must use deprecated methods here to set time
                if (selectedDate.getDay() == now.getDay()
                        && selectedDate.getMonth() == now.getMonth()
                        && selectedDate.getYear() == now.getYear()) {
                    selectedDate.setHours(now.getHours());
                    selectedDate.setMinutes(now.getMinutes());
                    selectedDate.setSeconds(now.getSeconds());
                    startDateBox.setValue(selectedDate);
                    startDateElement.clearErrorMsg();
                }
            }
        });
    }

    protected void applyTariffSelection() {
        int index = typeListBox.getSelectedIndex();
        if (index > -1) {
            TariffClass tariffClass = getSelectedTariffClass();
            final ITariffUIClass tariffUIClass = TariffUI.getTariffUIClass(tariffClass, clientFactory);
            if (this.tariffUIClass == null || !this.tariffUIClass.equals(tariffUIClass)) {
                logger.info("Getting tariffInitData: clientFactory=" + clientFactory+" tariffClass.getCalcClass()=" + tariffClass.getCalcClass());
                clientFactory.getPricingStructureRpc().getTariffInitData(pricingStructure, tariffClass.getId(), null,
                        new ClientCallback<ITariffInitData>() {
                            @Override
                            public void onSuccess(ITariffInitData result) {
                                showTariffForm(result, null, tariffUIClass);
                            }
                        });
            }
        }
    }

    private void showTariffForm(ITariffInitData tariffInitData, TariffWithData tariffWithData, ITariffUIClass tariffUIClass) {
        try {
            logger.info("Showing tariff form: "+tariffInitData+" "+tariffWithData+" "+(tariffUIClass == null? "no tariffUIClass" : ""+tariffUIClass.getClass().getName()));
            removeTariffForm();
            this.tariffUIClass = tariffUIClass;
            if (tariffUIClass != null) {
                if (tariffInitData != null) {
                    tariffUIClass.setTariffInitData(tariffInitData);
                }
                if (tariffWithData != null) {
                    tariffUIClass.setCalcTemplate(tariffWithData.getTariff().getCalcTemplate());
                    tariffUIClass.setCalcContents(tariffWithData.getTariff().getCalcContents());   //populates UI for xml calc_contents; empty method for Json classes
                    tariffUIClass.setTariffData(tariffWithData.getTariffData());                   //populates UI for Json calc_contents; empty method in BaseTariff
                }
                pnlTariffType.add(tariffUIClass);
                tariffUIClass.setForm(form);
                if (tariffWithData != null
                        && tariffWithData.getTariff() != null
                        && tariffWithData.getTariff().getStartDate() != null
                        && tariffWithData.getTariff().getStartDate().getTime() < System.currentTimeMillis()) {
                    setFormReadOnly(true);
                    tariffUIClass.setFormReadOnly(true);
                }
            } else {
                logger.info("No tariffUIClass specified");
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Could not display tariff form", e);
            Dialogs.centreErrorMessage(e.getMessage(),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    public void setTariffWithData(final TariffWithData tariffWithData) {
        this.tariffWithData = tariffWithData;
        logger.info("Displaying tariff: "+tariffWithData.getTariffData());
        Tariff tariff = tariffWithData.getTariff();
        if (tariff.getTariffClassId() != null) {
            populateTariffTypeItems();
            TariffClass tariffClass = getTariffClassAndSetSelectedIndex(tariff.getTariffClassId());
            logger.info("tariffClass=" + tariffClass.getId() + "," + tariffClass.getCalcClassName());
            final ITariffUIClass tariffUIClass = TariffUI.getTariffUIClass(tariffClass, clientFactory);
            logger.info("tariffUIClass=" + tariffUIClass.getClass().getName());
            clientFactory.getPricingStructureRpc().getTariffInitData(pricingStructure, tariffClass.getId(), tariff,
                    new ClientCallback<ITariffInitData>() {
                        @Override
                        public void onSuccess(ITariffInitData result) {
                            showTariffForm(result, tariffWithData, tariffUIClass);
                        }
                    });
        } else {
            applyTariffSelection();
        }
        nameTextBox.setText(tariff.getName());
        descTextBox.setText(tariff.getDescription());
        Format format = FormatUtil.getInstance();
        startDateBox.setValue(format.parseDateTime(format.formatDateTime(tariff.getStartDate())));
    }

    private TariffClass getTariffClassAndSetSelectedIndex(long id) {
        for (TariffClass tariffClass : tariffClassList) {
            if (tariffClass.getId().longValue() == id) {
                for (int i = 0; i < typeListBox.getItemCount(); i++) {
                    if (typeListBox.getValue(i).equals(tariffClass.getCalcClassName())) {
                        typeListBox.setSelectedIndex(i);
                        return tariffClass;
                    }
                }
            }
        }
        return null;
    }

    public TariffWithData getTariffWithData(List<TariffWithData> tariffWithDataList) {
        clearErrors();
        if (tariffUIClass == null) {
            typeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("pricingstructure.tariffs.ui.none"));
            return null;
        }

        tariffUIClass.clearErrors();

        boolean valid = true;

        Tariff t = new Tariff();
        t.setName(nameTextBox.getText());
        t.setDescription(descTextBox.getText());
        Date startDate = startDateBox.getValue();
        removeTimeZoneOffset(startDate);
        t.setStartDate(startDate);

        if (!ClientValidatorUtil.getInstance().validateField(t, "name", nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(t, "description", descElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(t, "startDate", startDateElement)) {
            valid = false;
        }

        String[] dateFormatParam = {FormatUtil.getInstance().getDateTimeFormat()};
        if (startDateBox.getValue() != null) {
            //startDate may not be in the past and must be > startdate of currently running tariff
            Long thisId = null;
            if (this.tariffWithData != null && this.tariffWithData.getTariff() != null) {
                thisId = this.tariffWithData.getTariff().getId();
            }

            Date currentDate = new Date();
            removeTimeZoneOffset(currentDate);
			if (startDate.equals(currentDate) || startDate.before(currentDate)) {
                valid = false;
                startDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.startdate"));
            }

            //startDate may not be duplicate
            Iterator<TariffWithData> itr = tariffWithDataList.listIterator();
            while (itr.hasNext()) {
                TariffWithData twd = (TariffWithData)itr.next();
                Tariff tariff = twd.getTariff();
                if (tariff != null && tariff.getId() != thisId) {
					if (startDate.equals(tariff.getStartDate())) {
                        valid = false;
                        startDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.startdate.unique"));
                        break;
                    }
                }
            }

            if (startDate != null && !MeterMngClientUtils.isDateBoxValueValid(startDateBox.getTextBox().getText())) {
                valid = false;
                startDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.datetime.invalid", dateFormatParam));
            }
        } else if (startDate == null && !startDateBox.getTextBox().getText().trim().isEmpty()) {
            valid = false;
            startDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.datetime.invalid", dateFormatParam));
        }
        TariffClass selectedTariffClass = getSelectedTariffClass();
        if (selectedTariffClass != null) {
            t.setTariffClassId(selectedTariffClass.getId());
            t.setCalcTemplate(selectedTariffClass.getCalcTemplate());
        }
        if (!ClientValidatorUtil.getInstance().validateField(t, "tariffClassId", typeElement)) {
            valid = false;
        }
        t.setCalcContents(tariffUIClass.getCalcContents());
        logger.info("calcContents: "+t.getCalcContents());
        if (t.getCalcContents() == null) {
            valid = false;
        }

        ITariffData tariffData = tariffUIClass.getTariffData();
        logger.info("tariffData: "+tariffData);

        if(tariffData == null && tariffUIClass.tariffDataRequired()) {
            // some tariffs use tariffData to build up calc contents if using json
            // they return a placeholder for calc contents to get past validator and
            // so need this additional check to make sure tariff data is validated
            logger.info("no tariff data, not valid");
            valid = false;
        }

        if (valid ) {
            if (tariffWithData != null) {
                Tariff tariff = tariffWithData.getTariff();
                tariff.setName(t.getName());
                tariff.setDescription(t.getDescription());
                tariff.setStartDate(t.getStartDate());
                tariff.setTariffClassId(t.getTariffClassId());
                tariff.setCalcContents(t.getCalcContents());
                tariff.setCalcTemplate(t.getCalcTemplate());
                tariffWithData.setTariffData(tariffData);
            } else {
                this.tariffWithData = new TariffWithData(t, tariffData);
            }
            logger.info("tariffWithData:" + tariffWithData);
            logger.info("tariffWithData:" + tariffWithData.getTariffData());
            return tariffWithData;
        } else {
            logger.info("Invalid tariff input");
            return null;
        }
    }

    @Override
    public void clearFields() {
        clearCoreDataFields();
        removeTariffForm();
        setFormReadOnly(false);
        populateTariffTypeItems();
        typeListBox.setSelectedIndex(0);
    }

    protected void clearCoreDataFields() {
        tariffWithData = new TariffWithData();
        tariffWithData.setTariff(new Tariff());
        nameTextBox.setText("");
        descTextBox.setText("");
        startDateBox.setValue(null);
    }

    private void setFormReadOnly(boolean readOnly) {
        form.getSaveBtn().setEnabled(!readOnly);
        if (readOnly) {
            form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("tariff.title.view"));
        }
        readOnlyLabel.setVisible(readOnly);
    }

    private void removeTariffForm() {
        if (tariffUIClass != null) {
            tariffUIClass.removeForm();
        }
        pnlTariffType.clear();
        this.tariffUIClass = null;
    }

    @Override
    public void clearErrors() {
        nameElement.clearErrorMsg();
        descElement.clearErrorMsg();
        typeElement.clearErrorMsg();
        startDateElement.clearErrorMsg();
    }

    public ITariffUIClass getTariffUIClass() {
        return tariffUIClass;
    }

    public FlowPanel getPnlTariffType() {
        return pnlTariffType;
    }

    @SuppressWarnings("deprecation")
    private void removeTimeZoneOffset(Date date) {
        TimeZone timeZone = FormatUtil.getInstance().getTimeZone();
        if (timeZone != null && date != null) {
            date.setMinutes(date.getMinutes() + timeZone.getStandardOffset() - date.getTimezoneOffset());
        }
    }

    private void populateTariffTypeItems() {
        typeListBox.clear();
        for (TariffClass tariffClass : tariffClassList) {
            boolean shouldAdd = true;
            if (!RecordStatus.ACT.name().equals(tariffClass.getRecordStatus())) {
                shouldAdd = false;
            }
            if (tariffWithData != null) {
                Long tariffClassId = tariffWithData.getTariff().getTariffClassId();
                if (tariffClassId != null && tariffClassId.compareTo(tariffClass.getId()) == 0) {
                    shouldAdd = true;
                }
            }
            if (shouldAdd) {
                typeListBox.addItem(tariffClass.getCalcClassName());
            }
        }
    }

    private TariffClass getSelectedTariffClass() {
        String selectedValue = typeListBox.getSelectedValue();
        for (TariffClass tariffClass : tariffClassList) {
            if (selectedValue.equals(tariffClass.getCalcClassName())) {
                return tariffClass;
            }
        }
        return null;
    }
}
