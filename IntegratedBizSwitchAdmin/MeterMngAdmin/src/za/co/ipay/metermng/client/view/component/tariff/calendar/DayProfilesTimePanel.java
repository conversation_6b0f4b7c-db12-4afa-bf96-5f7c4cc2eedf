package za.co.ipay.metermng.client.view.component.tariff.calendar;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.metermng.client.event.PeriodsUpdatedEvent;
import za.co.ipay.metermng.client.event.PeriodsUpdatedEventHandler;
import za.co.ipay.metermng.client.event.SeasonAssignedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;
import za.co.ipay.metermng.shared.TouDayProfileData;
import za.co.ipay.metermng.shared.TouDayProfileTimeData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.FocusWidget;
import com.google.gwt.user.client.ui.Focusable;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

public class DayProfilesTimePanel extends BaseComponent {

    @UiField(provided=true) CellTable<TouDayProfileTimeData> assignPeriodsTable;
    @UiField TablePager assignPeriodsPager;

    @UiField FlowPanel formPanel;
    @UiField FormElement periodElement;
    @UiField FormElement startTimeElement;
    @UiField FormElement endTimeElement;
    @UiField IpayListBox lstbxPeriod;
    @UiField TextBox txtbxStartHour;
    @UiField TextBox txtbxStartMinute;
    @UiField TextBox txtbxEndHour;
    @UiField TextBox txtbxEndMinute;

    @UiField Button btnCancelPeriod;
    @UiField Button btnSavePeriod;
    @UiField Button btnDeletePeriod;
    @UiField Anchor anchrNewPeriod;

    @UiField Label errorMsg;

    private ListDataProvider<TouDayProfileTimeData> dataProvider;
    private SingleSelectionModel<TouDayProfileTimeData> selectionModel;

    private TouDayProfileData dayProfile = new TouDayProfileData();
    private TouDayProfileTimeData dayProfileTime;

    private ArrayList<TouDayProfileTimeData> timesList;
    private TextColumn<TouDayProfileTimeData> periodColumn;
    private TextColumn<TouDayProfileTimeData> startColumn;
    private TextColumn<TouDayProfileTimeData> endColumn;

    @UiField HTML dataTitle;

    protected HasDirtyData hasDirtyData;
    protected boolean readOnly = false;


    private static Logger logger = Logger.getLogger(DayProfilesTimePanel.class.getName());
    private CalendarContainer parentContainer;

    private static DayProfilesTimeUiBinder uiBinder = GWT.create(DayProfilesTimeUiBinder.class);

    interface DayProfilesTimeUiBinder extends UiBinder<Widget, DayProfilesTimePanel> {
    }

    public DayProfilesTimePanel() {
        super();
    }

    public DayProfilesTimePanel(final ClientFactory clientFactory, CalendarContainer parentContainer) {
        super();
        this.clientFactory = clientFactory;
        this.parentContainer = parentContainer;
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        this.hasDirtyData = ((Workspace) parentContainer).createAndRegisterHasDirtyData();
        initAssignPeriodsTable();
        populatePeriods();

        clientFactory.getEventBus().addHandler(PeriodsUpdatedEvent.TYPE, new PeriodsUpdatedEventHandler() {

            @Override
            public void processPeriodsUpdatedEvent(PeriodsUpdatedEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populatePeriods();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        txtbxStartHour.addBlurHandler(new BlurHandler() {

            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxStartHour.getText().trim().length()==1) {
                    txtbxStartHour.setText("0"+txtbxStartHour.getText().trim());
                }
            }
        });
        txtbxStartHour.addKeyUpHandler(new KeyUpHandler() {

            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleHourBox(txtbxStartHour, txtbxStartMinute);
            }

        });
        txtbxStartMinute.addBlurHandler(new BlurHandler() {

            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxStartMinute.getText().trim().length()==1) {
                    txtbxStartMinute.setText("0"+txtbxStartMinute.getText().trim());
                }
            }
        });
        txtbxStartMinute.addKeyUpHandler(new KeyUpHandler() {

            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleMinuteBox(txtbxStartMinute, txtbxEndHour);
            }

        });

        txtbxEndHour.addBlurHandler(new BlurHandler() {

            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxEndHour.getText().trim().length()==1) {
                    txtbxEndHour.setText("0"+txtbxEndHour.getText().trim());
                }
            }
        });
        txtbxEndHour.addKeyUpHandler(new KeyUpHandler() {

            @Override
            public void onKeyUp(KeyUpEvent event) {
                handleHourBox(txtbxEndHour, txtbxEndMinute);
            }

        });
        txtbxEndMinute.addBlurHandler(new BlurHandler() {

            @Override
            public void onBlur(BlurEvent event) {
                if (txtbxEndMinute.getText().trim().length()==1) {
                    txtbxEndMinute.setText("0"+txtbxEndMinute.getText().trim());
                }
            }
        });
        txtbxEndMinute.addKeyUpHandler(new KeyUpHandler() {

            @Override
            public void onKeyUp(KeyUpEvent event) {
               handleMinuteBox(txtbxEndMinute, lstbxPeriod);
            }

        });

        addFieldHandlers();
    }

    void handleHourBox(TextBox hour, FocusWidget nextFocus) {
        boolean accept = true;
        String txt = hour.getText();
        try {
            if (txt.length()==1) {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 2) {
                    accept = false;
                }
            } else {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 23) {
                    accept = false;
                }
            }

        } catch (NumberFormatException nfe) {
            accept = false;
        }
        if (!accept) {
            hour.setText(txt.substring(0,txt.length()-1));
        } else {
            if (txt.length()==2) {
                nextFocus.setFocus(true);
            }
        }
    }

    void handleMinuteBox(TextBox minuteBox, Focusable nextFocus) {
        boolean accept = true;
        String txt = minuteBox.getText();
        try {
            if (txt.length()==1) {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 5) {
                    accept = false;
                }
            } else {
                if (Integer.valueOf(txt) < 0 || Integer.valueOf(txt) > 59) {
                    accept = false;
                }
            }

        } catch (NumberFormatException nfe) {
            accept = false;
        }
        if (!accept) {
            minuteBox.setText(txt.substring(0,txt.length()-1));
        } else {
            if (txt.length()==2) {
                nextFocus.setFocus(true);
            }
        }
    }

    public void setTouDayProfileData(TouDayProfileData dayProfile) {
        this.dayProfile = dayProfile;
        dataTitle.setText(ResourcesFactoryUtil.getInstance().getMessages().getMessage("calendar.assign.period.title") + " " + dayProfile.getName() + " ("+dayProfile.getCode()+")");
        refreshTable();
        clearFields();
    }

    protected void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            assignPeriodsTable = new CellTable<TouDayProfileTimeData > (15, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            assignPeriodsTable = new CellTable<TouDayProfileTimeData > (15);
        }
    }


    protected void initAssignPeriodsTable() {
        if (dataProvider == null) {
            periodColumn = new TextColumn<TouDayProfileTimeData>() {
                @Override
                public String getValue(TouDayProfileTimeData data) {
                    if (data.getTouPeriodId() != null) {
                        return (data.getPeriod().getName() + "("+data.getPeriod().getCode()+")");
                    }
                    return " ? ";
                }
            };
            periodColumn.setSortable(true);

            startColumn = new TextColumn<TouDayProfileTimeData>() {
                @Override
                public String getValue(TouDayProfileTimeData data) {
                    if (data.getStartHour() != null) {
                        String st;
                        if (data.getStartHour()<10) {
                            st = "0"+data.getStartHour();
                        } else {
                            st = String.valueOf(data.getStartHour());
                        }
                        st+=":";
                        if (data.getStartMinute()<10) {
                            st += "0"+data.getStartMinute();
                        } else {
                            st += data.getStartMinute();
                        }
                        return st;
                    }
                    return " ? ";
                }
            };
            startColumn.setSortable(true);

            endColumn = new TextColumn<TouDayProfileTimeData>() {
                @Override
                public String getValue(TouDayProfileTimeData data) {
                    if (data.getEndHour() != null) {
                        String end;
                        if (data.getEndHour()<10) {
                            end = "0"+data.getEndHour();
                        } else {
                            end = String.valueOf(data.getEndHour());
                        }
                        end+=":";
                        if (data.getEndMinute()<10) {
                            end += "0"+data.getEndMinute();
                        } else {
                            end += data.getEndMinute();
                        }
                        return end;
                    }
                    return " ? ";
                }
            };
            endColumn.setSortable(true);

            // Add the columns.
            assignPeriodsTable.addColumn(startColumn, MessagesUtil.getInstance().getMessage("calendar.assign.period.start"));
            assignPeriodsTable.addColumn(endColumn, MessagesUtil.getInstance().getMessage("calendar.assign.period.end"));
            assignPeriodsTable.addColumn(periodColumn, MessagesUtil.getInstance().getMessage("calendar.assign.period"));

            dataProvider = new ListDataProvider<TouDayProfileTimeData>();
            dataProvider.addDataDisplay(assignPeriodsTable);
            assignPeriodsPager.setDisplay(assignPeriodsTable);
            assignPeriodsTable.setPageSize(getPageSize());

            logger.info("Created Day profile times table");
        }

        selectionModel = new SingleSelectionModel<TouDayProfileTimeData>();
        CellPreviewEvent.Handler<TouDayProfileTimeData> handler = new CellPreviewEvent.Handler<TouDayProfileTimeData>() {
            final CellPreviewEvent.Handler<TouDayProfileTimeData> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<TouDayProfileTimeData> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (hasDirtyData.isDirtyData()) {
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    hasDirtyData.setDirtyData(false);
                                                    event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                    logger.info("Discarding changes for selection event");
                                                } else {
                                                    event.setCanceled(true);
                                                    logger.info("Cancelled selection event, staying with current selection");
                                                }

                                            }});
                            } else {
                                selectionEventManager.onCellPreview(event);
                            }

                        }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }
        };
        assignPeriodsTable.setSelectionModel(selectionModel, handler);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final TouDayProfileTimeData selected = selectionModel.getSelectedObject();
                if (selected != null) {
                    setDayProfileTimeData(selected);
                    assignPeriodsTable.getSelectionModel().setSelected(selected, true);
                }
            }
        });
    }

    public void clearTableSelection() {
        TouDayProfileTimeData selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    public void refreshTable() {
        setErrorMessage(null);
        if (dayProfile != null && dayProfile.getId() != null) {
            clientFactory.getCalendarRpc().getTimesByTouDayProfile(dayProfile.getId(), new ClientCallback<ArrayList<TouDayProfileTimeData>>() {
                @Override
                public void onSuccess(ArrayList<TouDayProfileTimeData> result) {
                    logger.info("Got times: " + result.size());
                    if (dataProvider != null && dataProvider.getList() != null) {
                        dataProvider.getList().clear();
                        dataProvider.getList().addAll(result);
                        setDayProfileTimesList(result);
                    }
                }
            });
        } else {
            if (dataProvider.getList() != null) {
                dataProvider.getList().clear();
            }
            if (timesList != null) {
                timesList.clear();
            }
            clearFields();
        }
    }

    public void setDayProfileTimesList(ArrayList<TouDayProfileTimeData> thedata) {
        logger.info("Displaying day profiles: "+thedata.size());
        timesList = thedata;

        dataProvider.getList().clear();
        dataProvider.getList().addAll(thedata);
        dataProvider.refresh();

        ListHandler<TouDayProfileTimeData> columnSortHandler = new ListHandler<TouDayProfileTimeData>(dataProvider.getList());
        columnSortHandler.setComparator(periodColumn, new Comparator<TouDayProfileTimeData>() {
            public int compare(TouDayProfileTimeData o1, TouDayProfileTimeData o2) {
                if (o1 == o2) {
                    return 0;
                }
                if (o1 != null && o1.getPeriod() != null) {
                    return (o2 != null && o2.getPeriod() != null) ? o1.getPeriod().getName().compareTo(o2.getPeriod().getName()) : 1;
                }
                return -1;
            }
        });

        columnSortHandler.setComparator(startColumn, new Comparator<TouDayProfileTimeData>() {
            public int compare(TouDayProfileTimeData o1, TouDayProfileTimeData o2) {
                int r = 0;
                if (o1 == o2) {
                    return r;
                }
                if (o1 != null && o1.getStartHour() != null) {
                    r = (o2 != null && o2.getStartHour() != null) ? o1.getStartHour().compareTo(o2.getStartHour()) : 1;
                }
                if (r == 0) {
                    r = (o2 != null && o2.getStartMinute() != null) ? o1.getStartMinute().compareTo(o2.getStartMinute()) : 1;
                }
                return r;
            }
        });

        columnSortHandler.setComparator(endColumn, new Comparator<TouDayProfileTimeData>() {
            public int compare(TouDayProfileTimeData o1, TouDayProfileTimeData o2) {
                int r = 0;
                if (o1 == o2) {
                    return r;
                }
                if (o1 != null && o1.getEndHour() != null) {
                    r = (o2 != null && o2.getEndHour() != null) ? o1.getEndHour().compareTo(o2.getEndHour()) : 1;
                }
                if (r == 0) {
                    r = (o2 != null && o2.getEndMinute() != null) ? o1.getEndMinute().compareTo(o2.getEndMinute()) : 1;
                }
                return r;
            }
        });

        assignPeriodsTable.addColumnSortHandler(columnSortHandler);
        assignPeriodsTable.getColumnSortList().push(startColumn);
        ColumnSortEvent.fire(assignPeriodsTable, assignPeriodsTable.getColumnSortList());
        assignPeriodsTable.getColumnSortList().push(startColumn);
        ColumnSortEvent.fire(assignPeriodsTable, assignPeriodsTable.getColumnSortList());
    }

    protected void populatePeriods() {
        clientFactory.getCalendarRpc().getPeriods(new ClientCallback<ArrayList<TouPeriod>>() {

            @Override
            public void onSuccess(ArrayList<TouPeriod> result) {
                lstbxPeriod.clear();
                for (TouPeriod period : result) {
                    lstbxPeriod.addItem(period.getName()+" ("+period.getCode()+")", String.valueOf(period.getId()));
                }
            }
        });
    }

    private void setDayProfileTimeData(TouDayProfileTimeData theprofile) {
        this.dayProfileTime = theprofile;
        if (dayProfileTime == null) {
            dayProfileTime = new TouDayProfileTimeData();
        }
        if (dayProfileTime.getId() == null) {
            clearTableSelection();
            btnDeletePeriod.setVisible(false);
        } else {
            btnDeletePeriod.setVisible(true);
        }
        mapDataToForm();
    }

    protected void mapDataToForm() {
        clearErrorMessages();
        clearFields();
        if (dayProfileTime != null && dayProfileTime.getId() != null) {
            lstbxPeriod.selectItemByValue(String.valueOf(dayProfileTime.getTouPeriodId()));
            txtbxStartHour.setText(getAsDoubleDigits(String.valueOf(dayProfileTime.getStartHour())));
            txtbxStartMinute.setText(getAsDoubleDigits(String.valueOf(dayProfileTime.getStartMinute())));
            txtbxEndHour.setText(getAsDoubleDigits(String.valueOf(dayProfileTime.getEndHour())));
            txtbxEndMinute.setText(getAsDoubleDigits(String.valueOf(dayProfileTime.getEndMinute())));
        } else if (dayProfileTime != null && dayProfileTime.getStartHour() != null) {
            txtbxStartHour.setText(getAsDoubleDigits(String.valueOf(dayProfileTime.getStartHour())));
            txtbxStartMinute.setText(getAsDoubleDigits(String.valueOf(dayProfileTime.getStartMinute())));
        }
    }

    private String getAsDoubleDigits(String input) {
        if (input.length()==1){
            input = "0"+input;
        }
        return input;
    }

    public void clearFields() {
        lstbxPeriod.clearSelections();
        txtbxStartHour.setText("");
        txtbxStartMinute.setText("");
        txtbxEndHour.setText("");
        txtbxEndMinute.setText("");
    }

    @UiHandler("btnSavePeriod")
    void handleSaveButton(ClickEvent event) {
        if (readOnly) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.readOnly", new String[] {parentContainer.getPricingStructureNames()}), MediaResourceUtil.getInstance().getErrorIcon(), btnSavePeriod.getAbsoluteLeft()+btnSavePeriod.getOffsetWidth(), btnSavePeriod.getAbsoluteTop()-btnSavePeriod.getOffsetHeight(), null);
            return;
        }
        // Map form fields to data object
        clearErrorMessages();
        mapFormToData();

        if (isValid()) {
            if (clientFactory != null) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {

                        clientFactory.getCalendarRpc().getTimeInMilliseconds(dayProfileTime, new ClientCallback<TouDayProfileTimeData>() {
                            @Override
                            public void onSuccess(TouDayProfileTimeData result) {
                                boolean continueSaving = true;
                                for (TouDayProfileTimeData data : timesList) {
                                    if (result.getId() == null || !(result.getId().equals(data.getId()))) {
                                        if (result.getStartMs() >= data.getStartMs() && result.getStartMs() <= data.getEndMs()
                                                || result.getEndMs() >= data.getStartMs() && result.getEndMs() <= data.getEndMs()) {
                                            continueSaving = false;
                                            break;
                                        }
                                    }
                                }
                                if (continueSaving) {
                                    clientFactory.getCalendarRpc().updateDayProfileTimes(dayProfileTime, parentContainer.getCalendar().getId(), new  ClientCallback<TouDayProfileTimeData>() {

                                        @Override
                                        public void onSuccess(TouDayProfileTimeData result) {
                                            hasDirtyData.setDirtyData(false);
                                            clientFactory.getEventBus().fireEvent(new SeasonAssignedEvent());
                                            TouDayProfileTimeData newprofile = new TouDayProfileTimeData();
                                            if (result.getEndMinute().equals(59)) {
                                                if (result.getEndHour().equals(23)) {
                                                    newprofile.setStartHour(0);
                                                } else {
                                                    newprofile.setStartHour(result.getEndHour()+1);
                                                }
                                                newprofile.setStartMinute(0);
                                            } else {
                                                newprofile.setStartHour(result.getEndHour());
                                                newprofile.setStartMinute(result.getEndMinute()+1);
                                            }
                                            refreshTable();
                                            setDayProfileTimeData(newprofile);
                                            Dialogs.displayInformationMessage(
                                                    MessagesUtil.getInstance().getMessage("calendar.assign.period.saved"),
                                                    MediaResourceUtil.getInstance().getInformationIcon(),
                                                    btnSavePeriod.getAbsoluteLeft(), btnSavePeriod.getAbsoluteTop(),null);
                                        }
                                    });
                                } else {
                                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.error.save"), MediaResourceUtil.getInstance().getErrorIcon(), null);
                                    setErrorMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.error.time.already.assigned"));
                                }
                            }
                        });
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.error.save"), MediaResourceUtil.getInstance().getErrorIcon(), null);
            btnSavePeriod.setEnabled(true);
        }
    }

    @UiHandler("btnCancelPeriod")
    void handleClearButton(ClickEvent event) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        setDayProfileTimeData(null);
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancelPeriod.getAbsoluteLeft()+btnCancelPeriod.getOffsetWidth(), btnCancelPeriod.getAbsoluteTop()-btnCancelPeriod.getOffsetHeight(), null);
                    }
                }});
        } else {
            setDayProfileTimeData(null);
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancelPeriod.getAbsoluteLeft()+btnCancelPeriod.getOffsetWidth(), btnCancelPeriod.getAbsoluteTop()-btnCancelPeriod.getOffsetHeight(), null);
        }
    }

    @UiHandler("btnDeletePeriod")
    void handleDeleteButton(ClickEvent event) {
        deleteTimePeriod();
    }

    @UiHandler("anchrNewPeriod")
    void handlePeriodAnchor(ClickEvent e) {
        PopupPanel simplePopup = new PopupPanel(false);
        simplePopup.setGlassEnabled(true);
        simplePopup.ensureDebugId("periodsAnchorPopup");
        PeriodsPanel periodsPanel = new PeriodsPanel(clientFactory, new LocalOnlyHasDirtyData(), simplePopup);
        periodsPanel.refreshTable();
        simplePopup.setWidget(periodsPanel);
        simplePopup.center();
        simplePopup.show();
    }

    public void deleteTimePeriod() {
        //ToDo double check with popup
        final int aleft = btnDeletePeriod.getAbsoluteLeft();
        final int atop = btnDeletePeriod.getAbsoluteTop();
        Dialogs.confirm(
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.delete"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                @Override
                                public void callback(SessionCheckResolution resolution) {
                                    hasDirtyData.setDirtyData(false);
                                    clientFactory.getCalendarRpc().deleteDayProfileTime(dayProfileTime.getId(), parentContainer.getCalendar().getId(), new ClientCallback<Boolean>(aleft, atop) {
                                        @Override
                                        public void onSuccess(Boolean result) {
                                            if (result) {
                                                setDayProfileTimeData(null);
                                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.deleted"), MediaResourceUtil.getInstance().getInformationIcon(), aleft, atop, MessagesUtil.getInstance().getMessage("button.close"));
                                                refreshTable();
                                                clientFactory.getEventBus().fireEvent(new SeasonAssignedEvent());
                                            }
                                        }
                                    });
                                }
                            };
                            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                        }
                    }
                },aleft,atop
         );
    }

    private void mapFormToData() {
        if (dayProfileTime==null) {
            dayProfileTime = new TouDayProfileTimeData();
        }
        dayProfileTime.setTouDayProfileId(dayProfile.getId());
        dayProfileTime.setTouPeriodId(Long.valueOf(lstbxPeriod.getValue(lstbxPeriod.getSelectedIndex())));

        try {
            dayProfileTime.setStartHour(Integer.valueOf(txtbxStartHour.getText()));
            dayProfileTime.setStartMinute(Integer.valueOf(txtbxStartMinute.getText()));
            dayProfileTime.setEndHour(Integer.valueOf(txtbxEndHour.getText()));
            dayProfileTime.setEndMinute(Integer.valueOf(txtbxEndMinute.getText()));
        } catch(NumberFormatException nfe) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.dayprofile.error.save"), MediaResourceUtil.getInstance().getErrorIcon(), null);
            btnSavePeriod.setEnabled(true);
        }
    }

    private boolean isValid() {
        boolean valid = true;
        clearErrorMessages();
        if (txtbxStartHour.getText() == null || txtbxStartHour.getText().isEmpty()
                || txtbxStartMinute.getText() == null || txtbxStartMinute.getText().isEmpty()
                || txtbxEndHour.getText() == null || txtbxEndHour.getText().isEmpty()
                || txtbxEndMinute.getText() == null || txtbxEndMinute.getText().isEmpty()) {
            setErrorMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.nulls"));
            return false;
        }

        if (dayProfileTime.getStartHour().equals(dayProfileTime.getEndHour())) {
            if (dayProfileTime.getStartMinute() > dayProfileTime.getEndMinute()) {
                logger.info("day profile time end time before start time");
                setErrorMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.error.end.before.start"));
                return false;
            }
        } else if (dayProfileTime.getStartHour() > dayProfileTime.getEndHour()) {
            logger.info("day profile time end month before start month");
            setErrorMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.error.end.before.start"));
            return false;
        }

        if (txtbxEndHour.getText().trim().equals("00") &&  txtbxEndMinute.getText().trim().equals("00")) {
        	setErrorMessage(MessagesUtil.getInstance().getMessage("calendar.assign.period.end.maximum"));
        	return false;
        }

        return valid;
    }

    public void clearErrorMessages() {
        setErrorMessage(null);
        periodElement.clearErrorMsg();
        startTimeElement.clearErrorMsg();
        endTimeElement.clearErrorMsg();
    }

    void setErrorMessage(String msg) {
        if (msg == null) {
            msg = "";
        }
        errorMsg.setText(msg);
        errorMsg.setVisible(msg != null || !(msg.trim().equals("")));
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
        formPanel.setVisible(!readOnly);
    }

    protected void addFieldHandlers() {
        txtbxStartHour.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxStartMinute.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEndHour.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        txtbxEndMinute.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxPeriod.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }


}
