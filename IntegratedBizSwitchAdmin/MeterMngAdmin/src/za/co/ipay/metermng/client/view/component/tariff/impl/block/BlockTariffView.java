package za.co.ipay.metermng.client.view.component.tariff.impl.block;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.IntegerValueBox;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell.ViewData;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.gwt.common.shared.validation.ValidateUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.tariff.ContainsPayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.ITariffUIClass;
import za.co.ipay.metermng.client.view.component.tariff.PayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.impl.BaseTariffView;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.bsst.BsstAllowance;
import za.co.ipay.metermng.shared.dto.tariff.BlockDto;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;
import za.co.ipay.metermng.shared.tariff.MeterDebt;
import za.co.ipay.metermng.shared.tariff.block.Block;
import za.co.ipay.metermng.shared.tariff.block.BlockCalcContents;
import za.co.ipay.metermng.shared.tariff.block.BlockCalcContentsTemplate;
import za.co.ipay.metermng.shared.tariff.block.BlockCalcInitData;
import za.co.ipay.metermng.shared.tariff.block.ThresholdChargeData;
import za.co.ipay.metermng.shared.tariff.cyclic.CyclicChargeData;
import za.co.ipay.metermng.shared.tariff.paytype.PayTypeDiscount;
import za.co.ipay.metermng.shared.tariff.percent.PercentChargeData;
import za.co.ipay.metermng.shared.tariff.units.UnitChargeData;

public class BlockTariffView extends BaseTariffView implements ITariffUIClass, ContainsPayTypeDiscountPanel {
    private static RoundingMode DEFAULT_AMOUNT_ROUNDING_MODE = RoundingMode.HALF_UP;
    private static int DEFAULT_AMOUNT_ROUNDING_PRECISION = MeterMngStatics.PRECISION_DEFAULT;
    private static RoundingMode DEFAULT_TAX_ROUNDING_MODE = RoundingMode.HALF_UP;
    private static int DEFAULT_TAX_ROUNDING_PRECISION = MeterMngStatics.PRECISION_DEFAULT;
    private static RoundingMode DEFAULT_UNITS_ROUNDING_MODE = RoundingMode.UP;
    private static int DEFAULT_UNITS_ROUNDING_PRECISION = MeterMngStatics.PRECISION_UNITS_STS;

    @UiField FormElement taxElement;
    @UiField FormElement freeUnitsNameElement;
    @UiField FormElement freeUnitsElement;
    @UiField FormElement bsstChargeNameElement;
    @UiField FormElement bsstChargeElement;
    @UiField FormElement blocksElement;

    @UiField PercentageTextBox taxBox;
    @UiField TextBox freeUnitsNameTextBox;
    @UiField BigDecimalValueBox freeUnitsTextBox;

    @UiField FormRowPanel bsstChargeFormRowPanel;
    @UiField TextBox bsstChargeNameTextBox;
    @UiField BigDecimalValueBox bsstChargeAmountBox;

    @UiField FormElement minVendAmountElement;
    @UiField Label minVendAmountCurrencyLabel;
    @UiField BigDecimalValueBox minVendAmountBox;

    @UiField DisclosurePanel advancedPanel;
    @UiField FormElement unitSymbolElement;
    @UiField FormElement priceSymbolElement;
    @UiField FormElement amountRoundingModeElement;
    @UiField FormElement amountPrecisionElement;
    @UiField FormElement unitsRoundingModeElement;
    @UiField FormElement unitsPrecisionElement;
    @UiField FormElement taxRoundingModeElement;
    @UiField FormElement taxPrecisionElement;

    @UiField FormRowPanel paytypeDiscountFormRowPanel;

    @UiField TextBox unitSymbolBox;
    @UiField TextBox priceSymbolBox;
    @UiField ListBox amountRoundingModeBox;
    @UiField IntegerValueBox amountPrecisionBox;
    @UiField ListBox unitsRoundingModeBox;
    @UiField IntegerValueBox unitsPrecisionBox;
    @UiField ListBox taxRoundingModeBox;
    @UiField IntegerValueBox taxPrecisionBox;

    @UiField CellTable<BlockDto> blocksTable;

    @UiField FlowPanel cyclicChargesPanel;
    @UiField FlowPanel percentChargesPanel;
    @UiField FlowPanel unitChargesPanel;
    @UiField FlowPanel meterDebtChargesPanel;

    @UiField FormGroupPanel freeUnitsPanel;

    private PayTypeDiscountPanel payTypeDiscountPanel;

    private ListDataProvider<BlockDto> blocksDataProvider;
    private DecimalInputCell unitPriceCell;
    private DecimalInputCell thresholdCell;
    private String unitSymbol;
    private ITariffInitData tariffInitData;
    private BlockCalcContentsTemplate template;

    private static Logger logger = Logger.getLogger(BlockTariffView.class.getName());

    private static StepTariffViewUiBinder uiBinder = GWT.create(StepTariffViewUiBinder.class);
    private String inlineTemplate;

    interface StepTariffViewUiBinder extends UiBinder<Widget, BlockTariffView> {
    }

    public BlockTariffView(ClientFactory clientFactory, String unitSymbol) {
        this.clientFactory = clientFactory;
        this.unitSymbol = unitSymbol;
        initWidget(uiBinder.createAndBindUi(this));
        createCurrencyLabel();
        initUi();
        this.ensureDebugId("blockTariffView");
    }

    private void createCurrencyLabel() {
        minVendAmountCurrencyLabel.setText(FormatUtil.getInstance().getCurrencySymbol());
        if (FormatUtil.getInstance().isRightToLeft()) {
            minVendAmountCurrencyLabel.setStyleName("btCurrency-right");
        } else {
            minVendAmountCurrencyLabel.setStyleName("btCurrency-left");
        }
    }

    private void initUi() {
        amountRoundingModeBox.addItem("");
        unitsRoundingModeBox.addItem("");
        taxRoundingModeBox.addItem("");
        for(RoundingMode m : RoundingMode.values()) {
            if (RoundingMode.UNNECESSARY != m) {
                amountRoundingModeBox.addItem(m.name(), m.name());
                unitsRoundingModeBox.addItem(m.name(), m.name());
                taxRoundingModeBox.addItem(m.name(), m.name());
            }
        }

        payTypeDiscountPanel = new PayTypeDiscountPanel(this);
    	paytypeDiscountFormRowPanel.add(payTypeDiscountPanel);

        if (blocksDataProvider == null) {
            blocksDataProvider = new ListDataProvider<BlockDto>();

            //BlockDto count
            TextColumn<BlockDto> countColumn = new TextColumn<BlockDto>() {
                @Override
                public String getValue(BlockDto data) {
                    return data.getPosition()+"";
                }
            };

            //Unit price
            if (FormatUtil.getInstance().isRightToLeft()) {
                unitPriceCell = new DecimalInputCell("", FormatUtil.getInstance().getCurrencySymbol(), MessagesUtil.getInstance().getMessage("error.numeric.value"));
            } else {
                unitPriceCell = new DecimalInputCell(FormatUtil.getInstance().getCurrencySymbol(), "", MessagesUtil.getInstance().getMessage("error.numeric.value"));
            }
            final Column<BlockDto, String> unitPrice = new Column<BlockDto, String>(unitPriceCell) {
                @Override
                public String getValue(BlockDto data) {
                    if (data.getBlock().getUnitPrice() == null) {
                        return null;
                    }
                    return FormatUtil.getInstance().formatDecimal(data.getBlock().getUnitPrice());
                }
            };
            unitPrice.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
                @Override
                public void update(int index, BlockDto block, String value) {
                    if (value != null && (unitPriceCell.isNumeric(value) || value.isEmpty())) {
                        getForm().setDirtyData(true);
                        block.getBlock().setUnitPrice(FormatUtil.getInstance().parseDecimal(value));
                        unitPriceCell.getViewData(block).setInvalid(false);
                    } else {
                        unitPriceCell.getViewData(block).setInvalid(true);     // Mark as invalid.
                    }
                    blocksTable.redraw();
                    MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(unitPrice));
                }
            });

            //Threshold
            thresholdCell = new DecimalInputCell("", unitSymbol, MessagesUtil.getInstance().getMessage("error.numeric.value"));
            final Column<BlockDto, String> threshold = new Column<BlockDto, String>(thresholdCell) {
                @Override
                public String getValue(BlockDto data) {
                    if (data.getBlock().getThreshold() == null) {
                        return null;
                    }
                    return FormatUtil.getInstance().formatDecimal(data.getBlock().getThreshold());
                }
            };
            threshold.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
                @Override
                public void update(int index, BlockDto block, String value) {
                    if (value != null && (thresholdCell.isNumeric(value) || value.isEmpty())) {
                        getForm().setDirtyData(true);
                        block.getBlock().setThreshold(FormatUtil.getInstance().parseDecimal(value));
                        thresholdCell.getViewData(block).setInvalid(false);
                    } else {
                        thresholdCell.getViewData(block).setInvalid(true);     // Mark as invalid.
                    }
                    blocksTable.redraw();
                    MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(threshold));
                }
            });

            // Add the columns
            blocksTable.addColumn(countColumn, MessagesUtil.getInstance().getMessage("tariff.field.block.single"));
            blocksTable.addColumn(unitPrice, MessagesUtil.getInstance().getMessage("tariff.field.unitprice"));
            blocksTable.addColumn(threshold, MessagesUtil.getInstance().getMessage("tariff.field.threshold"));
            blocksTable.setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);
            blocksDataProvider.addDataDisplay(blocksTable);
            blocksTable.getColumn(0).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);
        }
        clearForm();
    }

    @Override
    public void setFormReadOnly(boolean readOnly) {
    }

    @Override
    public void setCalcContents(String contents) {
    }

    @Override
    public String getCalcContents() {
        // gets past validator will be replaced on server
        return "placeholder";
    }


    @Override
    public void setCalcTemplate(String calcTemplate) {
        this.inlineTemplate = calcTemplate;
    }

    @Override
    public String getCalcTemplate() {
        return inlineTemplate;
    }

    @Override
    public void setTariffInitData(ITariffInitData tariffInitData) {
        this.tariffInitData = tariffInitData;
        BlockCalcInitData blockCalcInitData = (BlockCalcInitData) tariffInitData;
        if(blockCalcInitData == null) {
            return;
        }
        template = blockCalcInitData.getBlockCalcContentsTemplate();
        if(template == null) {
            // backwards compatible settings
            template = new BlockCalcContentsTemplate();
            template.setEnableAdvancedSettings(true);
            template.setEnableMinVendAmt(true);
            template.setEnableNonAccruingMonthly(false);
            template.setEnableTax(true);
            template.setUseNameAsLabel(false);
        }
        BlockCalcContents blockCalcContents = template.getBlockCalcContents();
        if(blockCalcContents == null) {
            // backwards compatible settings
            blockCalcContents = new BlockCalcContents();
            template.setBlockCalcContents(blockCalcContents);
            List<Block> blocks = new ArrayList<>();
            for (int i = 0; i < 8; i++) {
                blocks.add(new Block());
            }
            blockCalcContents.setBlocks(blocks);
            blockCalcContents.setCyclicCharges(new ArrayList<CyclicChargeData>(1));
            blockCalcContents.getCyclicCharges().add(new CyclicChargeData());
            blockCalcContents.setPercentCharges(new ArrayList<PercentChargeData>(1));
            blockCalcContents.getPercentCharges().add(new PercentChargeData());
            blockCalcContents.setBsstAllowance(new BsstAllowance());
            blockCalcContents.setAmountRoundingMode(DEFAULT_AMOUNT_ROUNDING_MODE);
            blockCalcContents.setAmountRoundingPrecision(DEFAULT_AMOUNT_ROUNDING_PRECISION);
            blockCalcContents.setTaxRoundingMode(DEFAULT_TAX_ROUNDING_MODE);
            blockCalcContents.setTaxRoundingPrecision(DEFAULT_TAX_ROUNDING_PRECISION);
            blockCalcContents.setUnitsRoundingMode(DEFAULT_UNITS_ROUNDING_MODE);
            blockCalcContents.setUnitsRoundingPrecision(DEFAULT_UNITS_ROUNDING_PRECISION);
            blockCalcContents.setPayTypeDiscounts(new ArrayList<PayTypeDiscount>());
            blockCalcContents.setUnitSymbol(unitSymbol);
            blockCalcContents.setPriceSymbol(FormatUtil.getInstance().getCurrencySymbol());
        } else {
            List<Block> templateBlocks = template.getBlockCalcContents().getBlocks();
            List<UnitChargeData> globalUnitCharges = template.getBlockCalcContents().getUnitCharges();
            if (templateBlocks != null && !templateBlocks.isEmpty()) {
                List<Block> blocks = new ArrayList<>(templateBlocks.size());
                List<UnitChargeData> blockCharges = null;
                ThresholdChargeData thresholdCharge = null;
                for (Block block : templateBlocks) {
                    if (block.getUnitCharges() != null && !block.getUnitCharges().isEmpty()) {
                        blockCharges = new ArrayList<>(block.getUnitCharges().size());
                        for (UnitChargeData charge : block.getUnitCharges()) {
                            blockCharges.add(new UnitChargeData(charge.getName(), charge.isPercentage(), charge.getCharge(), charge.isTaxable(), charge.getCode()));
                        }
                    }
                    if (block.getThresholdCharge() != null) {
                        thresholdCharge = new ThresholdChargeData(block.getThresholdCharge().getName(), block.getThresholdCharge().getCharge(),
                                block.getThresholdCharge().isTaxable());
                    }
                    blocks.add(new Block(null, null, blockCharges, thresholdCharge, block.getCode(), block.getTaxCode(), block.isTaxable()));
                }
                blockCalcContents.setBlocks(blocks);
                if (blockCharges != null && !blockCharges.isEmpty() && globalUnitCharges != null && !globalUnitCharges.isEmpty()) {
                    throw new RuntimeException("Either use Block unit charges or Global unit charges. But not both in the same template.");
                }
            }
        }
        if(blockCalcContents.getAmountRoundingMode() == null) {
            blockCalcContents.setAmountRoundingMode(DEFAULT_AMOUNT_ROUNDING_MODE);
            blockCalcContents.setAmountRoundingPrecision(DEFAULT_AMOUNT_ROUNDING_PRECISION);
        }
        if(blockCalcContents.getTaxRoundingMode() == null) {
            blockCalcContents.setTaxRoundingMode(DEFAULT_TAX_ROUNDING_MODE);
            blockCalcContents.setTaxRoundingPrecision(DEFAULT_TAX_ROUNDING_PRECISION);
        }
        if(blockCalcContents.getUnitsRoundingMode() == null) {
            blockCalcContents.setUnitsRoundingMode(DEFAULT_UNITS_ROUNDING_MODE);
            blockCalcContents.setUnitsRoundingPrecision(DEFAULT_UNITS_ROUNDING_PRECISION);
        }

        if(! template.isEnableTax() && blockCalcContents.getTaxMultiplier() == null) {
            throw new RuntimeException("If tax is disabled it's default value must be set in the tariff calc template");
        }

        if(blockCalcContents.getBlocks() == null || blockCalcContents.getBlocks().isEmpty()) {
            blockCalcContents.setBlocks(new ArrayList<Block>(MeterMngStatics.ALLOWED_BLOCKS));
            for (int i = 0; i < MeterMngStatics.ALLOWED_BLOCKS; i++) {
                blockCalcContents.getBlocks().add(new Block());
            }
        }

        // Fill in form as if it was a tariff loaded from db.
        setTariffData(blockCalcContents, true);

        advancedPanel.setVisible(template.isEnableAdvancedSettings());
        payTypeDiscountPanel.setVisible(blockCalcContents.getPayTypeDiscounts() != null);
        enableTaxElements(template.isEnableTax());
        freeUnitsPanel.setVisible(blockCalcContents.getBsstAllowance() != null);
        bsstChargeNameElement.setVisible(template.isEnableBsstChargeName());
        minVendAmountElement.setVisible(template.isEnableMinVendAmt());
        meterDebtChargesPanel.setVisible(blockCalcContents.getMeterDebt() != null);
    }

    protected void enableTaxElements(boolean enabled) {
        taxElement.setVisible(enabled);
        taxPrecisionElement.setVisible(enabled);
        taxRoundingModeElement.setVisible(enabled);
    }

    @Override
    public void setTariffData(ITariffData tariffData) {
        logger.info("Setting tariffData:" + tariffData);
        BlockCalcContents calcContents = (BlockCalcContents) tariffData;
        if (calcContents == null) {
            return;
        }
        setTariffData(calcContents, inlineTemplate != null);
    }

    protected void setTariffData(BlockCalcContents calcContents, boolean init) {
        if (calcContents.getBsstAllowance() != null) {
            BsstAllowance bsstAllowance = calcContents.getBsstAllowance();
            freeUnitsNameTextBox.setText(bsstAllowance.getDescription());
            freeUnitsTextBox.setText(FormatUtil.getInstance().formatDecimal(bsstAllowance.getUnits()));
            if (bsstAllowance.getBsstCharge() != null) {
                //change names from Free units to Subsidised Units basically
                Messages msgUtil = MessagesUtil.getInstance();
                freeUnitsPanel.setLabelText(msgUtil.getMessage("tariff.field.subsidised.units.title"));
                freeUnitsNameElement.setLabelText(msgUtil.getMessage("tariff.field.subsidised.units.descrip"));
                freeUnitsElement.setLabelText(msgUtil.getMessage("tariff.field.subsidised.units"));
                freeUnitsElement.setHelpMsg(msgUtil.getMessage("tariff.field.subsidised.units.help"));
                freeUnitsElement.setLabelText(msgUtil.getMessage("tariff.field.subsidised.units"));
                freeUnitsElement.setHelpMsg(msgUtil.getMessage("tariff.field.subsidised.units.help"));
                bsstChargeNameTextBox.setValue(bsstAllowance.getBsstChargeName());
                bsstChargeAmountBox.setValue(bsstAllowance.getBsstCharge());
            } else {
                freeUnitsPanel.remove(bsstChargeFormRowPanel);
            }
        }

        minVendAmountBox.setValue(calcContents.getMinVendAmount());

        List<CyclicChargeData> cyclicCharges = calcContents.getCyclicCharges();
        if(cyclicCharges != null && ! cyclicCharges.isEmpty()) {
            cyclicChargesPanel.clear();
            for (CyclicChargeData cyclicChargeData : cyclicCharges) {
                boolean required = false;
                if(init && cyclicChargeData.getName() != null) {
                    required = true;
                }
                CyclicChargePanel cyclicChargePanel = new CyclicChargePanel(clientFactory, required, template.isUseNameAsLabel(), template.isEnableNonAccruingMonthly());
                cyclicChargesPanel.add(cyclicChargePanel);
                cyclicChargePanel.setCyclicCharge(cyclicChargeData);
            }
        }

        List<PercentChargeData> percentCharges = calcContents.getPercentCharges();
        if(percentCharges != null && ! percentCharges.isEmpty()) {
            percentChargesPanel.clear();
            for (PercentChargeData percentChargeData : percentCharges) {
                boolean required = false;
                // only make the field required if this data is from template
                if(init && percentChargeData.getName() != null) {
                    required = true;
                }
                PercentChargePanel percentChargePanel = new PercentChargePanel(clientFactory, required, template.isUseNameAsLabel());
                percentChargesPanel.add(percentChargePanel);
                percentChargePanel.setPercentCharge(percentChargeData);
            }
        }

        List<UnitChargeData> unitCharges = calcContents.getUnitCharges();
        if(unitCharges != null && ! unitCharges.isEmpty()) {
            unitChargesPanel.clear();
            for (UnitChargeData unitChargeData : unitCharges) {
                boolean required = false;
                // only make the field required if this data is from template
                if(init && unitChargeData.getName() != null) {
                    required = true;
                }
                UnitChargePanel unitChargePanel = new UnitChargePanel(clientFactory, required, template.isUseNameAsLabel(), template.isEnableTax());
                unitChargesPanel.add(unitChargePanel);
                unitChargePanel.setUnitCharge(unitChargeData);
            }
        }

        MeterDebt meterDebt = calcContents.getMeterDebt();
        if (meterDebt != null) {
            meterDebtChargesPanel.clear();
            boolean required = false;
            MeterDebtChargePanel meterDebtChargePanel = new MeterDebtChargePanel(clientFactory, required);
            meterDebtChargePanel.clearForm();
            meterDebtChargePanel.setMeterDebt(meterDebt);
            meterDebtChargesPanel.add(meterDebtChargePanel);
        }

        BigDecimal taxMultiplier = calcContents.getTaxMultiplier();
        if (taxMultiplier != null) {
            taxMultiplier = taxMultiplier.subtract(BigDecimal.ONE).multiply(BigDecimal.valueOf(100L));
        }
        taxBox.setAmount(taxMultiplier);

        //Advanced settings
        if (calcContents.getPriceSymbol() != null && !calcContents.getPriceSymbol().trim().equals("")) {
            priceSymbolBox.setText(calcContents.getPriceSymbol());
        } else {
            priceSymbolBox.setText(FormatUtil.getInstance().getCurrencySymbol());
        }

        if (calcContents.getUnitSymbol() != null && !calcContents.getUnitSymbol().trim().equals("")) {
            unitSymbolBox.setText(calcContents.getUnitSymbol());
        } else {
            unitSymbolBox.setText(unitSymbol);
        }

        amountPrecisionBox.setValue(calcContents.getAmountRoundingPrecision());
        unitsPrecisionBox.setValue(calcContents.getUnitsRoundingPrecision());
        taxPrecisionBox.setValue(calcContents.getTaxRoundingPrecision());

        if (calcContents.getAmountRoundingMode() != null) {
            for(int i=0;i<amountRoundingModeBox.getItemCount();i++) {
                if (amountRoundingModeBox.getValue(i).equalsIgnoreCase(calcContents.getAmountRoundingMode().name())) {
                    amountRoundingModeBox.setSelectedIndex(i);
                    break;
                }
            }
        }
        if (calcContents.getUnitsRoundingMode() != null) {
            for(int i=0;i<unitsRoundingModeBox.getItemCount();i++) {
                if (unitsRoundingModeBox.getValue(i).equalsIgnoreCase(calcContents.getUnitsRoundingMode().name())) {
                    unitsRoundingModeBox.setSelectedIndex(i);
                    break;
                }
            }
        }
        if (calcContents.getTaxRoundingMode() != null) {
            for(int i=0;i<taxRoundingModeBox.getItemCount();i++) {
                if (taxRoundingModeBox.getValue(i).equalsIgnoreCase(calcContents.getTaxRoundingMode().name())) {
                    taxRoundingModeBox.setSelectedIndex(i);
                    break;
                }
            }
        }
        logger.info("Blocks: "+ calcContents.getBlocks());

        //blocks
        if(calcContents.getBlocks() != null) {
            blocksDataProvider.getList().clear();
            ArrayList<BlockDto> dtos = new ArrayList<BlockDto>(calcContents.getBlocks().size());
            List<UnitChargeData> uiCharges = null;
            List<UnitChargeData> blockCharges = null;
            ThresholdChargeData uiThresholdCharge = null;
            ThresholdChargeData blockThresholdCharge = null;
            for(int i=0; i<calcContents.getBlocks().size(); i++) {
                Block block = calcContents.getBlocks().get(i);
                // make a copy because celltable will modify underlying object which will modify template
                // and it must stay static so that if the form is cleared the template or tariff
                // can be referenced again unchanged.
                blockCharges = block.getUnitCharges();
                if (blockCharges != null && !blockCharges.isEmpty()) {
                    uiCharges = new ArrayList<>(blockCharges.size());
                    UnitChargeData uiCharge = null;
                    for (UnitChargeData charge : blockCharges) {
                        uiCharge = new UnitChargeData(charge.getName(), charge.isPercentage(), charge.getCharge(), charge.isTaxable(), charge.getCode());
                        uiCharges.add(uiCharge);
                    }
                }

                blockThresholdCharge = block.getThresholdCharge();
                if (blockThresholdCharge != null) {
                    uiThresholdCharge = new ThresholdChargeData(blockThresholdCharge.getName(), blockThresholdCharge.getCharge(),
                            blockThresholdCharge.isTaxable());
                }
                block = new Block(block.getUnitPrice(), block.getThreshold(), uiCharges, uiThresholdCharge, block.getCode(), block.getTaxCode(), block.isTaxable());
                dtos.add(new BlockDto(block, (i+1)));
                if(i == 0 && init) {
                    if (blockThresholdCharge != null) {
                        createBlockThresholdChargeColumns(blockThresholdCharge);
                    }
                    if (blockCharges != null && !blockCharges.isEmpty()) {
                        createBlockUnitChargeColumns(blockCharges, (blockThresholdCharge != null));
                    }
                }
            }
            blocksDataProvider.getList().addAll(dtos);
        }
        initDefaultTableRows();

        //discounts
        payTypeDiscountPanel.setCalcContentsPayTypeDiscount(calcContents.getPayTypeDiscounts());
    }

    private void createBlockThresholdChargeColumns(final ThresholdChargeData thresholdCharge) {
        createBlockThresholdChargeColumns(blocksTable, thresholdCharge, DEFAULT_BLOCKS_COLUMNS_COUNT);
    }

    private void createBlockUnitChargeColumns(final List<UnitChargeData> blockUnitCharges, final boolean hasThresholdCharge) {
        createBlockUnitChargeColumns(blocksTable, blockUnitCharges, DEFAULT_BLOCKS_COLUMNS_COUNT, hasThresholdCharge);
    }

    private void initDefaultTableRows() {
        int count = blocksDataProvider.getList().size();
        int allowedBlocks = template != null ? template.getBlockCalcContents().getBlocks().size() : MeterMngStatics.ALLOWED_BLOCKS;
        logger.info("Current blocks=" + count + ", allowedBlocks=" + allowedBlocks);
        if (count < allowedBlocks) {
            Block uiBlock = null;
            Block missingBlock = null;
            List<UnitChargeData> uiCharges = null;
            List<UnitChargeData> blockCharges = null;
            ThresholdChargeData uiThresholdCharge = null;
            ThresholdChargeData blockThresholdCharge = null;
            for (int i=count;i<allowedBlocks;i++) {
                if (template != null) {
                    missingBlock = template.getBlockCalcContents().getBlocks().get(i);
                    blockCharges = missingBlock.getUnitCharges();
                    if (blockCharges != null && !blockCharges.isEmpty()) {
                        uiCharges = new ArrayList<>(blockCharges.size());
                        UnitChargeData uiCharge = null;
                        for (UnitChargeData charge : blockCharges) {
                            uiCharge = new UnitChargeData(charge.getName(), charge.isPercentage(), charge.getCharge(), charge.isTaxable(), charge.getCode());
                            uiCharges.add(uiCharge);
                        }
                    }

                    blockThresholdCharge = missingBlock.getThresholdCharge();
                    if (blockThresholdCharge != null) {
                        uiThresholdCharge = new ThresholdChargeData(blockThresholdCharge.getName(), blockThresholdCharge.getCharge(),
                                blockThresholdCharge.isTaxable());
                    }

                    uiBlock = new Block(missingBlock.getUnitPrice(), missingBlock.getThreshold(), uiCharges, uiThresholdCharge, missingBlock.getCode(), missingBlock.getTaxCode(), missingBlock.isTaxable());
                } else {
                    uiBlock = new Block();
                }
                blocksDataProvider.getList().add(new BlockDto(uiBlock, (i+1)));
            }
        }
    }

    @Override
    public boolean tariffDataRequired() {
        return true;
    }

    @Override
    public ITariffData getTariffData() {
        boolean valid = true;
        clearErrors();

        BigDecimal freeUnits = null;
        try {
            if(freeUnitsTextBox.getValue() != null) {
                if(freeUnitsTextBox.getValue().compareTo(BigDecimal.ZERO) < 0) {
                    valid = false;
                    freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.freeunits.positive"));
                }
                freeUnits = freeUnitsTextBox.getValue();
                if (ValidateUtil.getNumberOfDecimalPlaces(freeUnits) > 1) {
                    valid = false;
                    freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.freeunits.decimal.limit"));
                }
            }
        } catch (Exception e) {
            valid = false;
            freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }

        String freeUnitsName = freeUnitsNameTextBox.getText();
        boolean hasFreeUnitsName = false;
        if (freeUnitsName != null && !freeUnitsName.trim().isEmpty()) {
            hasFreeUnitsName = true;
        }
        if (hasFreeUnitsName && freeUnits == null) {
            valid = false;
            freeUnitsElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
        } else if (!hasFreeUnitsName && freeUnits != null) {
            valid = false;
            freeUnitsNameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
        }
        BigDecimal bsstChargeAmt = null;
        try {
            if(bsstChargeFormRowPanel.isAttached() && bsstChargeFormRowPanel.isVisible() && bsstChargeAmountBox.getValue() != null) {
                if(bsstChargeAmountBox.getValue().compareTo(BigDecimal.ZERO) < 0) {
                    valid = false;
                    bsstChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.bsst.charge.positive"));
                }
                bsstChargeAmt = bsstChargeAmountBox.getValue();
            }
        } catch (Exception e) {
            valid = false;
            bsstChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.numeric.value"));
        }
        String bsstChargeName = null;
        if(template.isEnableBsstChargeName()) {
            bsstChargeName = bsstChargeNameTextBox.getText();
            boolean hasBsstChargeNameName = false;
            if (bsstChargeName != null && !bsstChargeName.trim().isEmpty()) {
                hasBsstChargeNameName = true;
            }
            if (hasBsstChargeNameName && bsstChargeAmt == null) {
                valid = false;
                bsstChargeElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
            } else if (!hasBsstChargeNameName && bsstChargeAmt != null) {
                valid = false;
                bsstChargeNameElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.field.required"));
            }
        }

        BsstAllowance bsstAllowance = null;
        if (freeUnits != null && hasFreeUnitsName) {
            bsstAllowance = new BsstAllowance();
            bsstAllowance.setDescription(freeUnitsName.trim());
            bsstAllowance.setUnits(freeUnits);
            if (bsstChargeAmt != null) {
                bsstAllowance.setBsstCharge(bsstChargeAmt);
                bsstAllowance.setBsstChargeName(bsstChargeName);
            }
        }

        //Tax
        BigDecimal tax = taxBox.getAmount();
        BigDecimal taxMultiplier = null;
        if (tax == null) {
            valid = false;
            taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.no.tax"));
        } else if (tax.doubleValue() < 0.0) {
            valid = false;
            taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.tax.positive"));
        } else {
            try {
                // taxMultiplier is 1 + (percent / 100) eg. 14% = 1.14
                // more efficient to do this calculation here than to store 14% and have the tariff calculator do this calculation on every vend
                taxMultiplier = BigDecimal.ONE.add(tax.divide(BigDecimal.valueOf(100L)));
            } catch (Exception e) {
                valid = false;
                taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.numeric.value"));
            }
        }

        // minVendAmount
        BigDecimal minVendAmt = minVendAmountBox.getValue();
        if (minVendAmt != null) {
            if (minVendAmt.compareTo(BigDecimal.ZERO) < 0) {
                valid = true;
                minVendAmountElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.positive.or.zero"));
            }
        }

        //Advanced settings
        boolean advValid = true;
        if (priceSymbolBox.getText() == null || priceSymbolBox.getText().trim().equals("")) {
            valid = false;
            advValid = false;
            priceSymbolElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.field.pricesymbol"));
        }
        if (unitSymbolBox.getText() == null || unitSymbolBox.getText().trim().equals("")) {
            valid = false;
            advValid = false;
            unitSymbolElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.field.unitsymbol"));
        }
        if (amountPrecisionBox.getValue() == null || amountPrecisionBox.getValue().intValue() < 0) {
            valid = false;
            advValid = false;
            amountPrecisionElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.field.amountprecision"));
        }
        if (taxPrecisionBox.getValue() == null || taxPrecisionBox.getValue().intValue() < 0) {
            valid = false;
            advValid = false;
            taxPrecisionElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.field.taxprecision"));
        }
        if (!advValid) {
            logger.info("Opening advanced disclosure panel");
            advancedPanel.setOpen(true);
        }

        RoundingMode roundingMode = null;
        int index = amountRoundingModeBox.getSelectedIndex();
        if (index > -1) {
            roundingMode = getRoundingMode(amountRoundingModeBox.getValue(index));
        }
        if (roundingMode == null) {
            valid = false;
            amountRoundingModeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.field.amountrounding"));
        }

        roundingMode = null;
        index = unitsRoundingModeBox.getSelectedIndex();
        if (index > -1) {
            roundingMode = getRoundingMode(unitsRoundingModeBox.getValue(index));
        }
        if (roundingMode == null) {
            valid = false;
            unitsRoundingModeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.field.unitsrounding"));
        }

        roundingMode = null;
        index = taxRoundingModeBox.getSelectedIndex();
        if (index > -1) {
            roundingMode = getRoundingMode(taxRoundingModeBox.getValue(index));
        }
        if (roundingMode == null) {
            valid = false;
            taxRoundingModeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.field.taxrounding"));
        }

        //Blocks
        boolean validBlockUnitCharges = true;
        boolean validBlockThresholdCharges = true;
        boolean validBlocks = true;
        int count = 0;
        for(BlockDto b : blocksDataProvider.getList()) {
            if ((b.getBlock().getUnitPrice() == null && b.getBlock().getThreshold() != null)) {
                valid = false;
                validBlocks = false;
                blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.incomplete"));
                break;
            } else if (b.getBlock().getUnitPrice() != null) {
                ViewData viewData = null;
                if (b.getBlock().getUnitPrice().doubleValue() < 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error"));
                    break;
                } else if (count > 0 && b.getBlock().getUnitPrice().compareTo(BigDecimal.ZERO) == 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.zero.error"));
                    break;
                } else if (b.getBlock().getThreshold() != null && b.getBlock().getThreshold().doubleValue() < 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error"));
                    break;
                } else if ((viewData = ((DecimalInputCell) (blocksTable.getColumn(1).getCell())).getViewData(b)) != null
                        && viewData.isInvalid()
                        || (viewData = ((DecimalInputCell) (blocksTable.getColumn(2).getCell())).getViewData(b)) != null
                                && viewData.isInvalid()) {
                    valid = validBlocks = false;
                    break;
                } else {
                    count++;
                }
            }
        }

        // Validate Block UnitCharges
        List<UnitChargeData> charges = null;
        Map<String, List<UnitChargeData>> blockUnitChargeMap = new HashMap<>();
        for(BlockDto b : blocksDataProvider.getList()) {
            if (b.getBlock().getUnitCharges() != null && !b.getBlock().getUnitCharges().isEmpty()) {
                for (UnitChargeData unitChargeData : b.getBlock().getUnitCharges()) {
                    if (b.getBlock().getUnitPrice() != null) {
                        charges = blockUnitChargeMap.get(unitChargeData.getName());
                        if (charges == null) {
                            charges = new ArrayList<>();
                            blockUnitChargeMap.put(unitChargeData.getName(), charges);
                            charges.add(unitChargeData);
                        } else {
                            charges.add(unitChargeData);
                        }
                    } else if (b.getBlock().getUnitPrice() == null && unitChargeData.getCharge() != null) {
                        valid = false;
                        validBlocks = false;
                        validBlockUnitCharges = false;
                        blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.unitcharge.error.incomplete"));
                        break;
                    }
                }
                if (!validBlockUnitCharges) {
                    break;
                }
            }
        }
        // Validate all UnitCharges are set OR all are empty.
        if (validBlockUnitCharges) {
            for (Map.Entry<String, List<UnitChargeData>> dataList : blockUnitChargeMap.entrySet()) {
                UnitChargeData currentCharge = null;
                UnitChargeData periousCharge = null;
                List<UnitChargeData> mapCharges = dataList.getValue();
                for (int i = 0; i < mapCharges.size(); i++) {
                    currentCharge = mapCharges.get(i);
                    if (i > 0) {
                        periousCharge = mapCharges.get(i-1);
                        if ((currentCharge.getCharge() == null && periousCharge.getCharge() != null) ||
                                (currentCharge.getCharge() != null && periousCharge.getCharge() == null)) {
                            valid = false;
                            validBlocks = false;
                            validBlockUnitCharges = false;
                            blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.unitcharge.error.empty"));
                            break;
                        }
                    }
                }
                if (!validBlockUnitCharges) {
                    break;
                }
            }
        }

        // Validate Block ThresholdCharge
        ThresholdChargeData thresholdCharge = null;
        List<ThresholdChargeData> thresholdCharges = null;
        Map<String, List<ThresholdChargeData>> blockThresholdChargeMap = new HashMap<>();
        for (BlockDto b : blocksDataProvider.getList()) {
            thresholdCharge = b.getBlock().getThresholdCharge();
            if (thresholdCharge != null) {
                if (b.getBlock().getUnitPrice() != null) {
                    thresholdCharges = blockThresholdChargeMap.get(b.getBlock().getThresholdCharge().getName());
                    if (thresholdCharges == null) {
                        thresholdCharges = new ArrayList<>();
                        blockThresholdChargeMap.put(thresholdCharge.getName(), thresholdCharges);
                        thresholdCharges.add(thresholdCharge);
                    } else {
                        thresholdCharges.add(thresholdCharge);
                    }
                } else if (b.getBlock().getUnitPrice() == null && thresholdCharge.getCharge() != null) {
                    valid = false;
                    validBlocks = false;
                    validBlockThresholdCharges = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.thresholdCharge.error.incomplete"));
                    break;
                }
            }
        }
        // Validate all ThresholdCharges are set OR all are empty.
        if (validBlockThresholdCharges) {
            for (Map.Entry<String, List<ThresholdChargeData>> dataList : blockThresholdChargeMap.entrySet()) {
                ThresholdChargeData currentCharge = null;
                ThresholdChargeData periousCharge = null;
                List<ThresholdChargeData> mapCharges = dataList.getValue();
                for (int i = 0; i < mapCharges.size(); i++) {
                    currentCharge = mapCharges.get(i);
                    if (i > 0) {
                        periousCharge = mapCharges.get(i-1);
                        if ((currentCharge.getCharge() == null && periousCharge.getCharge() != null) ||
                                (currentCharge.getCharge() != null && periousCharge.getCharge() == null)) {
                            valid = false;
                            validBlocks = false;
                            validBlockThresholdCharges = false;
                            blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.thresholdCharge.error.empty"));
                            break;
                        }
                    }
                }
                if (!validBlockThresholdCharges) {
                    break;
                }
            }
        }

        //Repack the blocks
        ArrayList<Block> newBlocks = new ArrayList<Block>();
        for(BlockDto b : blocksDataProvider.getList()) {
            if (b.getBlock().getUnitPrice() != null) {
                newBlocks.add(b.getBlock());
            }
        }
        //Get the last block with no threshold and check all the ones before that have a threshold set
        int lastIndex = newBlocks.size() - 1;
        for(int i=newBlocks.size()-1; i>=0; i--) {
            Block b = newBlocks.get(i);
            if (b.getUnitPrice() != null && b.getThreshold() == null) {
                if (i != lastIndex) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.last"));
                    break;
                }
            }
        }

        if (newBlocks.size() > 0) {
            Block lastBlock = newBlocks.get(newBlocks.size() - 1);
            if (lastBlock.getThreshold() != null) {
                valid = false;
                validBlocks = false;
                blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.last.none"));
            }
        }

        //now check that each succeeding threshold is > than one before
        BigDecimal previousThreshold = BigDecimal.ZERO;
        if (valid && validBlocks) {
            for(int i=0; i < newBlocks.size(); i++) {
                Block b = newBlocks.get(i);
                if (b.getThreshold() != null && b.getThreshold().compareTo(previousThreshold) < 1) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.increasing.thresholds"));
                    break;
                }
                previousThreshold = b.getThreshold();
            }
        }

        //Valid blocks?
        if (count == 0 && validBlocks) {
            valid = false;
            blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.none"));
        }

        //Discounts
        if (!payTypeDiscountPanel.isValid()) {
        	valid = false;
        }

        List<CyclicChargeData> cyclicCharges = new ArrayList<>(cyclicChargesPanel.getWidgetCount());
        for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
            CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(i);
            try {
                CyclicChargeData cyclicCharge = cyclicChargePanel.getCyclicCharge();
                // Charges not populated will be null
                if(cyclicCharge != null) {
                    cyclicCharges.add(cyclicCharge);
                }
            } catch (Exception e) {
                valid = false;
            }
        }

        List<PercentChargeData> percentCharges = new ArrayList<>(percentChargesPanel.getWidgetCount());
        for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
            PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(i);
            try {
                PercentChargeData percentCharge = percentChargePanel.getPercentCharge();
                // Charges not populated will be null
                if(percentCharge != null) {
                    percentCharges.add(percentCharge);
                }
            } catch (Exception e) {
                valid = false;
            }
        }

        List<UnitChargeData> unitCharges = new ArrayList<>(unitChargesPanel.getWidgetCount());
        for (int i = 0; i < unitChargesPanel.getWidgetCount(); i++) {
            UnitChargePanel unitChargePanel = (UnitChargePanel) unitChargesPanel.getWidget(i);
            try {
                UnitChargeData unitCharge = unitChargePanel.getUnitCharge();
                // Charges not populated will be null
                if(unitCharge != null) {
                    unitCharges.add(unitChargePanel.getUnitCharge());
                }
            } catch (Exception e) {
                valid = false;
            }
        }

        MeterDebt meterDebt = null;
        if (meterDebtChargesPanel != null && meterDebtChargesPanel.getWidgetCount() > 0) {
            MeterDebtChargePanel meterDebtChargePanel = (MeterDebtChargePanel) meterDebtChargesPanel.getWidget(0);
            meterDebt = meterDebtChargePanel.getMeterDebt();
        }

        //Valid input?
        if (!valid) {
            return null;
        } else {
            BlockCalcContents cc = new BlockCalcContents();
            cc.setBsstAllowance(bsstAllowance);
            cc.setTaxMultiplier(taxMultiplier);
            cc.setAmountRoundingMode( getRoundingMode(amountRoundingModeBox.getValue(amountRoundingModeBox.getSelectedIndex())) );
            cc.setAmountRoundingPrecision(amountPrecisionBox.getValue());
            cc.setPriceSymbol(priceSymbolBox.getText());
            cc.setUnitSymbol(unitSymbolBox.getText());
            cc.setTaxRoundingMode( getRoundingMode(taxRoundingModeBox.getValue(taxRoundingModeBox.getSelectedIndex())) );
            cc.setTaxRoundingPrecision(taxPrecisionBox.getValue());
            cc.setUnitsRoundingMode( getRoundingMode(unitsRoundingModeBox.getValue(unitsRoundingModeBox.getSelectedIndex())) );
            cc.setUnitsRoundingPrecision(MeterMngStatics.PRECISION_UNITS_STS);
            cc.setBlocks(newBlocks);
            cc.setMinVendAmount(minVendAmt);
            cc.setPayTypeDiscounts(payTypeDiscountPanel.getThisPayTypeDiscountsList());
            cc.setCyclicCharges(cyclicCharges);
            cc.setPercentCharges(percentCharges);
            cc.setUnitCharges(unitCharges);
            cc.setMeterDebt(meterDebt);
            cc.setTaxItemName(template.getBlockCalcContents().getTaxItemName());
            cc.setTaxAsSeparateItem(template.getBlockCalcContents().isTaxAsSeparateItem());
            cc.setTaxItemLoggedAsAmtTax(template.getBlockCalcContents().isTaxItemLoggedAsAmtTax());
            cc.setTotalUnitChargeRoundingMode(template.getBlockCalcContents().getTotalUnitChargeRoundingMode());
            cc.setTotalUnitChargeRoundingPrecision(template.getBlockCalcContents().getTotalUnitChargeRoundingPrecision());
            cc.setTaxFirst(template.getBlockCalcContents().isTaxFirst());
            cc.setRollUpAux((template.getBlockCalcContents().isRollUpAux()));
            cc.setTotalDebtName(template.getBlockCalcContents().getTotalDebtName());
            cc.setTotalRefundsName(template.getBlockCalcContents().getTotalRefundsName());
            cc.setReceiptTariffInCents(template.getBlockCalcContents().isReceiptTariffInCents());
            cc.setCentsSymbol(template.getBlockCalcContents().getCentsSymbol());
            cc.setAppendPercentToTaxName(template.getBlockCalcContents().isAppendPercentToTaxName());
            return cc;
        }

    }

    private RoundingMode getRoundingMode(String value) {
        for(RoundingMode m : RoundingMode.values()) {
            if (m.name().equalsIgnoreCase(value)) {
                return m;
            }
        }
        return null;
    }

    @Override
    public void clearErrors() {
        for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
            CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(0);
            cyclicChargePanel.clearErrors();
        }
        for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
            PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(0);
            percentChargePanel.clearErrors();
        }
        for (int i = 0; i < unitChargesPanel.getWidgetCount(); i++) {
            UnitChargePanel unitChargePanel = (UnitChargePanel) unitChargesPanel.getWidget(0);
            unitChargePanel.clearErrors();
        }
        freeUnitsNameElement.setErrorMsg(null);
        freeUnitsElement.setErrorMsg(null);
        bsstChargeNameElement.setErrorMsg(null);
        bsstChargeElement.setErrorMsg(null);
        taxElement.setErrorMsg(null);
        minVendAmountElement.setErrorMsg(null);
        blocksElement.setErrorMsg(null);
        priceSymbolElement.setErrorMsg(null);
        unitSymbolElement.setErrorMsg(null);
        unitsPrecisionElement.setErrorMsg(null);
        amountRoundingModeElement.setErrorMsg(null);
        amountPrecisionElement.setErrorMsg(null);
        unitsRoundingModeElement.setErrorMsg(null);
        unitsPrecisionElement.setErrorMsg(null);
        taxRoundingModeElement.setErrorMsg(null);
        taxPrecisionElement.setErrorMsg(null);
        payTypeDiscountPanel.clearErrors();
    }

    public void clearForm() {
        for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
            CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(0);
            cyclicChargePanel.clearForm();
        }
        for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
            PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(0);
            percentChargePanel.clearForm();
        }
        for (int i = 0; i < unitChargesPanel.getWidgetCount(); i++) {
            UnitChargePanel unitChargePanel = (UnitChargePanel) unitChargesPanel.getWidget(0);
            unitChargePanel.clearForm();
        }

        taxBox.setAmount(null);
        minVendAmountBox.setValue(null);
        freeUnitsNameTextBox.setText(null);
        freeUnitsTextBox.setValue(null);
        bsstChargeAmountBox.setValue(null);
        priceSymbolBox.setText(FormatUtil.getInstance().getCurrencySymbol());
        unitSymbolBox.setText(unitSymbol);
        setDefaultRoundingMode(amountRoundingModeBox, DEFAULT_AMOUNT_ROUNDING_MODE);
        amountPrecisionBox.setValue(DEFAULT_AMOUNT_ROUNDING_PRECISION);
        setDefaultRoundingMode(unitsRoundingModeBox, DEFAULT_UNITS_ROUNDING_MODE);
        unitsPrecisionBox.setValue(DEFAULT_UNITS_ROUNDING_PRECISION);
        setDefaultRoundingMode(taxRoundingModeBox, DEFAULT_TAX_ROUNDING_MODE);
        taxPrecisionBox.setValue(DEFAULT_TAX_ROUNDING_PRECISION);
        blocksDataProvider.getList().clear();
        blocksDataProvider.flush();
        clearErrors();
        setTariffInitData(tariffInitData);
    }

    private void setDefaultRoundingMode(ListBox box, RoundingMode roundingMode) {
        for(int i=0;i<box.getItemCount();i++) {
            if (box.getValue(i).equalsIgnoreCase(roundingMode.name())) {
                box.setSelectedIndex(i);
                return;
            }
        }
        box.setSelectedIndex(0); //wasn't found
    }

    @Override
    protected void addFieldHandlers() {
        taxBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        minVendAmountBox.addChangeHandler(new FormDataChangeHandler(form));
        freeUnitsNameTextBox.addChangeHandler(new FormDataChangeHandler(form));
        freeUnitsTextBox.addChangeHandler(new FormDataChangeHandler(form));
        bsstChargeAmountBox.addChangeHandler(new FormDataChangeHandler(form));
        priceSymbolBox.addChangeHandler(new FormDataChangeHandler(form));
        unitSymbolBox.addChangeHandler(new FormDataChangeHandler(form));
        amountPrecisionBox.addChangeHandler(new FormDataChangeHandler(form));
        amountRoundingModeBox.addChangeHandler(new FormDataChangeHandler(form));
        unitsPrecisionBox.addChangeHandler(new FormDataChangeHandler(form));
        unitsRoundingModeBox.addChangeHandler(new FormDataChangeHandler(form));
        taxPrecisionBox.addChangeHandler(new FormDataChangeHandler(form));
        taxRoundingModeBox.addChangeHandler(new FormDataChangeHandler(form));

        for (int i = 0; i < cyclicChargesPanel.getWidgetCount(); i++) {
            CyclicChargePanel cyclicChargePanel = (CyclicChargePanel) cyclicChargesPanel.getWidget(i);
            cyclicChargePanel.addFieldHandlers(form);
        }

        for (int i = 0; i < percentChargesPanel.getWidgetCount(); i++) {
            PercentChargePanel percentChargePanel = (PercentChargePanel) percentChargesPanel.getWidget(i);
            percentChargePanel.addFieldHandlers(form);
        }

        for (int i = 0; i < unitChargesPanel.getWidgetCount(); i++) {
            UnitChargePanel unitChargePanel = (UnitChargePanel) unitChargesPanel.getWidget(i);
            unitChargePanel.addFieldHandlers(form);
        }

    }

}
