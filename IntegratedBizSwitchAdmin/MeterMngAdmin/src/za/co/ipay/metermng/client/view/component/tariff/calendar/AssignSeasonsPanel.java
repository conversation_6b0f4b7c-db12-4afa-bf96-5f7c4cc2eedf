package za.co.ipay.metermng.client.view.component.tariff.calendar;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.SeasonAssignedEvent;
import za.co.ipay.metermng.client.event.SeasonsUpdatedEvent;
import za.co.ipay.metermng.client.event.SeasonsUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.TouSeasonDate;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouSeasonDateData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.CalendarModel;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

public class AssignSeasonsPanel extends BaseComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField(provided=true) CellTable<TouSeasonDateData> assignSeasonsTable;
    @UiField TablePager assignSeasonsPager;
    @UiField Label formHeading;
    @UiField IpayListBox lstbxSeason;
    @UiField ListBox startDayListBox;
    @UiField ListBox startMonthListBox;
    @UiField ListBox endDayListBox;
    @UiField ListBox endMonthListBox;
    @UiField FlowPanel formPanel;
    @UiField FormElement startDatesElement;
    @UiField FormElement seasonElement;
    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField Button btnDelete;
    @UiField Anchor anchrNewSeason;

    private ListDataProvider<TouSeasonDateData> dataProvider;
    private SingleSelectionModel<TouSeasonDateData> selectionModel;
    private TouCalendarData calendarData;
    private TouSeasonDateData seasonDateData;

    private ArrayList<TouSeasonDateData> seasonDatesList = new ArrayList<TouSeasonDateData>();
    private ArrayList<Integer> alreadyAssignedMonths = new ArrayList<Integer>(12);

    private CalendarModel calmod;
    private ListHandler<TouSeasonDateData> columnSortHandler;

    private TextColumn<TouSeasonDateData> seasonName;
    private TextColumn<TouSeasonDateData> startColumn;
    private TextColumn<TouSeasonDateData> endColumn;

    private CalendarContainer parentContainer;

    protected HasDirtyData hasDirtyData;
    protected boolean readOnly = false;

    private static Logger logger = Logger.getLogger(AssignSeasonsPanel.class.getName());

    private static SeasonsPanelUiBinder uiBinder = GWT.create(SeasonsPanelUiBinder.class);

    interface SeasonsPanelUiBinder extends UiBinder<Widget, AssignSeasonsPanel> {
    }

    public AssignSeasonsPanel(ClientFactory clientFactory, CalendarContainer parent) {
        this.clientFactory = clientFactory;
        this.parentContainer = parent;
        calmod = new CalendarModel();
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        this.hasDirtyData = ((Workspace) parent).createAndRegisterHasDirtyData();
        initTable();
        populateDateListBoxes();
        populateSeasonsBox();
        clientFactory.getEventBus().addHandler(SeasonsUpdatedEvent.TYPE, new SeasonsUpdatedEventHandler() {

            @Override
            public void processSeasonsUpdatedEvent(SeasonsUpdatedEvent event) {
                populateSeasonsBox();
            }
        });
        setSeasonDate(null);
        addFieldHandlers();
    }

    private void populateSeasonsBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                lstbxSeason.setLookupItems(result);
            }

        };
        if (clientFactory != null) {
            lstbxSeason.clear();
            clientFactory.getLookupRpc().getTouSeasonLookupList(lookupSvcAsyncCallback);
        }
    }

    private void populateDateListBoxes() {
        startMonthListBox.clear();
        startDayListBox.clear();
        endDayListBox.clear();
        endMonthListBox.clear();

        for (int i=0; i<12; i++) {

                startMonthListBox.addItem(calmod.formatMonth(i), String.valueOf(i+1));
                endMonthListBox.addItem(calmod.formatMonth(i), String.valueOf(i+1));

        }
        for (int i=1; i<32; i++) {
            startDayListBox.addItem(String.valueOf(i), String.valueOf(i));
            endDayListBox.addItem(String.valueOf(i), String.valueOf(i));
        }

        setDateFieldsToNextAvailableDate();
    }



    protected void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            assignSeasonsTable = new CellTable<TouSeasonDateData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            assignSeasonsTable = new CellTable<TouSeasonDateData>(DEFAULT_PAGE_SIZE);
        }
    }

    public void setTouCalendarData(TouCalendarData calendarData) {
        this.calendarData = calendarData;
        if (calendarData == null || calendarData.getId()==null) {
            clearFields();
            seasonDatesList.clear();
            dataProvider.setList(seasonDatesList);
            dataProvider.refresh();
        } else {
            refreshTable(calendarData);
        }
    }

   protected void initTable() {
        if (dataProvider == null) {
            seasonName = new TextColumn<TouSeasonDateData>() {
                @Override
                public String getValue(TouSeasonDateData data) {
                    if (data.getSeason() != null && data.getSeason().getName() != null) {
                        return data.getSeason().getName();
                    }
                    return " ? ";
                }
            };
            seasonName.setSortable(true);

            startColumn = new TextColumn<TouSeasonDateData>() {
                @Override
                public String getValue(TouSeasonDateData data) {
                    if (data.getStartDay() != null && data.getStartMonth() != null) {
                        return getStartDateString(false, data);
                    }
                    return " ? ";
                }


            };
            startColumn.setSortable(true);

            endColumn = new TextColumn<TouSeasonDateData>() {
                @Override
                public String getValue(TouSeasonDateData data) {
                    if (data.getEndDay() != null && data.getEndMonth() != null) {
                        return getEndDateString(false, data);
                    }
                    return " ? ";
                }
            };
            endColumn.setSortable(true);

            // Add the columns.
            assignSeasonsTable.addColumn(seasonName, MessagesUtil.getInstance().getMessage("calendar.assign.season"));
            assignSeasonsTable.addColumn(startColumn, MessagesUtil.getInstance().getMessage("calendar.assign.season.start"));
            assignSeasonsTable.addColumn(endColumn, MessagesUtil.getInstance().getMessage("calendar.assign.season.end"));


            dataProvider = new ListDataProvider<TouSeasonDateData>();
            dataProvider.addDataDisplay(assignSeasonsTable);
            assignSeasonsPager.setDisplay(assignSeasonsTable);
            assignSeasonsTable.setPageSize(getPageSize());


            logger.info("Created Period table");
        }

        selectionModel = new SingleSelectionModel<TouSeasonDateData>();
        CellPreviewEvent.Handler<TouSeasonDateData> handler = new CellPreviewEvent.Handler<TouSeasonDateData>() {
            final CellPreviewEvent.Handler<TouSeasonDateData> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<TouSeasonDateData> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (hasDirtyData.isDirtyData()) {
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    hasDirtyData.setDirtyData(false);
                                                    event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                    logger.info("Discarding changes for selection event");
                                                } else {
                                                    event.setCanceled(true);
                                                    logger.info("Cancelled selection event, staying with current selection");
                                                }

                                            }});
                            } else {
                                selectionEventManager.onCellPreview(event);
                            }

                        }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }
        };
        assignSeasonsTable.setSelectionModel(selectionModel, handler);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final TouSeasonDateData selected = selectionModel.getSelectedObject();
                if (selected != null) {
                    setSeasonDate(selected);
                    assignSeasonsTable.getSelectionModel().setSelected(selected, true);
                }
            }
        });
    }

    public void clearTableSelection() {
        if (selectionModel != null) {
            TouSeasonDateData selected = selectionModel.getSelectedObject();
            if (selected != null) {
                selectionModel.setSelected(selected, false);
            }
        }
    }

    public void refreshTable() {
        clientFactory.getCalendarRpc().getSeasonDates(calendarData.getId(), new ClientCallback<ArrayList<TouSeasonDateData>>() {
            @Override
            public void onSuccess(ArrayList<TouSeasonDateData> result) {
                calendarData.setAssignedSeasons(result);
                refreshTable(calendarData);
            }
        });
    }
    public void refreshTable(TouCalendarData calendarData) {
        ArrayList<TouSeasonDateData> result = calendarData.getAssignedSeasons();
        logger.info("Got season dates: " + result.size());
        if (dataProvider != null && dataProvider.getList() != null) {
            dataProvider.getList().clear();
            dataProvider.getList().addAll(result);
            setSeasonDatesList(result);
            parentContainer.setAssignSeasonsComplete(isYearComplete());
        }
    }

    public void setSeasonDatesList(ArrayList<TouSeasonDateData> thedata) {
        logger.info("Displaying Season Dates: "+thedata.size());
        seasonDatesList = thedata;
        calendarData.setAssignedSeasons(thedata);
        dataProvider.getList().clear();
        dataProvider.getList().addAll(thedata);
        dataProvider.refresh();

        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<TouSeasonDateData>(dataProvider.getList());
            columnSortHandler.setComparator(seasonName, new Comparator<TouSeasonDateData>() {
                public int compare(TouSeasonDateData o1, TouSeasonDateData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getSeason().getName() != null) {
                        return (o2 != null && o2.getSeason().getName() != null) ? o1.getSeason().getName().compareTo(o2.getSeason().getName()) : 1;
                    }
                    return -1;
                }
            });

            columnSortHandler.setComparator(startColumn, new Comparator<TouSeasonDateData>() {
                public int compare(TouSeasonDateData o1, TouSeasonDateData o2) {
                    int r = 0;
                    if (o1 == o2) {
                        return r;
                    }
                    if (o1 != null && o1.getStartMonth() != null) {
                        r = (o2 != null && o2.getStartMonth() != null) ? o1.getStartMonth().compareTo(o2.getStartMonth()) : 1;
                    }
                    if (r == 0) {
                        r = (o2 != null && o2.getStartDay() != null) ? o1.getStartDay().compareTo(o2.getStartDay()) : 1;
                    }
                    return r;
                }
            });

            columnSortHandler.setComparator(endColumn, new Comparator<TouSeasonDateData>() {
                public int compare(TouSeasonDateData o1, TouSeasonDateData o2) {
                    int r = 0;
                    if (o1 == o2) {
                        return r;
                    }
                    if (o1 != null && o1.getEndMonth() != null) {
                        r = (o2 != null && o2.getEndMonth() != null) ? o1.getEndMonth().compareTo(o2.getEndMonth()) : 1;
                    }
                    if (r == 0) {
                        r = (o2 != null && o2.getEndDay() != null) ? o1.getEndDay().compareTo(o2.getEndDay()) : 1;
                    }
                    return r;
                }
            });

            alreadyAssignedMonths.clear();
            for (int i=0; i<seasonDatesList.size();i++) {
                alreadyAssignedMonths.addAll(seasonDatesList.get(i).getCompletemonths());
            }
            populateDateListBoxes();
            assignSeasonsTable.addColumnSortHandler(columnSortHandler);
            assignSeasonsTable.getColumnSortList().push(startColumn);
            ColumnSortEvent.fire(assignSeasonsTable, assignSeasonsTable.getColumnSortList());

        } else {
            alreadyAssignedMonths.clear();
            for (int i=0; i<seasonDatesList.size();i++) {
                alreadyAssignedMonths.addAll(seasonDatesList.get(i).getCompletemonths());
            }
            populateDateListBoxes();
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(assignSeasonsTable, assignSeasonsTable.getColumnSortList());
        }
        assignSeasonsTable.setPageStart(0);
    }

    private void setSeasonDate(TouSeasonDateData theseasondate) {
        clearErrors();
        clearFields();
        this.seasonDateData = theseasondate;
        if (theseasondate != null) {
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.update"));
            btnDelete.setVisible(true);
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.assign.season.form.heading"));
            lstbxSeason.selectItemByValue(String.valueOf(seasonDateData.getTouSeasonId()));
            startMonthListBox.setSelectedIndex(seasonDateData.getStartMonth()-1);
            endMonthListBox.setSelectedIndex(seasonDateData.getEndMonth()-1);
            onStartMonthChange();
            onEndMonthChange();
            startDayListBox.setSelectedIndex(seasonDateData.getStartDay()-1);
            endDayListBox.setSelectedIndex(seasonDateData.getEndDay()-1);

        } else {
            seasonDateData = new TouSeasonDateData(new TouSeasonDate());
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.create"));
            btnDelete.setVisible(false);
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.assign.season.form.heading"));
            clearTableSelection();
            hasDirtyData.setDirtyData(false);
        }
    }

    private void setDateFieldsToNextAvailableDate() {
        int lastEndMonth=1;
        int lastEndDay=1;
        if ( (seasonDatesList == null || seasonDatesList.isEmpty())
                || isYearComplete()) {
            startMonthListBox.setSelectedIndex(0);
            endMonthListBox.setSelectedIndex(0);
            onStartMonthChange();
            onEndMonthChange();
            startDayListBox.setSelectedIndex(0);
            endDayListBox.setSelectedIndex(0);
            return;
        }
        for (TouSeasonDateData tsdd : seasonDatesList) {
            if (tsdd.getEndMonth() > lastEndMonth) {
                lastEndMonth = tsdd.getEndMonth();
                lastEndDay = tsdd.getEndDay();
            } else if (tsdd.getEndMonth()==lastEndMonth && tsdd.getEndDay() > lastEndDay) {
                lastEndDay = tsdd.getEndDay();
            }
        }

        if (lastEndDay == getLastDayOfMonth(lastEndMonth)) {
            if (lastEndMonth==12) {
                lastEndDay = 1;
                lastEndMonth = 1;
            } else {
                lastEndDay = 1;
                lastEndMonth++;
            }
        }
        startMonthListBox.setSelectedIndex(lastEndMonth-1);
        endMonthListBox.setSelectedIndex(lastEndMonth-1);
        onStartMonthChange();
        onEndMonthChange();
        startDayListBox.setSelectedIndex(lastEndDay);
        endDayListBox.setSelectedIndex(lastEndDay);
    }

    public void clearFields() {
        lstbxSeason.clearSelections();
        startDayListBox.setSelectedIndex(0);
        startMonthListBox.setSelectedIndex(0);
        endDayListBox.setSelectedIndex(0);
        endMonthListBox.setSelectedIndex(0);
    }

    public void clearErrors() {
        seasonElement.clearErrorMsg();
        startDatesElement.clearErrorMsg();
    }

    @UiHandler("startMonthListBox")
    public void onStartMonthChange(ChangeEvent event) {
        onStartMonthChange();
    }

    //returns last day of month
    private void onStartMonthChange() {

        int s = getLastDayOfMonth(Integer.valueOf(startMonthListBox.getValue(startMonthListBox.getSelectedIndex())));
        startDayListBox.clear();
        for (int i=0; i<s; i++) {
            startDayListBox.addItem(String.valueOf(i+1), String.valueOf(i+1));
        }
    }

    @UiHandler("endMonthListBox")
    public void onEndMonthChange(ChangeEvent event) {
        onEndMonthChange();
    }

    public void onEndMonthChange() {
        int e = getLastDayOfMonth(Integer.valueOf(endMonthListBox.getValue(endMonthListBox.getSelectedIndex())));
        endDayListBox.clear();
        for (int i=0; i<e; i++) {
            endDayListBox.addItem(String.valueOf(i+1), String.valueOf(i+1));
        }
    }


    private int getLastDayOfMonth(int month) {
        int e = 31;
        if (month == 4
                || month == 6
                || month == 9
                || month == 11) {
            e=30;
        } else if (month == 2) {
            e=29;
        }
        return e;
    }


    @UiHandler("btnSave")
    void save(ClickEvent e) {
        if (readOnly) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.readOnly", new String[] {parentContainer.getPricingStructureNames()}), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft()+btnSave.getOffsetWidth(), btnSave.getAbsoluteTop()-btnSave.getOffsetHeight(), null);
            return;
        }
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (seasonDateData.getId() == null) {
                    addSeasonDate();
                } else {
                    updateSeasonDate();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    void cancel(ClickEvent e) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        setSeasonDate(null);
                    }
                }});
        } else {
            setSeasonDate(null);
        }
    }

    @UiHandler("btnDelete")
    void delete(ClickEvent e) {
        deleteSeasonDate();
        hasDirtyData.setDirtyData(false);
    }

    @UiHandler("anchrNewSeason")
    void handleSeasonAnchor(ClickEvent e) {
        PopupPanel simplePopup = new PopupPanel(false);
        simplePopup.setGlassEnabled(true);
        simplePopup.ensureDebugId("SeasonsAnchorPopup");
        SeasonsPanel seasonsPanel = new SeasonsPanel(clientFactory, new LocalOnlyHasDirtyData(), simplePopup);
        seasonsPanel.refreshTable();
        simplePopup.setWidget(seasonsPanel);
        simplePopup.center();
        simplePopup.show();
    }


    public String getStartDateString(boolean monthFirst, TouSeasonDate touSeasonDate) {
        if (touSeasonDate.getStartMonth() == null || touSeasonDate.getStartDay() == null) {
            return null;
        }
        if (monthFirst) {
            return (calmod.formatMonth(touSeasonDate.getStartMonth())+" "+touSeasonDate.getStartDay());
        } else {
            return (touSeasonDate.getStartDay()+" "+calmod.formatMonth(touSeasonDate.getStartMonth()-1));//we use 1 for jan calmod uses 0
        }
    }

    public String getEndDateString(boolean monthFirst, TouSeasonDate touSeasonDate) {
        if (touSeasonDate.getEndMonth() == null || touSeasonDate.getEndDay() == null) {
            return null;
        }
        if (monthFirst) {
            return (calmod.formatMonth(touSeasonDate.getEndMonth())+" "+touSeasonDate.getEndDay());
        } else {
            return (touSeasonDate.getEndDay()+" "+calmod.formatMonth(touSeasonDate.getEndMonth()-1));//we use 1 for jan calmod uses 0
        }
    }
    private void update() {
        if (seasonDateData == null) {
            this.seasonDateData = new TouSeasonDateData();
        }
        seasonDateData.setTouCalendarId(calendarData.getId());
        seasonDateData.setTouSeasonId(Long.valueOf(lstbxSeason.getValue(lstbxSeason.getSelectedIndex())));
        seasonDateData.setStartDay(Integer.valueOf(startDayListBox.getValue(startDayListBox.getSelectedIndex())));
        seasonDateData.setStartMonth(Integer.valueOf(startMonthListBox.getValue(startMonthListBox.getSelectedIndex())));
        seasonDateData.setEndMonth(Integer.valueOf(endMonthListBox.getValue(endMonthListBox.getSelectedIndex())));
        seasonDateData.setEndDay(Integer.valueOf(endDayListBox.getValue(endDayListBox.getSelectedIndex())));
    }

    private boolean isValid() {
        boolean valid = true;
        clearErrors();

        if (seasonDateData.getTouSeasonId() <= 0) {
            logger.info("season not selected");
            seasonElement.showErrorMsg(MessagesUtil.getInstance().getMessage("calendar.assign.season.error.select.season"));
            valid = false;
        }

        // check if all there
        if (seasonDateData.getStartMonth().equals(seasonDateData.getEndMonth())) {
            if (seasonDateData.getStartDay() > seasonDateData.getEndDay()) {
                logger.info("season date end before start");
                startDatesElement.showErrorMsg(MessagesUtil.getInstance().getMessage("calendar.assign.season.error.end.before.start"));
                valid = false;
            }
        } else if (seasonDateData.getStartMonth() > seasonDateData.getEndMonth()) {
            logger.info("season date end month before start month");
            startDatesElement.showErrorMsg(MessagesUtil.getInstance().getMessage("calendar.assign.season.error.end.before.start"));
            valid = false;
        }
        if (isDateInExistingDates(seasonDateData.getStartMonth(), seasonDateData.getStartDay(), seasonDateData.getId())
                    || (isDateInExistingDates(seasonDateData.getEndMonth(), seasonDateData.getEndDay(), seasonDateData.getId()))
                ) {
            logger.info("season date already assigned");
            startDatesElement.showErrorMsg(MessagesUtil.getInstance().getMessage("calendar.assign.season.error.date.already.assigned"));
            valid = false;
        }
        return valid;
    }

    public void addSeasonDate() {
        update();
        if (isValid()) {
            clientFactory.getCalendarRpc().addSeasonDate(seasonDateData, new ClientCallback<TouSeasonDateData>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouSeasonDateData result) {
                    if (result != null) {
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("calendar.assign.season.title") }), MediaResourceUtil.getInstance().getInformationIcon(), btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                        seasonDatesList.add(result);
                        dataProvider.setList(seasonDatesList);
                        dataProvider.refresh();
                        setSeasonDate(null);
                        clientFactory.getEventBus().fireEvent(new SeasonAssignedEvent());
                    }
                }
            });
        }
    }

    public void updateSeasonDate() {
        update();
        if (isValid()) {
            clientFactory.getCalendarRpc().updateSeasonDate(seasonDateData, new ClientCallback<TouSeasonDateData>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouSeasonDateData result) {
                    if (result != null) {
                        int index = -1;
                        for (int i = 0; i < seasonDatesList.size(); i++) {
                            if (seasonDatesList.get(i).getId().equals(result.getId())) {
                                index = i;
                                break;
                            }
                        }
                        if (index > -1) {
                            setSeasonDate(null);
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("calendar.assign.season.title") }), MediaResourceUtil.getInstance().getInformationIcon(), btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                            seasonDatesList.set(index, result);
                            dataProvider.setList(seasonDatesList);
                            dataProvider.refresh();
                            clientFactory.getEventBus().fireEvent(new SeasonAssignedEvent());
                        }
                    }
                }
            });
        }
    }

    public void deleteSeasonDate() {
        //ToDo double check with popup
        final int aleft = btnDelete.getAbsoluteLeft();
        final int atop = btnDelete.getAbsoluteTop();
        Dialogs.confirm(
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.delete"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.negative"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            hasDirtyData.setDirtyData(false);
                            boolean deleteCalendarSeason = true;
                            for (TouSeasonDateData tsdd : seasonDatesList) {
                                if (!tsdd.getId().equals(seasonDateData.getId())
                                        && tsdd.getTouSeasonId().equals(seasonDateData.getTouSeasonId())) {
                                    deleteCalendarSeason = false;
                                    break;
                                }
                            }
                            final boolean _deleteCalendarSeason = deleteCalendarSeason;
                            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                @Override
                                public void callback(SessionCheckResolution resolution) {
                                    clientFactory.getCalendarRpc().deleteSeasonDate(seasonDateData, _deleteCalendarSeason, new ClientCallback<Boolean>(aleft, atop) {
                                        @Override
                                        public void onSuccess(Boolean result) {
                                            if (result) {
                                                setSeasonDate(null);
                                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.assign.season.deleted"), MediaResourceUtil.getInstance().getInformationIcon(), aleft, atop, MessagesUtil.getInstance().getMessage("button.close"));
                                                refreshTable();
                                                clientFactory.getEventBus().fireEvent(new SeasonAssignedEvent());
                                            }
                                        }
                                    });
                                }
                            };
                            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                        }
                    }
                }, aleft,atop
         );
    }

    private boolean isDateInExistingDates(int month, int day, Long id) {
        boolean yesItsIn = false;

        for (TouSeasonDateData data : seasonDatesList) {

            if (data.getCompletemonths().contains(month)) {
                if (id == null || !(id.equals(data.getId()))) {
                    yesItsIn = true;
                    break;
                }
            }

            if (data.getStartMonth()==month && day >= data.getStartDay()) {
                if ((data.getEndMonth()==month && day <= data.getEndDay())
                        || data.getEndMonth() > month) {
                    if (id == null || !(id.equals(data.getId()))) {
                        yesItsIn = true;
                        break;
                    }
                }
            }

            if (data.getEndMonth()==month && day <= data.getEndDay()) {
                if ((data.getStartMonth()==month && day >= data.getStartDay())
                        || data.getStartMonth() < month) {
                    if (id == null || !(id.equals(data.getId()))) {
                        yesItsIn = true;
                        break;
                    }
                }
            }
        }
        return yesItsIn;
    }

    public boolean isYearComplete() {
        Collections.sort(seasonDatesList);
        TouSeasonDateData prevSd = null;
        for (TouSeasonDateData tsdd : seasonDatesList) {
            if(prevSd == null) {
                // this is the first one, so must start with 1 month 1 day
                if(tsdd.getStartMonth() != 1 && tsdd.getStartDay() != 1) {
                    return false;
                }
            } else {
                 if(prevSd.getTouSeasonId().equals(tsdd.getTouSeasonId())) {
                     //fail // must merge them into one as it is redundant to have 2 entries
                 }
                 // this seasonday start must be one day ahead of the prevSeasons end
                 int currentDay = prevSd.getEndDay() == prevSd.getLastDayOfMonth(prevSd.getEndMonth()) ? 1 : prevSd.getEndDay() + 1;
                 int currentMonth = prevSd.getEndDay() == prevSd.getLastDayOfMonth(prevSd.getEndMonth()) ? prevSd.getEndMonth() + 1 : prevSd.getEndMonth();

                 if(tsdd.getStartMonth() != currentMonth || tsdd.getStartDay() != currentDay) {
                     return false;
                 }
                 // can't go beyond 12 months
                 if(tsdd.getEndMonth() > 12) {
                     return false;
                 }
             }
             prevSd = tsdd;
        }
        if (prevSd == null) {
            return false;
        }
        if(prevSd.getEndMonth() != 12 || prevSd.getEndDay() != prevSd.getLastDayOfMonth(12)) {
            return false;
        }
        return true;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
        formPanel.setVisible(!readOnly);
    }

    protected void addFieldHandlers() {
        startMonthListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        startDayListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        endMonthListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        endDayListBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        lstbxSeason.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

}
