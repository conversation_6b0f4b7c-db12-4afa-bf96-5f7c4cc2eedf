package za.co.ipay.metermng.client.view.component.customer;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormGroupPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.CurrencyTextBox;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.AuxChargeScheduleUpdatedEvent;
import za.co.ipay.metermng.client.event.AuxChargeScheduleUpdatedEventHandler;
import za.co.ipay.metermng.client.event.AuxTypesUpdatedEvent;
import za.co.ipay.metermng.client.event.AuxTypesUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.form.SimpleFormPanel;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.MridComponent;
import za.co.ipay.metermng.client.view.component.SpecialActionsReasonComponent;
import za.co.ipay.metermng.mybatis.generated.model.AuxAccount;
import za.co.ipay.metermng.mybatis.generated.model.AuxChargeSchedule;
import za.co.ipay.metermng.shared.SpecialActionsData;

public class AuxAccountPanel extends SimpleFormPanel  {
    public static final String ACCOUNT_SPECIFIC_ACS = "accountspecific";
    
	private ClientFactory clientFactory;
    private AuxAccount auxAccount;
    
	@UiField FormElement typeElement;
    @UiField FormElement nameElement;
    @UiField FormElement balanceElement;
    @UiField FormElement priorityElement;
    @UiField FormElement chargeScheduleElement;
    @UiField FormElement activeElement;
    @UiField FormGroupPanel auxBalanceTypePanel;
    @UiField FormElement startDateElement;
    @UiField FormElement lastChargeDateElement;
    @UiField FormElement suspendUntilElement;
    @UiField FormElement debtStatusElement;
    @UiField(provided=true) AuxChargeSchedAccountSpecificView specificChargeSchedule;
    
    @UiField IpayListBox lstbxAuxType;
    @UiField TextBox txtbxName;
    @UiField CurrencyTextBox txtbxBalance;
    @UiField TextBox txtbxPriority;
   	@UiField IpayListBox lstbxChargeSchedule;
   	@UiField CheckBox chckbxActive;
    @UiField(provided=true) SpecialActionsReasonComponent specialactionreasons;
    @UiField (provided=true) MridComponent mridComponent;
    @UiField RadioButton auxDebtBalance;
    @UiField RadioButton auxRefundBalance;
    @UiField DateBox startDateBox;
    @UiField Label lastChargeDateInfo;
    @UiField DateBox suspendUntil;
    @UiField Label installSuspendInfo;
    @UiField Label debtStatusInfo;
    
	private static AuxAccountPanelUiBinder uiBinder = GWT.create(AuxAccountPanelUiBinder.class);
	interface AuxAccountPanelUiBinder extends UiBinder<Widget, AuxAccountPanel> {
	}

	public AuxAccountPanel(SimpleForm form, ClientFactory clientFactory) {
		super(form);
		specificChargeSchedule = new AuxChargeSchedAccountSpecificView(clientFactory, null);
		specialactionreasons = new SpecialActionsReasonComponent(clientFactory, form, SpecialActionsData.ADD_AUX_ACCOUNT);
		mridComponent = new MridComponent();
		initWidget(uiBinder.createAndBindUi(this));
		this.clientFactory = clientFactory;
		debtStatusElement.setHelpMsg(new HTML(MessagesUtil.getInstance().getMessage("customer.debt.status.help")).toString());
		StrictDateFormat strictDateFormat = new StrictDateFormat(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
        suspendUntil.setFormat(strictDateFormat);
        startDateBox.setFormat(strictDateFormat);
		addFieldHandlers();
		clientFactory.getEventBus().addHandler(AuxTypesUpdatedEvent.TYPE, new AuxTypesUpdatedEventHandler() {
            @Override
            public void processAuxTypesUpdatedEvent(AuxTypesUpdatedEvent event) {
                populateAuxTypeListBox();
            }
        });
		clientFactory.getEventBus().addHandler(AuxChargeScheduleUpdatedEvent.TYPE, new AuxChargeScheduleUpdatedEventHandler() {
            
            @Override
            public void processAuxChargeScheduleUpdatedEvent(AuxChargeScheduleUpdatedEvent event) {
                populateAuxChargeScheduleListBox();
            }
        }); 
		populateAuxTypeListBox();
		populateAuxChargeScheduleListBox();
		startDateBox.setValue(new Date());
		mridComponent.setMrid(null);
		mridComponent.initMrid(clientFactory);
	}	
    
    @UiHandler(value={"txtbxName","txtbxBalance", "lstbxChargeSchedule", "lstbxAuxType", "txtbxPriority"})
	void handleRequiredChange(ChangeEvent event) {
		enableActiveCheckbox();
	}

	private void populateAuxTypeListBox() {
		ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
			@Override
			public void onSuccess(ArrayList<LookupListItem> result) {
				lstbxAuxType.setLookupItems(result);
				if (auxAccount != null && auxAccount.getAuxTypeId() != null) {
					lstbxAuxType.selectItemByValue(auxAccount.getAuxTypeId().toString());
				}	
				enableActiveCheckbox();
			} 
			
		};
		if (clientFactory != null) {
			lstbxAuxType.clear();
			clientFactory.getLookupRpc().getAuxTypeLookupList(lookupSvcAsyncCallback);
		}
	}
	
	private void populateAuxChargeScheduleListBox() {
		ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
			@Override
			public void onSuccess(ArrayList<LookupListItem> result) {
			    if (result == null) { // No active ACS found so add the empty option here
			        result = new ArrayList<LookupListItem>();
			        result.add(new LookupListItem("", ""));
			    }
			    result.add(1,new LookupListItem(ACCOUNT_SPECIFIC_ACS, MessagesUtil.getInstance().getMessage("auxchargeschedule.specific.list.item")));
				lstbxChargeSchedule.setLookupItems(result);
				resetAuxChargeScheduleSelection();
		        enableActiveCheckbox();
			} 
		};
		if (clientFactory != null) {
			lstbxChargeSchedule.clear();
			clientFactory.getLookupRpc().getAuxChargeScheduleLookupList(lookupSvcAsyncCallback);
		}
	}
	
	public void enableActiveCheckbox() {
		boolean allRequiredFieldsPopulated = (!txtbxName.getText().trim().isEmpty()); 
		if (allRequiredFieldsPopulated)
				allRequiredFieldsPopulated = (!(lstbxAuxType.getSelectedValues().isEmpty() || lstbxAuxType.getSelectedValues().get(0).trim().isEmpty()));
		if (allRequiredFieldsPopulated)
				allRequiredFieldsPopulated = (txtbxBalance.getAmount() != null);
		if (allRequiredFieldsPopulated && chargeScheduleElement.isVisible())
				allRequiredFieldsPopulated = (!(lstbxChargeSchedule.getSelectedValues().isEmpty() || lstbxChargeSchedule.getSelectedValues().get(0).trim().isEmpty()));
		if (allRequiredFieldsPopulated)
			allRequiredFieldsPopulated = (!txtbxPriority.getValue().isEmpty());
		if (allRequiredFieldsPopulated)
            allRequiredFieldsPopulated = startDateBox.getValue() != null && !startDateBox.getTextBox().getValue().isEmpty();
            
		if (!allRequiredFieldsPopulated) {
			chckbxActive.setValue(false);
			chckbxActive.setEnabled(false);
			chckbxActive.setStyleName("gwt-CheckBox-disabled ");
		} else {
			chckbxActive.setEnabled(true);
			chckbxActive.setStyleName("gwt-Label-bold-left");
		}
	}

	@Override
	public void addFieldHandlers() {
		txtbxName.addChangeHandler(new FormDataChangeHandler(form));
		lstbxAuxType.addChangeHandler(new FormDataChangeHandler(form));
		txtbxBalance.addChangeHandler(new FormDataChangeHandler(form));
		txtbxPriority.addChangeHandler(new FormDataChangeHandler(form));	
		chckbxActive.addClickHandler(new FormDataClickHandler(form));  
		auxRefundBalance.addClickHandler(new FormDataClickHandler(form));
		auxDebtBalance.addClickHandler(new FormDataClickHandler(form));
		suspendUntil.setFireNullValues(true);
		
		lstbxChargeSchedule.addChangeHandler(new ChangeHandler() {
		    public void onChange(ChangeEvent event) {
		        if(lstbxChargeSchedule.getSelectedValues().get(0).equals(ACCOUNT_SPECIFIC_ACS)) {
		            specificChargeSchedule.setVisible(true);
		        } else {
		            specificChargeSchedule.setVisible(false);
		        }
		       }
		      });
		
		suspendUntil.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form){
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                super.onValueChange(event);
                LookupListItem lli = lstbxChargeSchedule.getItem(lstbxChargeSchedule.getSelectedIndex());
                Map<String, Object> extraInfoMap = lli.getExtraInfoMap();
                if(suspendUntil.getValue() != null && !suspendUntil.getTextBox().getValue().isEmpty()
                        && extraInfoMap.get("Instalment").equals("true")){
                    installSuspendInfo.setVisible(true);
                } else {
                    installSuspendInfo.setVisible(false);
                }
            }
        });
		
		startDateBox.setFireNullValues(true);
		startDateBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(form){
            @Override
            public void onValueChange(ValueChangeEvent<Date> event) {
                super.onValueChange(event);
                enableActiveCheckbox();
            }
        });
		
        ValueChangeHandler<Boolean> radioBalanceTypeChangeHandler = new ValueChangeHandler<Boolean>() {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event) {
                handleBalanceTypeChange();
            }
        };
        auxRefundBalance.addValueChangeHandler(radioBalanceTypeChangeHandler);
        auxDebtBalance.addValueChangeHandler(radioBalanceTypeChangeHandler);
        mridComponent.addFieldHandlers(form);
	}

    public void handleBalanceTypeChange() {
        boolean refundSelected = auxRefundBalance.getValue();
        if (refundSelected) {
            chargeScheduleElement.setVisible(false);
            specificChargeSchedule.setVisible(false);
            resetAuxChargeScheduleSelection();
        } else {
            chargeScheduleElement.setVisible(true);
        }
        enableActiveCheckbox();
    }

    private void resetAuxChargeScheduleSelection() {
        if (auxAccount != null) {
            Long id = auxAccount.getAuxChargeScheduleId();
            if (id != null) {
                lstbxChargeSchedule.selectItemByValue(id.toString());
                return;
            }
        }
        lstbxChargeSchedule.setSelectedIndex(0);
    }

	@Override
	public void clearFields() {
		form.setDirtyData(false);
		txtbxName.setText("");
		lstbxAuxType.clearSelections();
		lstbxChargeSchedule.clearSelections();
		txtbxBalance.setValue(null);
		txtbxPriority.setText("");			
		chckbxActive.setValue(false);
		specialactionreasons.clearFields();
		auxDebtBalance.setValue(false);
		auxRefundBalance.setValue(false);
		suspendUntil.setValue(null);
		startDateBox.setValue(new Date());
		lastChargeDateInfo.setText("");
		installSuspendInfo.setVisible(false);
		showSpecificAuxChargeSched(null);
		specificChargeSchedule.setVisible(false);
	}

	@Override
	public void clearErrors() {
		typeElement.clearErrorMsg();
	    nameElement.clearErrorMsg();
	    balanceElement.clearErrorMsg();
	    priorityElement.clearErrorMsg();
	    chargeScheduleElement.clearErrorMsg();
	    activeElement.clearErrorMsg();
	    specialactionreasons.clearErrorMessages();
	    auxBalanceTypePanel.clearErrorMsg();
	    suspendUntilElement.clearErrorMsg();
	    startDateElement.clearErrorMsg();
	    mridComponent.clearErrorMsg();
	}

	public void setSpecialActionValue(String value) {
		specialactionreasons.setSpecialActionValue(value);
	}

	public boolean isBalanceTypeValid() {
		if (auxDebtBalance.getValue() == true || auxRefundBalance.getValue() == true) {
			return true;
		}
		return false;
	}
	
   public void showSpecificAuxChargeSched(AuxChargeSchedule acs) {
       specificChargeSchedule.setVisible(true);
       specificChargeSchedule.setAuxChargeSchedule(acs);
    }
}
