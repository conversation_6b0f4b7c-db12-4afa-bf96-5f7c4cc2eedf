package za.co.ipay.metermng.client.view.component.group;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.ScrollableTabLayoutPanel;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormView;
import za.co.ipay.gwt.common.client.workspace.TabLayoutWorkspaceContainer;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.event.OpenAdminDashboardEvent;
import za.co.ipay.metermng.client.event.OpenDashboardEvent;
import za.co.ipay.metermng.client.event.SearchEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.selection.SelectionDataWidget;
import za.co.ipay.metermng.mybatis.generated.model.GenGroup;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;
import za.co.ipay.metermng.shared.dto.SelectionDataItem.SelectionDataType;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;
import za.co.ipay.metermng.shared.dto.user.UserAvailableGroupsData;

public class ChangeGroupView extends BaseComponent {

	private static ChangeGroupViewUiBinder uiBinder = GWT.create(ChangeGroupViewUiBinder.class);

	interface ChangeGroupViewUiBinder extends UiBinder<Widget, ChangeGroupView> {
	}

	public ChangeGroupView() {
		initWidget(uiBinder.createAndBindUi(this));
	}

	private PopupPanel popupPanel;
	private MeterMngUser user;
	@UiField SimpleFormView view;
	ChangeGroupSelectionPanel panel;
	private boolean setupLoginAccessGroup = false;

	/** The group type for the access groups displayed in the widget for selection. */
	private GroupType accessGroupType;

	private static Logger logger = Logger.getLogger(ChangeGroupView.class.getName());

	public ChangeGroupView(PopupPanel popupPanel, ClientFactory clientFactory) {
		this(popupPanel, clientFactory, false);
	}
	public ChangeGroupView(PopupPanel popupPanel, ClientFactory clientFactory, boolean setupLoginAccessGroup) {
		this.popupPanel = popupPanel;
		this.setupLoginAccessGroup = setupLoginAccessGroup;
		this.clientFactory = clientFactory;
		initWidget(uiBinder.createAndBindUi(this));
		initUi();
	}

	private void initUi() {
		view.setDataDetails(null, MessagesUtil.getInstance().getMessage("changegroup.select"));
		initForm();
	}

	private void initForm() {
		panel = new ChangeGroupSelectionPanel(view.getForm(), clientFactory);
		view.getForm().getFormFields().add(panel);
		view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("changegroup.current.details"));
		view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("changegroup.set"));
		view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
			@Override
			public void onClick(ClickEvent event) {
                if (anyOpenUsagePointTabsHaveDirtyData()) {
                    Dialogs.confirm(
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("question.close.tabs.dirty"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.yes"),
                            ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                            ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                            new ConfirmHandler() {

                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                onChangeGroup();
                            }
                        }
                    });
                } else {
                    onChangeGroup();
                }
			}
		});
		view.getForm().getSaveBtn().ensureDebugId("changeButton");

		view.getForm().getOtherBtn().setVisible(false);
		view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.clear.group"));
		view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
			@Override
			public void onClick(ClickEvent event) {
				onClearGroup();
			}
		});
		view.getForm().getOtherBtn().ensureDebugId("clearButton");

		if (!setupLoginAccessGroup) {
			view.getForm().getBackBtn().setVisible(true);
			view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
			view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
				@Override
				public void onClick(ClickEvent event) {
					onCancel();
				}
			});
		}
		view.getForm().getBackBtn().ensureDebugId("cancelButton");

		view.getForm().getFormPanel().setHeadingText(UiMessagesUtil.getInstance().getChangeGroup(), "pageSectionTitle");
	}

	public void setUser(MeterMngUser user) {
		this.user = user;
		if (user != null) {
			if (user.getUserName() != null) {
				panel.usernameBox.setText(user.getUserName());
			}
			if (user.getAssignedGroup() != null) {
				view.getForm().getOtherBtn().setVisible(true);
				panel.assignedGroupBox.setText(user.getAssignedGroup().getName());
			} else {
				view.getForm().getOtherBtn().setVisible(false);
			}
			loadUserAccessGroupType();
		}
	}

	private void loadUserAccessGroupType() {
		clientFactory.getGroupRpc().getAccessGroupType(new ClientCallback<GroupType>() {
			@Override
			public void onSuccess(GroupType result) {
				accessGroupType = result;
				if (result != null) {
					clientFactory.getGroupRpc().getAccessGroupsForUser(accessGroupType.getId(), new ClientCallback<UserAvailableGroupsData>() {
						@Override
						public void onSuccess(UserAvailableGroupsData result) {
							SelectionDataItem item = null;
							//Only expecting one group
							panel.groupPanel.clear();
							for(int i=0;i<result.getGroups().size();i++) {
								item = result.getGroups().get(i);
								logger.info("Got SelectionDataItem group: "+item);
								Long dId = null;
								if (user.getAssignedGroup() != null) {
									dId = user.getAssignedGroup().getId();
								}
								panel.userGroupBox = new SelectionDataWidget(clientFactory, clientFactory.getGroupRpc(), item, false, true, SelectionDataItem.toIdKey(SelectionDataType.GROUP_DATA, dId), false, null);
								panel.groupPanel.add(panel.userGroupBox);
							}
							if (result.getCurrentGroupPath().size() > 0) {
								//Set the selected path for the current group id
								panel.userGroupBox.setSelectedGroup(result.getCurrentGroupPath());
							}
						}
					});
				} else {
					Dialogs.centreErrorMessage(MessagesUtil.getInstance().getMessage("usergroup.no.accessgroup"), MediaResourceUtil.getInstance().getErrorIcon(), MessagesUtil.getInstance().getMessage("button.close"));
				}
			}
		});
	}

	private void onCancel() {
		popupPanel.hide();
		user = null;
		accessGroupType = null;
		view.getForm().getOtherBtn().setVisible(false);
		panel.clearErrors();
		panel.clearFields();
	}

	private void onChangeGroup() {
		Long selectedId = panel.userGroupBox.getSelectedGroup();
		if (! SelectionDataItem.isActualGroupId(selectedId)) {
			selectedId = null;
		}
		if (selectedId != null) {
			if (!panel.userGroupBox.isLowestLevel()) {       //if come from timeout re-login then won't have opened the next level of widget!!
				panel.userGroupBox.createNextListBox(panel.userGroupBox.getLatestListBox());
				return;
			}
			if (user != null && user.getCurrentGroupId() != null && selectedId.equals(user.getCurrentGroupId())) {
				panel.groupElement.showErrorMsg(MessagesUtil.getInstance().getMessage("changegroup.error.same"));
			} else {
				//Change the user's currentGroup
				final Long currentGroupId = selectedId;
				SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
					@Override
					public void callback(SessionCheckResolution resolution) {
						clientFactory.getUserRpc().updateUserCurrentGroup(currentGroupId, new ClientCallback<GenGroup>() {
							@Override
							public void onSuccess(GenGroup group) {
								Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("changegroup.group.changed"), MediaResourceUtil.getInstance().getInformationIcon());
								//Update the header & the user in ClientFactory
								clientFactory.displayUserGroup(group.getName());
								closeOpenUsagePointTabs();
								//Notify any affected tabs
								clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.USER_CURRENT_GROUP));
								//Hide this screen
								onCancel();
								//when coming from login choose --> now display dashboard, admin dashboard or advanced search depending on user permissions
								if (setupLoginAccessGroup) {
									openPanels();
								}
							}
						});
					}
				};
				clientFactory.handleSessionCheckCallback(sessionCheckCallback);
			}
		} else {
			panel.groupElement.showErrorMsg(MessagesUtil.getInstance().getMessage("changegroup.error.group.none"));
		}
	}

	private void onClearGroup() {
		//Clear the user's currentGroup
		SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
			@Override
			public void callback(SessionCheckResolution resolution) {
				clientFactory.getUserRpc().updateUserCurrentGroup(null, new ClientCallback<GenGroup>() {
					@Override
					public void onSuccess(GenGroup group) {
						panel.clearErrors();
						if (group == null) {
							loadUserAccessGroupType();
							Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("changegroup.error.clear"), MediaResourceUtil.getInstance().getInformationIcon());
							return;
						}
						Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("changegroup.group.cleared"), MediaResourceUtil.getInstance().getInformationIcon());
						//Hide this screen
						onCancel();
						//Update the header
						clientFactory.displayUserGroup(group.getName());
						//Notify any affected tabs
						clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.USER_CURRENT_GROUP));
						//when coming from login choose --> now display dashboard, admin dashboard or advanced search depending on user permissions
						if (setupLoginAccessGroup) {
							openPanels();
						}
					}
				});
			}
		};
		clientFactory.handleSessionCheckCallback(sessionCheckCallback);
	}

	private void openPanels() {
		if (clientFactory.getUser().hasPermission(
				MeterMngStatics.ACCESS_PERMISSION_MM_DASHBOARD_VIEW)){
				clientFactory.getEventBus().fireEvent(new OpenDashboardEvent());
		} else if (clientFactory.getUser().hasPermission(
				MeterMngStatics.ACCESS_PERMISSION_MM_ADMIN_DASHBOARD_VIEW)){
			clientFactory.getEventBus().fireEvent(new OpenAdminDashboardEvent());
		} else {
			clientFactory.getEventBus().fireEvent(new SearchEvent("advanced"));
		}
	}

	private void closeOpenUsagePointTabs(){
		TabLayoutWorkspaceContainer container = (TabLayoutWorkspaceContainer) clientFactory.getWorkspaceContainer();
		int tabCount = container.getWorkspacesCount();
		ScrollableTabLayoutPanel tabLayoutPanel = container.getTabLayoutPanel();
		logger.fine("Recursively close open usage point tabs");
		for (int i = tabCount-1; i >= 0; i--) {
			Workspace w = (Workspace) tabLayoutPanel.getWidget(i);
			container.closeWorkspaceNow(w);
		}
	}

    private boolean anyOpenUsagePointTabsHaveDirtyData() {
        TabLayoutWorkspaceContainer container = (TabLayoutWorkspaceContainer) clientFactory.getWorkspaceContainer();
        int tabCount = container.getWorkspacesCount();
        ScrollableTabLayoutPanel tabLayoutPanel = container.getTabLayoutPanel();
        logger.fine("Check open usage point tabs for dirty data");
        for (int i = tabCount - 1; i >= 0; i--) {
            Workspace w = (Workspace) tabLayoutPanel.getWidget(i);
            if (w.hasAnyDirtyData()) {
                return true;
            }
        }
        return false;
    }

}
