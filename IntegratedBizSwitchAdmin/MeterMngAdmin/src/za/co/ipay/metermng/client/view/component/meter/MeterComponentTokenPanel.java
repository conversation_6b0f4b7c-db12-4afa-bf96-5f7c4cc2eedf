package za.co.ipay.metermng.client.view.component.meter;

import java.math.BigDecimal;
import java.util.ArrayList;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.Window.Location;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.InlineLabel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.event.EngineeringTokenIssuedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.Meter;
import za.co.ipay.metermng.mybatis.generated.model.StsMeter;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.TokenData;

public class MeterComponentTokenPanel extends BaseComponent {

    private Meter meter;
    private StsMeter stsMeter;
    private Long usagePointId;
    private PopupPanel simplePopup;

    @UiField FormElement descriptionElement;
    @UiField Button btnGetToken;
    @UiField Button btnClose;
    @UiField TextBox txtbxdescription;
    @UiField Label theTokenCode1;
    @UiField Label theTokenCode2;
    @UiField Label theTokenCode3;
    @UiField Label theTokenCode4;
    @UiField TextBox txtbxSgKrn;
    @UiField FormElement newSGCElement;
    @UiField FormElement newTIElement;
    @UiField TextBox txtbxNewTI;
    @UiField HTMLPanel pnlPdfButtons;
    @UiField Button btnPrint;
    @UiField Button btnSaveToPdf;
    @UiField Label lblHeading;
    @UiField FormRowPanel formStsFields;
    @UiField InlineLabel descriptionPrefix;
    @UiField(provided=true) EngineeringTokenUserRefPanel engineeringTokenUserRefPanel;
    @UiField FormElement powerLimitElement;
    @UiField TextBox txtbxPowerLimit;
    @UiField FormElement formDescriptionPrefix;

    private String stsEngineeringTokenId = null;

    private LookupListItem newSgcListItem = null;
    private LookupListItem powerLimitItem = null;

    private static MeterComponentTokenWidgetUiBinder uiBinder = GWT.create(MeterComponentTokenWidgetUiBinder.class);

    interface MeterComponentTokenWidgetUiBinder extends UiBinder<Widget, MeterComponentTokenPanel> {
    }

    public MeterComponentTokenPanel() {
        initWidget(uiBinder.createAndBindUi(this));
    }

    public MeterComponentTokenPanel(ClientFactory clientFactory, StsMeter stsMeter, Meter meter, Long usagePointId,
            PopupPanel simplePopup, boolean isLastPanel, LookupListItem powerLimitItem) {
        this.clientFactory = clientFactory;
        initEngineeringTokenUserRefPanel();
        initWidget(uiBinder.createAndBindUi(this));
        this.meter = meter;
        setStsMeter(stsMeter);
        this.usagePointId = usagePointId;
        this.simplePopup = simplePopup;
        init();
        this.powerLimitItem = powerLimitItem;
        String headingKey = null;
        if (this.powerLimitItem == null) {
            headingKey = "changekey";
            if (!isLastPanel) {
                btnClose.setVisible(false);
            }
        } else {
            headingKey = "powerlimit";
            formStsFields.setVisible(false);
            txtbxPowerLimit.setText(this.powerLimitItem.getDisplayString());
            descriptionPrefix.setText(this.powerLimitItem.getDisplayString() + " - ");
            powerLimitElement.setVisible(true);
            formDescriptionPrefix.setVisible(true);
            descriptionPrefix.setVisible(true);
        }
        Messages messagesInstance = MessagesUtil.getInstance();
        String tokenType = messagesInstance.getMessage("meter." + headingKey);
        lblHeading.setText(tokenType);
        descriptionElement.setHelpMsg(
                messagesInstance.getMessage("meter.issue.token.description.help", new String[] { tokenType }));
    }

    protected void init() {
        theTokenCode1.setVisible(false);
        theTokenCode2.setVisible(false);
        theTokenCode3.setVisible(false);
        theTokenCode4.setVisible(false);
        populateSgKrnCodeListBox();
    }

    private void initEngineeringTokenUserRefPanel() {
        engineeringTokenUserRefPanel = new EngineeringTokenUserRefPanel(clientFactory);
        engineeringTokenUserRefPanel.showPanel();
    }

    public void setStsMeter(StsMeter stsMeter) {
        this.stsMeter = stsMeter;
        populateSgKrnCodeListBox();
    }

    public void setUsagePointId(Long usagePointId) {
        this.usagePointId = usagePointId;
        init();
    }

    @UiHandler("btnGetToken")
    void handleGetTokenButton(final ClickEvent event) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                handleGetTokenButtonClickEvent(event);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @SuppressWarnings("deprecation")
    private void handleGetTokenButtonClickEvent(ClickEvent ignored) {
        if (validateForm()) {
            final PopupPanel waitDialog = Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon(),
                    btnGetToken.getAbsoluteLeft() + btnGetToken.getOffsetWidth(), btnGetToken.getAbsoluteTop());

            // Map form fields to data object
            ClientCallback<TokenData> tokenSvcAsyncCallback = new ClientCallback<TokenData>() {
                @Override
                public void onSuccess(TokenData result) {
                    waitDialog.hide();
                    if (result == null) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("error.token.retrieve"),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else if (result.getErrorMsg() != null) {
                        Dialogs.displayErrorMessage(
                                (MessagesUtil.getInstance().getMessage("error.token.retrieve") + " ("
                                        + result.getResCode() + ": " + result.getErrorMsg() + ") "),
                                MediaResourceUtil.getInstance().getErrorIcon(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                    } else {
                        btnGetToken.setEnabled(false);
                        ArrayList<String> thecodes = result.getEngineeringTokenCodes();

                        String tokenLabel = MessagesUtil.getInstance().getMessage("meter.token.code");
                        int tokensTotal = thecodes.size();
                        if (tokensTotal > 1) {
                            tokenLabel = MessagesUtil.getInstance().getMessage("meter.token.code1");
                            String token = thecodes.get(1);
                            if (token != null) {
                                theTokenCode2.setText(MessagesUtil.getInstance().getMessage("meter.token.code2") + ": "
                                        + constructToken(token));
                                theTokenCode2.setVisible(true);
                                if (tokensTotal > 2) {
                                    token = thecodes.get(2);
                                    if (token != null) {
                                        theTokenCode3.setText(MessagesUtil.getInstance().getMessage("meter.token.code3")
                                                + ": " + constructToken(token));
                                        theTokenCode3.setVisible(true);
                                        if (tokensTotal > 3) {
                                            token = thecodes.get(3);
                                            if (token != null) {
                                                theTokenCode4.setText(
                                                        MessagesUtil.getInstance().getMessage("meter.token.code4")
                                                                + ": " + constructToken(token));
                                                theTokenCode4.setVisible(true);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        theTokenCode1.setText(tokenLabel + ": " + constructToken(thecodes.get(0)));
                        theTokenCode1.setVisible(true);

                        getEngineeringTokenTransactions();
                        if (formStsFields.isVisible()) {
                            clientFactory.getEventBus().fireEvent(
                                    new EngineeringTokenIssuedEvent(stsMeter, TokenData.TOKEN_TYPE_KEY_CHANGE));
                        } else {
                            clientFactory.getMeterRpc().updateMeterPowerLimit(meter, stsMeter,
                                    new BigDecimal(powerLimitItem.getValue()), powerLimitItem.getDisplayString(),
                                    new ClientCallback<Void>() {
                                        @Override
                                        public void onSuccess(Void result) {
                                            clientFactory.getEventBus().fireEvent(new EngineeringTokenIssuedEvent(
                                                    stsMeter, TokenData.TOKEN_TYPE_POWER_LIMIT));
                                        }
                                    });
                        }
                    }
                }
            };

            clearErrorMessages();
            theTokenCode1.setVisible(false);
            theTokenCode1.setText("");
            theTokenCode2.setVisible(false);
            theTokenCode2.setText("");
            theTokenCode3.setVisible(false);
            theTokenCode3.setText("");
            theTokenCode4.setVisible(false);
            theTokenCode4.setText("");

            if (clientFactory != null) {
                waitDialog.show();
                if (formStsFields.isVisible()) {
                    stsMeter.setStsNewSupplyGroupId(Long.valueOf(newSgcListItem.getValue()));
                    String[] lliA = newSgcListItem.getExtraInfo().split(":");
                    for (int i = 0; i < lliA.length; i++) {
                        if (lliA[i].equals("SGC")) {
                            stsMeter.setStsNewSupplyGroupCode(lliA[i + 1]);
                        } else if (lliA[i].equals("KRN")) {
                            stsMeter.setStsNewKeyRevisionNum(Integer.parseInt(lliA[i + 1]));
                        }
                    }
                    stsMeter.setStsNewTariffIndex(txtbxNewTI.getText());
                    clientFactory.getTokenGeneratorRpc().requestKeyChangeTokens(txtbxdescription.getText(),
                            usagePointId, stsMeter, engineeringTokenUserRefPanel.getUserReferenceValue(),
                            tokenSvcAsyncCallback);
                } else {
                    clientFactory.getTokenGeneratorRpc().requestPowerLimitToken(txtbxdescription.getText(),
                            usagePointId, stsMeter,
                            Integer.parseInt(powerLimitItem.getValue()),
                            engineeringTokenUserRefPanel.getUserReferenceValue(), tokenSvcAsyncCallback);
                }
            }
        }
    }

    private boolean validateForm() {
        boolean isValidated = true;
        if (!engineeringTokenUserRefPanel.validateFormField()) {
            isValidated = false;
        }

        return isValidated;
    }

    @UiHandler("btnClose")
    void handleCloseButton(ClickEvent event) {
        simplePopup.hide();
    }

    private String constructToken(String thecode) {
        int len;
        int numberOfSpaces;
        char[] val;
        char[] buf;

        len = thecode.length();
        numberOfSpaces = (len / 4) + 1;
        val = thecode.toCharArray();
        buf = new char[len + numberOfSpaces];

        int j = 0;
        for (int c = 0; c < len; c++) {
            if ((c % 4) == 0) {
                buf[j++] = ' ';
            }
            buf[j++] = val[c];
        }

        return new String(buf, 0, j).trim();
    }

    public void clearErrorMessages() {
        descriptionElement.clearErrorMsg();
        engineeringTokenUserRefPanel.clearErrorMessage();
    }

    private void populateSgKrnCodeListBox() {
        ClientCallback<ArrayList<LookupListItem>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<LookupListItem>>() {
            @Override
            public void onSuccess(ArrayList<LookupListItem> result) {
                newSgcListItem = null;
                for (LookupListItem item : result) {
                    if (item.getValue().equals(stsMeter.getStsNewSupplyGroupId().toString())) {
                        newSgcListItem = item;
                        break;
                    }
                }
                txtbxSgKrn.setText(newSgcListItem.getText());
                txtbxNewTI.setText(stsMeter.getStsNewTariffIndex());
            }

        };
        if (clientFactory != null) {
            clientFactory.getLookupRpc().getSgKrnLookupList(lookupSvcAsyncCallback);
        }
    }

    public void clearTokens() {
        theTokenCode1.setText("");
        theTokenCode1.setVisible(false);
        theTokenCode2.setText("");
        theTokenCode2.setVisible(false);
        theTokenCode3.setText("");
        theTokenCode3.setVisible(false);
        theTokenCode4.setText("");
        theTokenCode4.setVisible(false);
    }

    private void getEngineeringTokenTransactions() {
        clientFactory.getMeterRpc().getEngineeringTokenTransactions(stsMeter.getId(), clientFactory.isEnableAccessGroups(),
                new ClientCallback<ArrayList<StsEngineeringTokenData>>() {
                    @Override
                    public void onSuccess(ArrayList<StsEngineeringTokenData> result) {
                        if (result != null) {
                            stsEngineeringTokenId = result.get(0).getId().toString();
                            pnlPdfButtons.setVisible(true);
                        }
                    }
                });
    }

    @UiHandler("btnPrint")
    void handlePrintReceipt(ClickEvent e) {
        generatePdf("print");
    }

    @UiHandler("btnSaveToPdf")
    void handleSaveReceiptPdf(ClickEvent e) {
        generatePdf("save");
    }

    private void generatePdf(String action) {
        GetRequestBuilder url = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                .withTargetUrl("reprintreceiptpdf")
                .addParam("action", action)
                .addParam("stsengineeringtokenid", stsEngineeringTokenId)
                .addParam("meterid", stsMeter.getId().toString())
                .addParam("usingaccessgroups", "" + clientFactory.isEnableAccessGroups())
                .addParam("locale", clientFactory.getLocaleName());
        if ("print".equals(action)) {
            Window.open(url.toEncodedUrl(), "_blank", "");
        } else {
            Location.assign(url.toEncodedUrl());
        }
    }
}
