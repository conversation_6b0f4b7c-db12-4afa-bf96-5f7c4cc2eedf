package za.co.ipay.metermng.client.view.component.group.entity.ndp;

import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.NdpSchedule;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.NdpScheduleData;

public class NonDisconnectPeriodPanel extends BaseComponent implements ContainsNdpPanel {
    @UiField VerticalPanel contentPanel;

    @UiField HTMLPanel scheduleInfoPanel;
    @UiField CheckBox scheduleActiveBox;

    @UiField Button btnDelete;
    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField Button btnBack;
    @UiField Button btnAdd;
    @UiField Button btnShowInherited;

    @UiField(provided=true) SchedulePanel schedulePanel;

    private GenGroupParentComponent parentWorkspace;
    private GenGroupData genGroupData;
    private NdpScheduleData globalNdpScheduleData;
    private NdpScheduleData ndpScheduleData;

    private SchedulePopup inheritedSchedulePopup;
    private Boolean isViewOnly = false;
    private HasDirtyData hasDirtyData;

    private static NonDisconnectPeriodPanelUiBinder uiBinder = GWT.create(NonDisconnectPeriodPanelUiBinder.class);

    interface NonDisconnectPeriodPanelUiBinder extends UiBinder<Widget, NonDisconnectPeriodPanel> {
    }

    private static Logger logger = Logger.getLogger(NonDisconnectPeriodPanel.class.getName());

    public NonDisconnectPeriodPanel(GenGroupParentComponent parentWorkspace, ClientFactory clientFactory) {
        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        schedulePanel = new SchedulePanel(clientFactory, this, null);
        hasDirtyData = parentWorkspace.getHasDirtyDataManager().createAndRegisterHasDirtyData();

        initWidget(uiBinder.createAndBindUi(this));

        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ACTION_PERMISSION_MM_NDP_GROUP_ADMIN)) {
            //then has only clientFactory.getUser().hasPermission(MeterMngStatics.VIEW_ONLY_MM_NDP))
            isViewOnly = true;
            btnDelete.removeFromParent();
            btnSave.removeFromParent();
            btnAdd.removeFromParent();
            btnCancel.removeFromParent();
        } else {
            scheduleActiveBox.addClickHandler(new FormDataClickHandler(hasDirtyData));
        }

        setButtonBasicVisibility();
    }

    private void setButtonBasicVisibility(){
        if (!isViewOnly) {
            btnDelete.setVisible(false);
            btnSave.setVisible(false);
            btnAdd.setVisible(true);
        }
        btnShowInherited.setVisible(true);
    }

    @UiHandler("btnSave")
    public void save(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                save();
            }
        };
       clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnCancel")
    public void cancel(ClickEvent e) {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    hasDirtyData.setDirtyData(false);
                    if (ndpScheduleData == null) {
                        scheduleActiveBox.setEnabled(false);
                    } else {
                        displayActive(ndpScheduleData);
                    }
                }
            }
        });
    }

    @UiHandler("btnBack")
    public void back(ClickEvent e) {
        parentWorkspace.goBack();
    }

    @UiHandler("btnAdd")
    public void add(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                add();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnShowInherited")
    public void showInheritedValues(ClickEvent e) {
        inheritedSchedulePopup = new SchedulePopup(clientFactory);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (genGroupData.getParent() == null || genGroupData.getParent().getNdpScheduleId() == null) {
                    clientFactory.getNdpRpc().getGlobalNdpScheduleData(new ClientCallback<NdpScheduleData>()  {
                        @Override
                        public void onSuccess(NdpScheduleData result) {
                            globalNdpScheduleData = result;
                            if (result == null || !result.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT)) {
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.inherited.global.none"),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        btnShowInherited.getAbsoluteLeft() + btnShowInherited.getOffsetWidth(),
                                        btnShowInherited.getAbsoluteTop() - btnShowInherited.getOffsetHeight(),
                                        null);
                                return;
                            }
                            inheritedSchedulePopup.populate(globalNdpScheduleData);
                            inheritedSchedulePopup.show(btnShowInherited.getAbsoluteLeft(), btnShowInherited.getAbsoluteTop(), "global", null);
                        }
                    });
                } else {
                    clientFactory.getNdpRpc().getNdpScheduleData(genGroupData.getParent().getNdpScheduleId(), new ClientCallback<NdpScheduleData>() {

                        @Override
                        public void onSuccess(NdpScheduleData result) {
                            inheritedSchedulePopup.populate(result);
                            inheritedSchedulePopup.show(btnShowInherited.getAbsoluteLeft(), btnShowInherited.getAbsoluteTop(), "parent", genGroupData.getParent().getName());
                        }
                    });
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnDelete")
    public void delete(ClickEvent e) {
        deleteNdpSchedule();
    }

    protected void display(NdpScheduleData ndpScheduleData) {
        this.ndpScheduleData = ndpScheduleData;

        scheduleInfoPanel.setVisible(true);
        displayActive(ndpScheduleData);

        schedulePanel.setVisible(true);
        schedulePanel.display(ndpScheduleData);

        if (isViewOnly) {
            scheduleActiveBox.setEnabled(false);
        } else {
            btnDelete.setVisible(true);
            btnSave.setVisible(true);
            btnAdd.setVisible(false);
        }
        btnShowInherited.setVisible(false);
    }

    private void displayActive(NdpScheduleData ndpScheduleData) {
        boolean active = ndpScheduleData.getNdpSchedule().getRecordStatus().equals(RecordStatus.ACT);
        scheduleActiveBox.setValue(active);
        scheduleActiveBox.setEnabled(true);
        if (!active) {
            if (!ndpScheduleData.isNdpTimeEntered()) {   //i.e. have not entered at least one NDP time yet
                scheduleActiveBox.setEnabled(false);
            }
        }
    }

    @Override
    public boolean isActiveBoxTicked() {
        return scheduleActiveBox.getValue().equals(Boolean.TRUE);
    }

    @Override
    public void enableActivate() {
        scheduleActiveBox.setEnabled(true);
    }

    @Override
    public void disableActivate() {
        scheduleActiveBox.setValue(false);
        scheduleActiveBox.setEnabled(false);
    }

    @Override
    public void deactivateSchedule() {
        scheduleActiveBox.setValue(false);
        scheduleActiveBox.setEnabled(false);
        ndpScheduleData.getNdpSchedule().setRecordStatus(RecordStatus.DAC);
        //save schedule
        clientFactory.getNdpRpc().saveNdpSchedule(ndpScheduleData.getNdpSchedule(), new ClientCallback<NdpSchedule>() {
            @Override
            public void onSuccess(NdpSchedule result) {
                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.schedule.activation.saved"),
                        MediaResourceUtil.getInstance().getInformationIcon(),
                        btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(),
                        btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
                        null);
            }
        });
    }

    public void loadNdps(GenGroupData genGroupData) {
        //if used the Anchor to go back before, would not have executed the button goback, might still have panels open! So clear here in case...
        schedulePanel.returnSetup();

        logger.info("NdpPanel: loadNdps(" + genGroupData.getName() + ")");
        this.genGroupData = genGroupData;

        //Only display the ndpSchedule if genGroup has its own ndpScheduleId (not=parent) which is not null
        boolean myScheduleIdIsNull = true;
        boolean parentScheduleIdIsNull = true;
        if (genGroupData.getNdpScheduleId() != null) {
            myScheduleIdIsNull = false;
        }
        if (genGroupData.getParent() != null && genGroupData.getParent().getNdpScheduleId() != null) {
            parentScheduleIdIsNull = false;
        }
        boolean myScheduleIdEqualsParent = false;
        if ((parentScheduleIdIsNull && myScheduleIdIsNull)
                ||(!myScheduleIdIsNull && !parentScheduleIdIsNull && genGroupData.getNdpScheduleId().equals(genGroupData.getParent().getNdpScheduleId()))) {
            myScheduleIdEqualsParent = true;
        }

        if ((genGroupData.getParent() == null && myScheduleIdIsNull) || myScheduleIdEqualsParent) {
            logger.info(genGroupData.getName() + " : uniqueschedule=NO");
            setButtonBasicVisibility();
            scheduleInfoPanel.setVisible(false);
            schedulePanel.setVisible(false);
        } else {
            logger.info(genGroupData.getName() + " uniqueschedule=YES, scheduleId=" + genGroupData.getNdpScheduleId());
            getAndDisplayGenGroupSchedule(genGroupData.getNdpScheduleId());
        }
    }

    private void getAndDisplayGenGroupSchedule(Long ndpScheduleId) {
        clientFactory.getNdpRpc().getNdpScheduleData(ndpScheduleId, new ClientCallback<NdpScheduleData>()  {
            @Override
            public void onSuccess(NdpScheduleData result) {
                display(result);
            }
        });
    }

    private void add() {
        //create Schedule
        btnAdd.setEnabled(false);
        clientFactory.getGroupRpc().addNewNdpScheduleToGenGroup(genGroupData,
                new ClientCallback<NdpSchedule>(btnAdd.getAbsoluteLeft(), btnAdd.getAbsoluteTop()) {
             @Override
             public void onSuccess(NdpSchedule result) {
                //if Don't go back to main tree page, "refresh" the current genGroupData instance
                Long existingNdpScheduleId = genGroupData.getNdpScheduleId();
                genGroupData.setNdpScheduleId(result.getId());
                updateChildrenAfterSuccess(genGroupData.getChildren(), existingNdpScheduleId, result.getId());

                Dialogs.displayInformationMessage(
                        MessagesUtil.getInstance().getMessage("ndp.schedule.new.added"),
                        MediaResourceUtil.getInstance().getInformationIcon(),
                        btnAdd.getAbsoluteLeft(), btnAdd.getAbsoluteTop(),
                        MessagesUtil.getInstance().getMessage("button.close"));
                btnAdd.setEnabled(true);
                NdpScheduleData addScheduleData = new NdpScheduleData(result);
                display(addScheduleData);
                parentWorkspace.reLoadGroups(genGroupData);
            }

            @Override
            public void onFailureClient() {
                btnAdd.setEnabled(true);
            }
        });
    }

    private void save() {
        //disable buttons
        btnDelete.setEnabled(false);
        btnSave.setEnabled(false);
        btnBack.setEnabled(false);
        btnAdd.setEnabled(false);
        btnShowInherited.setEnabled(false);
        schedulePanel.disableButtons();

        if (hasDirtyData.isDirtyData()) {
            if (scheduleActiveBox.getValue()) {
                ndpScheduleData.getNdpSchedule().setRecordStatus(RecordStatus.ACT);
            } else {
                ndpScheduleData.getNdpSchedule().setRecordStatus(RecordStatus.DAC);
            }
            //save schedule
            clientFactory.getNdpRpc().saveNdpSchedule(ndpScheduleData.getNdpSchedule(), new ClientCallback<NdpSchedule>() {
                @Override
                public void onSuccess(NdpSchedule result) {
                    hasDirtyData.setDirtyData(false);
                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.schedule.activation.saved"),
                                                      MediaResourceUtil.getInstance().getInformationIcon(),
                                                      btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(),
                                                      btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
                                                      null);
                    enableButtons();
                    display(ndpScheduleData);
                }

                @Override
                public void onFailureClient() {
                    enableButtons();
                }
            });
        } else {
            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("ndp.schedule.activation.no.change"),
                                              MediaResourceUtil.getInstance().getInformationIcon(),
                                              btnSave.getAbsoluteLeft() + btnSave.getOffsetWidth(),
                                              btnSave.getAbsoluteTop() - btnSave.getOffsetHeight(),
                                              null);
            enableButtons();
        }
    }

    private void enableButtons() {
        btnDelete.setEnabled(true);
        btnSave.setEnabled(true);
        btnBack.setEnabled(true);
        btnAdd.setEnabled(true);
        btnShowInherited.setEnabled(true);
        schedulePanel.enableButtons();
    }

    private void deleteNdpSchedule() {
        btnDelete.setEnabled(false);
        final String source;
        if (genGroupData.getParent() == null) {
            source = "Global";
        } else {
            source = "Parent";
        }

        Dialogs.confirm (
                MessagesUtil.getInstance().getMessage("ndp.revert.parent.global", new String[] {source}),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (genGroupData.getParent() != null && genGroupData.getParent().getNdpScheduleId() != null) {
                                deleteNdpScheduleStep2(source, genGroupData.getParent().getNdpScheduleId());
                            } else {
                                deleteNdpScheduleStep2(source, null);   //"set" to global
                            }
                        } else {
                            btnDelete.setEnabled(true);
                        }
                    }
                });
    }

    private void deleteNdpScheduleStep2(String source, final Long revertToNdpScheduleId) {
        if (genGroupData.getChildren() != null && !genGroupData.getChildren().isEmpty()) {
            Dialogs.confirm (
                    MessagesUtil.getInstance().getMessage("ndp.children.delete.alert", new String[] {source}),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                revertNdpSchedule(revertToNdpScheduleId);
                            } else {
                                btnDelete.setEnabled(true);
                            }
                        }
                    });
        } else {
            revertNdpSchedule(revertToNdpScheduleId);
        }
    }

    private void revertNdpSchedule(final Long revertToNdpScheduleId) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getGroupRpc().deleteNdpSchedule(genGroupData, revertToNdpScheduleId,
                        new ClientCallback<NdpScheduleData>(btnDelete.getAbsoluteLeft(), btnDelete.getAbsoluteTop() + btnDelete.getOffsetHeight()) {
                            @Override
                            public void onSuccess(NdpScheduleData result) {
                                //if Don't go back to main tree page but press delete here directly after create, "refresh" the current genGroupData instance
                                Long existingNdpScheduleId = genGroupData.getNdpScheduleId();
                                genGroupData.setNdpScheduleId(revertToNdpScheduleId);
                                updateChildrenAfterSuccess(genGroupData.getChildren(), existingNdpScheduleId, revertToNdpScheduleId);

                                loadNdps(genGroupData);
                                btnDelete.setEnabled(true);
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance().getMessage("ndp.schedule.deleted"),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        btnBack.getAbsoluteLeft(), btnBack.getAbsoluteTop(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
                                parentWorkspace.reLoadGroups(genGroupData);
                            }

                            @Override
                            public void onFailureClient() {
                                btnDelete.setEnabled(true);
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }
    public void updateChildrenAfterSuccess(List<GenGroupData> genGroupChildren, Long existingNdpScheduleId, Long newScheduleId) {
        if (genGroupChildren != null) {
            for (GenGroupData ggdChild : genGroupChildren) {
                if ((ggdChild.getNdpScheduleId() == null && existingNdpScheduleId == null)
                        || (ggdChild.getNdpScheduleId() != null && existingNdpScheduleId != null
                            && ggdChild.getNdpScheduleId().equals(existingNdpScheduleId))) {
                    logger.info("Updating children: " + ggdChild.getName() + " scheduleId=" + newScheduleId);
                    ggdChild.setNdpScheduleId(newScheduleId);
                }
                updateChildrenAfterSuccess(ggdChild.getChildren(), existingNdpScheduleId, newScheduleId);
            }
        }
    }

    protected GenGroupParentComponent getParentWorkspace() {
        return parentWorkspace;
    }

    @Override
    public HasDirtyDataManager getHasDirtyDataManager() {
        return parentWorkspace.getHasDirtyDataManager();
    }
}
