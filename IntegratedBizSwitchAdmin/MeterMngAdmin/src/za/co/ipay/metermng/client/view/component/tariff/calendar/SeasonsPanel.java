package za.co.ipay.metermng.client.view.component.tariff.calendar;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.SeasonsUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.TouSeason;

public class SeasonsPanel extends BaseComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField(provided=true) CellTable<TouSeason> seasonsTable;
    @UiField TablePager seasonsPager;
    @UiField Label formHeading;
    @UiField TextBox nameTextBox;
    @UiField FormElement nameElement;
    @UiField Button saveBtn;
    @UiField Button cancelBtn;
    @UiField Button deleteBtn;

    private ListDataProvider<TouSeason> dataProvider;
    private SingleSelectionModel<TouSeason> selectionModel;

    private TouSeason season;

    private ArrayList<TouSeason> seasonsList;

    protected HasDirtyData hasDirtyData;
    private PopupPanel popupPanel = null;

    private static Logger logger = Logger.getLogger(SeasonsPanel.class.getName());


    private static SeasonsPanelUiBinder uiBinder = GWT.create(SeasonsPanelUiBinder.class);

    interface SeasonsPanelUiBinder extends UiBinder<Widget, SeasonsPanel> {
    }

    public SeasonsPanel(ClientFactory clientFactory, HasDirtyData hasDirtyData) {
        this.clientFactory = clientFactory;
        // passing has dirty in as this panel is reused in calendar popup
        this.hasDirtyData = hasDirtyData;
        construct();
    }

    public SeasonsPanel(ClientFactory clientFactory, HasDirtyData hasDirtyData, PopupPanel popupPanel) {
        this.clientFactory = clientFactory;
        this.hasDirtyData = hasDirtyData;
        this.popupPanel = popupPanel;
        construct();
    }

    private void construct() {
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initTable();
        setSeason(null);
        addFieldHandlers();
    }

    protected void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            seasonsTable = new CellTable<TouSeason>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            seasonsTable = new CellTable<TouSeason>(DEFAULT_PAGE_SIZE);
        }
    }

    protected void initTable() {
        if (dataProvider == null) {
            TextColumn<TouSeason> seasonName = new TextColumn<TouSeason>() {
                @Override
                public String getValue(TouSeason data) {
                    if (data.getName() != null) {
                        return data.getName();
                    }
                    return " ? ";
                }
            };
            seasonName.setSortable(true);

            // Add the columns.
            seasonsTable.addColumn(seasonName, MessagesUtil.getInstance().getMessage("calendar.season.field.name"));

            dataProvider = new ListDataProvider<TouSeason>();
            dataProvider.addDataDisplay(seasonsTable);
            seasonsPager.setDisplay(seasonsTable);
            seasonsTable.setPageSize(getPageSize());

            ListHandler<TouSeason> columnSortHandler = new ListHandler<TouSeason>(dataProvider.getList());
            columnSortHandler.setComparator(seasonName, new Comparator<TouSeason>() {
                public int compare(TouSeason o1, TouSeason o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getName() != null) {
                        return (o2 != null && o2.getName() != null) ? o1.getName().compareTo(o2.getName()) : 1;
                    }
                    return -1;
                }
            });
            seasonsTable.addColumnSortHandler(columnSortHandler);
            seasonsTable.getColumnSortList().push(seasonName);
            logger.info("Created Season table");
        }

        selectionModel = new SingleSelectionModel<TouSeason>();
        CellPreviewEvent.Handler<TouSeason> handler = new CellPreviewEvent.Handler<TouSeason>() {
            final CellPreviewEvent.Handler<TouSeason> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<TouSeason> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (hasDirtyData.isDirtyData()) {
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    hasDirtyData.setDirtyData(false);
                                                    event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                    logger.info("Discarding changes for selection event");
                                                } else {
                                                    event.setCanceled(true);
                                                    logger.info("Cancelled selection event, staying with current selection");
                                                }

                                            }});
                            } else {
                                selectionEventManager.onCellPreview(event);
                            }

                        }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }
        };
        seasonsTable.setSelectionModel(selectionModel, handler);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final TouSeason selected = selectionModel.getSelectedObject();
                if (selected != null) {
                    setSeason(selected);
                    seasonsTable.getSelectionModel().setSelected(selected, true);
                }
            }
        });
    }

    public void clearTableSelection() {
        TouSeason selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    public void refreshTable() {
        clientFactory.getCalendarRpc().getSeasons(new ClientCallback<ArrayList<TouSeason>>() {
            @Override
            public void onSuccess(ArrayList<TouSeason> result) {
                logger.info("Got seasons: " + result.size());
                if (dataProvider != null && dataProvider.getList() != null) {
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    setSeasonList(result);
                }
            }
        });
    }

    public void setSeasonList(ArrayList<TouSeason> thedata) {
        logger.info("Displaying Seasons: "+thedata.size());
        seasonsList = thedata;
        dataProvider.getList().clear();
        dataProvider.getList().addAll(thedata);
        dataProvider.refresh();
    }

    private void setSeason(TouSeason theseason) {
        clearErrors();
        clearFields();
        this.season = theseason;
        if (theseason != null) {
            saveBtn.setText(MessagesUtil.getInstance().getMessage("button.update"));
            deleteBtn.setVisible(true);
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.season.update"));
        } else {
            season = new TouSeason();
            season.setRecordStatus(RecordStatus.ACT);
            saveBtn.setText(MessagesUtil.getInstance().getMessage("button.create"));
            deleteBtn.setVisible(false);
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.season.add"));
            clearTableSelection();
            hasDirtyData.setDirtyData(false);
        }

        nameTextBox.setText(season.getName());
    }

    public void clearFields() {
        nameTextBox.setText("");
    }

    public void clearErrors() {
        nameElement.clearErrorMsg();
    }

    @UiHandler("saveBtn")
    public void save(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (season.getId() == null) {
                    addSeason();
                } else {
                    updateSeason();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("cancelBtn")
    public void cancel(ClickEvent e) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        setSeason(null);
                        if (popupPanel != null) {
                            popupPanel.hide();
                        }
                    }
                }});
        } else {
            setSeason(null);
            if (popupPanel != null) {
                popupPanel.hide();
            }
        }
    }


    @UiHandler("deleteBtn")
    public void delete(ClickEvent e) {
        if (season.getId() != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    deleteSeason();
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void update() {
        if (season == null) {
            this.season = new TouSeason();
        }
        season.setName(nameTextBox.getText().isEmpty() ? null : nameTextBox.getText());
        season.setRecordStatus(RecordStatus.ACT);
    }

    private boolean isValid() {
        boolean valid = true;
        clearErrors();
        TouSeason aseason = new TouSeason();

        aseason.setName(nameTextBox.getText().isEmpty() ? null : nameTextBox.getText());

        if (!ClientValidatorUtil.getInstance().validateField(aseason, "name", nameElement)) {
            valid = false;
        }
        return valid;
    }

    public void addSeason() {
        if (isValid()) {
            update();
            clientFactory.getCalendarRpc().addSeason(season, new ClientCallback<TouSeason>(saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouSeason result) {
                    if (result != null) {
                        hasDirtyData.setDirtyData(false);
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(
                                new String[] { MessagesUtil.getInstance().getMessage("calendar.season.title") }),
                                MediaResourceUtil.getInstance().getInformationIcon(),
                                saveBtn.getAbsoluteLeft(),
                                saveBtn.getAbsoluteTop(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                        seasonsList.add(result);
                        dataProvider.setList(seasonsList);
                        dataProvider.refresh();
                        setSeason(null);
                        clientFactory.getEventBus().fireEvent(new SeasonsUpdatedEvent());
                        if (popupPanel != null) {
                            popupPanel.hide();
                        }
                    }
                }
            });
        }
    }

    public void updateSeason() {
        if (isValid()) {
            update();
            clientFactory.getCalendarRpc().updateSeason(season, new ClientCallback<TouSeason>(saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouSeason result) {
                    if (result != null) {
                        hasDirtyData.setDirtyData(false);
                        int index = -1;
                        for (int i = 0; i < seasonsList.size(); i++) {
                            if (seasonsList.get(i).getId().equals(result.getId())) {
                                index = i;
                                break;
                            }
                        }
                        if (index > -1) {
                            setSeason(null);
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("calendar.season.title") }), MediaResourceUtil.getInstance().getInformationIcon(), saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                            seasonsList.set(index, result);
                            dataProvider.setList(seasonsList);
                            dataProvider.refresh();
                            clientFactory.getEventBus().fireEvent(new SeasonsUpdatedEvent());
                        }
                        if (popupPanel != null) {
                            popupPanel.hide();
                        }
                    }
                }
            });
        }
    }


    public void deleteSeason() {
        clientFactory.getCalendarRpc().deleteSeason(season, new ClientCallback<TouSeason>(deleteBtn.getAbsoluteLeft(), deleteBtn.getAbsoluteTop()) {
            @Override
            public void onSuccess(TouSeason result) {
                if (result != null) {
                    hasDirtyData.setDirtyData(false);
                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.season.deleted", new String[] { result.getName() }), MediaResourceUtil.getInstance().getInformationIcon(), deleteBtn.getAbsoluteLeft(), deleteBtn.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                    seasonsList.remove(result);
                    clearTableSelection();
                    refreshTable();
                    setSeason(null);
                    clientFactory.getEventBus().fireEvent(new SeasonsUpdatedEvent());
                    if (popupPanel != null) {
                        popupPanel.hide();
                    }
                }
            }
        });
    }

    protected void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

}
