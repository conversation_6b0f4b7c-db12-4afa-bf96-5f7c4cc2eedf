package za.co.ipay.metermng.client.view.component.tariff.impl;

import java.math.BigDecimal;
import java.util.List;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;

import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.tariff.ITariffUIClass;
import za.co.ipay.metermng.shared.dto.tariff.BlockDto;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;
import za.co.ipay.metermng.shared.tariff.block.ThresholdChargeData;
import za.co.ipay.metermng.shared.tariff.units.UnitChargeData;

public abstract class BaseTariffView extends BaseComponent implements ITariffUIClass {

	public static int DEFAULT_BLOCKS_COLUMNS_COUNT = 3;

    protected SimpleForm form;

    public BaseTariffView() {
    }

    @Override
    public void setForm(SimpleForm form) {
        this.form = form;
        addFieldHandlers();
    }

    @Override
    public void removeForm() {
        this.form = null;
    }

    protected abstract void addFieldHandlers();

    @Override
    public void setTariffData(ITariffData tariffData) {
    }

    @Override
    public ITariffData getTariffData() {
        return null;
    }


    @Override
    public boolean tariffDataRequired() {
        return false;
    }

    @Override
    public void setTariffInitData(ITariffInitData tariffInitData) {
    }

    @Override
    public void setCalcTemplate(String calcTemplate) {
    }

    @Override
    public String getCalcTemplate() {
        return null;
    }

    public SimpleForm getForm() {
        return form;
    }

    public void createBlockUnitChargeColumns(final CellTable<BlockDto> blocksTable,
                                             final List<UnitChargeData> blockUnitCharges,
                                             int DEFAULT_BLOCKS_COLUMNS_COUNT,
                                             final boolean hasThresholdCharge) {
        int columnCount = DEFAULT_BLOCKS_COLUMNS_COUNT;
        if (hasThresholdCharge) {
            columnCount = DEFAULT_BLOCKS_COLUMNS_COUNT + 1;
        }
        if (blocksTable.getColumnCount() > columnCount) {
            while (blocksTable.getColumnCount() > columnCount) {
                blocksTable.removeColumn(columnCount);
            }
            blocksTable.redraw();
        }

        for (final UnitChargeData unitCharge : blockUnitCharges) {
            // Unit price
            final DecimalInputCell blockChargeCell;
            if (FormatUtil.getInstance().isRightToLeft() || unitCharge.isPercentage()) {
                blockChargeCell = new DecimalInputCell("", unitCharge.isPercentage() ? "%" : FormatUtil.getInstance().getCurrencySymbol(), MessagesUtil.getInstance().getMessage("error.numeric.value"));
            } else {
                blockChargeCell = new DecimalInputCell(unitCharge.isPercentage() ? "%" : FormatUtil.getInstance().getCurrencySymbol(), "", MessagesUtil.getInstance().getMessage("error.numeric.value"));
            }
            final Column<BlockDto, String> blockChargePrice = new Column<BlockDto, String>(blockChargeCell) {
                @Override
                public String getValue(BlockDto data) {
                    List<UnitChargeData> thisBlockCharges = data.getBlock().getUnitCharges();
                    if (thisBlockCharges != null && !thisBlockCharges.isEmpty()) {
                        for (UnitChargeData thisBlockCharge : thisBlockCharges) {
                            if(thisBlockCharge.getName().equals(unitCharge.getName())) {
                                if (thisBlockCharge.getCharge() == null) {
                                    return null;
                                }
                                BigDecimal charge = thisBlockCharge.getCharge();
                                if (thisBlockCharge.isPercentage()) {
                                    charge = charge.movePointRight(2);
                                }
                                return FormatUtil.getInstance().formatDecimal(charge);
                            }
                        }
                    }
                    return null;
                }
            };
            blockChargePrice.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
                @Override
                public void update(int index, BlockDto block, String value) {
                    if (value != null && (blockChargeCell.isNumeric(value) || value.isEmpty())) {
                        getForm().setDirtyData(true);
                        List<UnitChargeData> thisBlockCharges = block.getBlock().getUnitCharges();
                        if (thisBlockCharges != null && !thisBlockCharges.isEmpty()) {
                            for (UnitChargeData thisBlockCharge : thisBlockCharges) {
                                if(thisBlockCharge.getName().equals(unitCharge.getName())) {
                                    thisBlockCharge.setCharge(FormatUtil.getInstance().parseDecimal(value));
                                    if (thisBlockCharge.isPercentage() && thisBlockCharge.getCharge() != null) {
                                        thisBlockCharge.setCharge(thisBlockCharge.getCharge().movePointLeft(2));
                                    }
                                }
                            }
                        }
                        blockChargeCell.getViewData(block).setInvalid(false);
                    } else {
                        blockChargeCell.getViewData(block).setInvalid(true);     // Mark as invalid.
                    }
                    //blocksTable.redraw();
                    MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(blockChargePrice));
                }
            });
            blocksTable.addColumn(blockChargePrice, unitCharge.getName());
        }
    }

    public void createBlockThresholdChargeColumns(final CellTable<BlockDto> blocksTable,
            final ThresholdChargeData thresholdCharge, int DEFAULT_BLOCKS_COLUMNS_COUNT) {

        if (blocksTable.getColumnCount() > DEFAULT_BLOCKS_COLUMNS_COUNT) {
            while (blocksTable.getColumnCount() > DEFAULT_BLOCKS_COLUMNS_COUNT) {
                blocksTable.removeColumn(DEFAULT_BLOCKS_COLUMNS_COUNT);
            }
            blocksTable.redraw();
        }

        final DecimalInputCell blockChargeCell;
        if (FormatUtil.getInstance().isRightToLeft()) {
            blockChargeCell = new DecimalInputCell("", FormatUtil.getInstance().getCurrencySymbol(), MessagesUtil.getInstance().getMessage("error.numeric.value"));
        } else {
            blockChargeCell = new DecimalInputCell(FormatUtil.getInstance().getCurrencySymbol(), "", MessagesUtil.getInstance().getMessage("error.numeric.value"));
        }

        final Column<BlockDto, String> blockChargePrice = new Column<BlockDto, String>(blockChargeCell) {
            @Override
            public String getValue(BlockDto data) {
                ThresholdChargeData thresholdCharge = data.getBlock().getThresholdCharge();
                if (thresholdCharge != null) {
                    return FormatUtil.getInstance().formatDecimal(thresholdCharge.getCharge());
                }
                return null;
            }
        };

        blockChargePrice.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
            @Override
            public void update(int index, BlockDto block, String value) {
                if (value != null && (blockChargeCell.isNumeric(value) || value.isEmpty())) {
                    getForm().setDirtyData(true);
                    ThresholdChargeData thresholdCharge = block.getBlock().getThresholdCharge();
                    if (thresholdCharge != null) {
                        thresholdCharge.setCharge(FormatUtil.getInstance().parseDecimal(value));
                    }
                    blockChargeCell.getViewData(block).setInvalid(false);
                } else {
                    blockChargeCell.getViewData(block).setInvalid(true); // Mark as invalid.
                }
                MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(blockChargePrice));
            }
        });
        blocksTable.addColumn(blockChargePrice, thresholdCharge.getName());
    }

}
