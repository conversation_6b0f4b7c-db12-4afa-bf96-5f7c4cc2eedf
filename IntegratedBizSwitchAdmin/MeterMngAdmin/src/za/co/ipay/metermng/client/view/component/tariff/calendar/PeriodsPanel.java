package za.co.ipay.metermng.client.view.component.tariff.calendar;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.ListDataProvider;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.PeriodsUpdatedEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.TouPeriod;

public class PeriodsPanel extends BaseComponent {

    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField(provided=true) CellTable<TouPeriod> periodsTable;
    @UiField TablePager periodsPager;
    @UiField Label formHeading;
    @UiField TextBox nameTextBox;
    @UiField TextBox codeTextBox;
    @UiField FormElement nameElement;
    @UiField FormElement codeElement;
    @UiField Button saveBtn;
    @UiField Button cancelBtn;
    @UiField Button deleteBtn;

    private ListDataProvider<TouPeriod> dataProvider;
    private SingleSelectionModel<TouPeriod> selectionModel;

    private TouPeriod period;

    private ArrayList<TouPeriod> periodsList;

    protected HasDirtyData hasDirtyData;
    private PopupPanel popupPanel = null;

    private static Logger logger = Logger.getLogger(PeriodsPanel.class.getName());


    private static PeriodsPanelUiBinder uiBinder = GWT.create(PeriodsPanelUiBinder.class);

    interface PeriodsPanelUiBinder extends UiBinder<Widget, PeriodsPanel> {
    }

    public PeriodsPanel(ClientFactory clientFactory, HasDirtyData hasDirtyData) {
        this.clientFactory = clientFactory;
        this.hasDirtyData = hasDirtyData;
        construct();
    }

    public PeriodsPanel(ClientFactory clientFactory, HasDirtyData hasDirtyData, PopupPanel popupPanel) {
        this.clientFactory = clientFactory;
        this.hasDirtyData = hasDirtyData;
        this.popupPanel = popupPanel;
        construct();
    }

    private void construct() {
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initTable();
        setPeriod(null);
        addFieldHandlers();
    }

    protected void createTable() {
        if (ResourcesFactoryUtil.getInstance() != null && ResourcesFactoryUtil.getInstance().getCellTableResources() != null) {
            periodsTable = new CellTable<TouPeriod>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
        } else {
            periodsTable = new CellTable<TouPeriod>(DEFAULT_PAGE_SIZE);
        }
    }

    protected void initTable() {
        if (dataProvider == null) {
            TextColumn<TouPeriod> periodName = new TextColumn<TouPeriod>() {
                @Override
                public String getValue(TouPeriod data) {
                    if (data.getName() != null) {
                        return data.getName();
                    }
                    return " ? ";
                }
            };
            periodName.setSortable(true);

            TextColumn<TouPeriod> periodCode = new TextColumn<TouPeriod>() {
                @Override
                public String getValue(TouPeriod data) {
                    if (data.getCode() != null) {
                        return data.getCode();
                    }
                    return " ? ";
                }
            };
            periodCode.setSortable(true);

            // Add the columns.
            periodsTable.addColumn(periodName, MessagesUtil.getInstance().getMessage("calendar.period.field.name"));
            periodsTable.addColumn(periodCode, MessagesUtil.getInstance().getMessage("calendar.period.field.code"));

            dataProvider = new ListDataProvider<TouPeriod>();
            dataProvider.addDataDisplay(periodsTable);
            periodsPager.setDisplay(periodsTable);
            periodsTable.setPageSize(getPageSize());

            ListHandler<TouPeriod> columnSortHandler = new ListHandler<TouPeriod>(dataProvider.getList());
            columnSortHandler.setComparator(periodName, new Comparator<TouPeriod>() {
                public int compare(TouPeriod o1, TouPeriod o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getName() != null) {
                        return (o2 != null && o2.getName() != null) ? o1.getName().compareTo(o2.getName()) : 1;
                    }
                    return -1;
                }
            });
            columnSortHandler.setComparator(periodCode, new Comparator<TouPeriod>() {
                public int compare(TouPeriod o1, TouPeriod o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getCode() != null) {
                        return (o2 != null && o2.getCode() != null) ? o1.getCode().compareTo(o2.getCode()) : 1;
                    }
                    return -1;
                }
            });
            periodsTable.addColumnSortHandler(columnSortHandler);
            periodsTable.getColumnSortList().push(periodName);
            logger.info("Created Period table");
        }

        selectionModel = new SingleSelectionModel<TouPeriod>();
        CellPreviewEvent.Handler<TouPeriod> handler = new CellPreviewEvent.Handler<TouPeriod>() {
            final CellPreviewEvent.Handler<TouPeriod> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<TouPeriod> event) {
                    if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                        if (!event.isCanceled()) {
                            if (hasDirtyData.isDirtyData()) {
                                Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                                            @Override
                                            public void confirmed(boolean confirm) {
                                                if (confirm) {
                                                    hasDirtyData.setDirtyData(false);
                                                    event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                    logger.info("Discarding changes for selection event");
                                                } else {
                                                    event.setCanceled(true);
                                                    logger.info("Cancelled selection event, staying with current selection");
                                                }

                                            }});
                            } else {
                                selectionEventManager.onCellPreview(event);
                            }

                        }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }
        };
        periodsTable.setSelectionModel(selectionModel, handler);
        selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
            public void onSelectionChange(SelectionChangeEvent event) {

                final TouPeriod selected = selectionModel.getSelectedObject();
                if (selected != null) {
                    setPeriod(selected);
                    periodsTable.getSelectionModel().setSelected(selected, true);
                }
            }
        });
    }

    public void clearTableSelection() {
        TouPeriod selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }
    }

    public void refreshTable() {
        clientFactory.getCalendarRpc().getPeriods(new ClientCallback<ArrayList<TouPeriod>>() {
            @Override
            public void onSuccess(ArrayList<TouPeriod> result) {
                logger.info("Got periods: " + result.size());
                if (dataProvider != null && dataProvider.getList() != null) {
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    setPeriodList(result);
                }
            }
        });
    }

    public void setPeriodList(ArrayList<TouPeriod> thedata) {
        logger.info("Displaying Periods: "+thedata.size());
        periodsList = thedata;
        dataProvider.getList().clear();
        dataProvider.getList().addAll(thedata);
        dataProvider.refresh();
    }

    private void setPeriod(TouPeriod theperiod) {
        clearErrors();
        clearFields();
        this.period = theperiod;
        if (theperiod != null) {
            saveBtn.setText(MessagesUtil.getInstance().getMessage("button.update"));
            deleteBtn.setVisible(true);
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.period.update"));
        } else {
            period = new TouPeriod();
            period.setRecordStatus(RecordStatus.ACT);
            saveBtn.setText(MessagesUtil.getInstance().getMessage("button.create"));
            deleteBtn.setVisible(false);
            formHeading.setText(MessagesUtil.getInstance().getMessage("calendar.period.add"));
            clearTableSelection();
            hasDirtyData.setDirtyData(false);
        }

        nameTextBox.setText(period.getName());
        codeTextBox.setText(period.getCode());
    }

    public void clearFields() {
        nameTextBox.setText("");
        codeTextBox.setText("");
    }

    public void clearErrors() {
        nameElement.clearErrorMsg();
        codeElement.clearErrorMsg();
    }

    @UiHandler("saveBtn")
    public void save(ClickEvent e) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                if (period.getId() == null) {
                    addPeriod();
                } else {
                    updatePeriod();
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("cancelBtn")
    public void cancel(ClickEvent e) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        setPeriod(null);
                        if (popupPanel != null) {
                            popupPanel.hide();
                        }
                    }
                }});
        } else {
            setPeriod(null);
            if (popupPanel != null) {
                popupPanel.hide();
            }
        }
    }

    @UiHandler("deleteBtn")
    public void delete(ClickEvent e) {
        if (period.getId() != null) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    deletePeriod();
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private void update() {
        if (period == null) {
            this.period = new TouPeriod();
        }
        period.setName(nameTextBox.getText().isEmpty() ? null : nameTextBox.getText());
        period.setCode(codeTextBox.getText().isEmpty() ? null : codeTextBox.getText());
        period.setRecordStatus(RecordStatus.ACT);
    }

    private boolean isValid() {
        boolean valid = true;
        clearErrors();
        TouPeriod aperiod = new TouPeriod();
        aperiod.setName(nameTextBox.getText().isEmpty() ? null : nameTextBox.getText());
        aperiod.setCode(codeTextBox.getText().isEmpty() ? null : codeTextBox.getText());
        if (!ClientValidatorUtil.getInstance().validateField(aperiod, "name", nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(aperiod, "code", codeElement)) {
            valid = false;
        }
        return valid;
    }

    public void addPeriod() {
        if (isValid()) {
            update();
            clientFactory.getCalendarRpc().addPeriod(period, new ClientCallback<TouPeriod>(saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouPeriod result) {
                    if (result != null) {
                        hasDirtyData.setDirtyData(false);
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("calendar.period.title") }), MediaResourceUtil.getInstance().getInformationIcon(), saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                        periodsList.add(result);
                        dataProvider.setList(periodsList);
                        dataProvider.refresh();
                        setPeriod(null);
                        clientFactory.getEventBus().fireEvent(new PeriodsUpdatedEvent());
                        if (popupPanel != null) {
                            popupPanel.hide();
                        }
                    }
                }
            });
        }
    }

    public void updatePeriod() {
        if (isValid()) {
            update();
            clientFactory.getCalendarRpc().updatePeriod(period, new ClientCallback<TouPeriod>(saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouPeriod result) {
                    hasDirtyData.setDirtyData(false);
                    if (result != null) {
                        int index = -1;
                        for (int i = 0; i < periodsList.size(); i++) {
                            if (periodsList.get(i).getId().equals(result.getId())) {
                                index = i;
                                break;
                            }
                        }
                        if (index > -1) {
                            setPeriod(null);
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("calendar.period.title") }), MediaResourceUtil.getInstance().getInformationIcon(), saveBtn.getAbsoluteLeft(), saveBtn.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                            periodsList.set(index, result);
                            dataProvider.setList(periodsList);
                            dataProvider.refresh();
                            clientFactory.getEventBus().fireEvent(new PeriodsUpdatedEvent());
                        }
                        if (popupPanel != null) {
                            popupPanel.hide();
                        }
                    }
                }
            });
        }
    }

    public void deletePeriod() {
        if (isValid()) {
            update();
            clientFactory.getCalendarRpc().deletePeriod(period, new ClientCallback<TouPeriod>(deleteBtn.getAbsoluteLeft(), deleteBtn.getAbsoluteTop()) {
                @Override
                public void onSuccess(TouPeriod result) {
                    if (result != null) {
                        hasDirtyData.setDirtyData(false);
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.period.deleted", new String[] { result.getName() }), MediaResourceUtil.getInstance().getInformationIcon(), deleteBtn.getAbsoluteLeft(), deleteBtn.getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));
                        periodsList.remove(result);
                        clearTableSelection();
                        refreshTable();
                        setPeriod(null);
                        clientFactory.getEventBus().fireEvent(new PeriodsUpdatedEvent());
                        if (popupPanel != null) {
                            popupPanel.hide();
                        }
                    }
                }
            });
        }
    }

    protected void addFieldHandlers() {
        nameTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        codeTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

}
