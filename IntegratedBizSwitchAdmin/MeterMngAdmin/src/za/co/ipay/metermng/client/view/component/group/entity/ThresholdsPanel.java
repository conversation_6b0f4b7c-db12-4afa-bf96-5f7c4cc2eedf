package za.co.ipay.metermng.client.view.component.group.entity;

import java.util.List;
import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAccThresholds;
import za.co.ipay.metermng.shared.GenGroupData;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.Widget;

public class ThresholdsPanel extends BaseComponent {

    @UiField FlowPanel thresholdsValuesFlowPanel;
    @UiField ThresholdsPopup thresholdsValuesPanel;
    @UiField Label errorMsg;

    @UiField HTMLPanel deleteButtonPanel;
    @UiField Button btnDelete;

    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField Button btnBack;
    @UiField Button btnAdd;
    @UiField Button btnShowInherited;

    private GenGroupParentComponent parentWorkspace;
    private GenGroupData genGroupData;
    private CustomerAccThresholds thresholds;

    private CustomerAccThresholds globalSettings;
    private ThresholdsPopup inheritedThresholdsPopup;

    private static ThresholdsPanelUiBinder uiBinder = GWT.create(ThresholdsPanelUiBinder.class);

    interface ThresholdsPanelUiBinder extends UiBinder<Widget, ThresholdsPanel> {
    }

    private static Logger logger = Logger.getLogger(ThresholdsPanel.class.getName());

    public ThresholdsPanel(GenGroupParentComponent parentWorkspace, ClientFactory clientFactory) {
        this.parentWorkspace = parentWorkspace;
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        getGlobalSettings();
        thresholdsValuesPanel.setHasDirtyDataManager(parentWorkspace.getHasDirtyDataManager());
        thresholdsValuesPanel.addFieldHandlers();
    }

    public void getGlobalSettings() {
        clientFactory.getGroupRpc().getGlobalThresholds(new ClientCallback<CustomerAccThresholds>() {
            @Override
            public void onSuccess(CustomerAccThresholds result) {
                globalSettings = result;
            }
        });
    }

    @UiHandler("btnSave")
    public void save(ClickEvent e) {
        save();
    }

    @UiHandler("btnCancel")
    public void cancel(ClickEvent e) {
        thresholdsValuesPanel.getHasDirtyData().checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    thresholdsValuesPanel.getHasDirtyData().setDirtyData(false);
                    clearErrors();
                    SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                        @Override
                        public void callback(SessionCheckResolution resolution) {
                            loadThresholds(genGroupData);
                        }
                    };
                    clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                }
            }
        });
    }

    @UiHandler("btnDelete")
    public void delete(ClickEvent e) {
        deleteThresholds();
    }

    @UiHandler("btnBack")
    public void back(ClickEvent e) {
        parentWorkspace.goBack();
    }

    @UiHandler("btnAdd")
    public void add(ClickEvent e) {
        add();
    }

    @UiHandler("btnShowInherited")
    public void showInheritedValues(ClickEvent e) {
        if (genGroupData.getParent() == null || genGroupData.getParent().getCustomerAccThresholdsId() == null) {
            inheritedThresholdsPopup = new ThresholdsPopup(globalSettings);
            inheritedThresholdsPopup.show(btnShowInherited.getAbsoluteLeft(), btnShowInherited.getAbsoluteTop(), "global");
        } else {
            clientFactory.getGroupRpc().getGenGroupThresholds(genGroupData.getParent().getCustomerAccThresholdsId(), new ClientCallback<CustomerAccThresholds>() {
                @Override
                public void onSuccess(CustomerAccThresholds result) {
                    inheritedThresholdsPopup = new ThresholdsPopup(result);
                    inheritedThresholdsPopup.show(btnShowInherited.getAbsoluteLeft(), btnShowInherited.getAbsoluteTop(), "parent");
                }
            });
        }
    }

    protected void display(CustomerAccThresholds thresholds, Boolean isDeleteVisible) {
        this.thresholds = thresholds;

        thresholdsValuesFlowPanel.setVisible(true);
        thresholdsValuesPanel.display(thresholds);

        deleteButtonPanel.setVisible(isDeleteVisible);

        btnSave.setVisible(true);
        if (isDeleteVisible) {
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.update"));
        } else {
            btnSave.setText(MessagesUtil.getInstance().getMessage("button.create"));
        }
        btnCancel.setVisible(true);
        btnAdd.setVisible(false);
        btnShowInherited.setVisible(false);
    }

    private void clearErrors() {
        thresholdsValuesPanel.clearErrors();
        clearErrorMsg();
    }
    private void clearErrorMsg() {
        errorMsg.setText("");
        errorMsg.setVisible(false);
    }

    public void loadThresholds(GenGroupData genGroupData) {
        logger.info("ThresholdsPanel: loadThresholds(" + genGroupData.getName() + ")");
        this.genGroupData = genGroupData;
        thresholds = null;

        //Only display the thresholds if genGroup has its own threshold Id (not=parent) which is not null
        boolean mythresholdIdIsNull = true;
        boolean parentThresholdIdIsNull = true;
        if (genGroupData.getCustomerAccThresholdsId() != null) {
            mythresholdIdIsNull = false;
        }
        if (genGroupData.getParent() != null && genGroupData.getParent().getCustomerAccThresholdsId() != null) {
            parentThresholdIdIsNull = false;
        }
        boolean myThresholdIdEqualsParent = false;
        if ((parentThresholdIdIsNull && mythresholdIdIsNull)
                ||(!mythresholdIdIsNull && !parentThresholdIdIsNull && genGroupData.getCustomerAccThresholdsId().equals(genGroupData.getParent().getCustomerAccThresholdsId()))) {
            myThresholdIdEqualsParent = true;
        }

        if ((genGroupData.getParent() == null && mythresholdIdIsNull) || myThresholdIdEqualsParent) {
            btnSave.setVisible(false);
            btnCancel.setVisible(false);
            btnAdd.setVisible(true);
            btnShowInherited.setVisible(true);
            thresholdsValuesFlowPanel.setVisible(false);
            deleteButtonPanel.setVisible(false);
            logger.info(genGroupData.getName() + " : uniqueThresholds=NO");
        } else {
            logger.info(genGroupData.getName() + " uniqueThresholds=YES, thresholdId=" + genGroupData.getCustomerAccThresholdsId());
            getAndDisplayThresholds(genGroupData.getCustomerAccThresholdsId(), true);
        }
    }

    private void getAndDisplayThresholds(Long customerAccThresholdsId, final boolean isDeleteVisible) {
        clientFactory.getGroupRpc().getGenGroupThresholds(customerAccThresholdsId, new ClientCallback<CustomerAccThresholds>() {
            @Override
            public void onSuccess(CustomerAccThresholds result) {
                display(result, isDeleteVisible);
            }
        });
    }

    private void add() {
        CustomerAccThresholds addThresholds = new CustomerAccThresholds();
        addThresholds.setId(null);
        addThresholds.setDisconnect(null);
        addThresholds.setEmergencyCredit(null);
        addThresholds.setReconnect(null);
        addThresholds.setLowBalance(null);
        display(addThresholds, false);
    }

    private void save() {
        final CustomerAccThresholds thresholdsToSave = thresholdsValuesPanel.mapFormToData();
        thresholdsToSave.setId(thresholds.getId());

        if (isValidInput(thresholdsToSave)) {
            if(!haveValuesChanged(thresholdsToSave)) {
                //When create / update will instigate a reload of genGroups - avoid if no change!!
                logger.info("Thresholds no change.");
                return;
            }

            btnSave.setEnabled(false);
            //children will potentially change - continue?
            if (genGroupData.getChildren() != null && !genGroupData.getChildren().isEmpty()) {
                Dialogs.confirm (
                        MessagesUtil.getInstance().getMessage("groupthreshold.children.change.alert"),
                        ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                        ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                        ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                        new ConfirmHandler() {
                            @Override
                            public void confirmed(boolean confirm) {
                                if (confirm) {
                                    saveThresholds(thresholdsToSave);
                                } else {
                                    btnSave.setEnabled(true);
                                }
                            }
                        });
            } else {
                saveThresholds(thresholdsToSave);
            }
        }
    }

    private void saveThresholds(final CustomerAccThresholds thresholdsToSave) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getGroupRpc().updateThresholds(genGroupData, thresholdsToSave,
                        new ClientCallback<CustomerAccThresholds>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
                            @Override
                            public void onSuccess(CustomerAccThresholds result) {
                                thresholds = result;
                                thresholdsValuesPanel.getHasDirtyData().setDirtyData(false);

                                //if Don't go back to main tree page, "refresh" the current genGroupData instance
                                Long existingThresholdId = genGroupData.getCustomerAccThresholdsId();
                                genGroupData.setCustomerAccThresholdsId(result.getId());
                                updateChildrenAfterSuccess(genGroupData.getChildren(), existingThresholdId, result.getId());

                                display(result, true);
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("groupthreshold.title") }),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
                                btnSave.setEnabled(true);
                                parentWorkspace.reLoadGroups(genGroupData);
                            }

                            @Override
                            public void onFailureClient() {
                                btnSave.setEnabled(true);
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private boolean isValidInput(CustomerAccThresholds thresholdsToSave) {
        logger.info("Doing validation...");
        clearErrorMsg();
        boolean valid = true;
        clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(thresholdsToSave, "disconnect", thresholdsValuesPanel.meterDisconnectElement)) {
            valid = false;
        }
        if (thresholdsToSave.getDisconnect() == null) {
            thresholdsValuesPanel.meterDisconnectElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.disconnect.null"));
            valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(thresholdsToSave, "lowBalance", thresholdsValuesPanel.lowBalanceElement)) {
            valid = false;
        }
        if (thresholdsToSave.getLowBalance() == null) {
            thresholdsValuesPanel.lowBalanceElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.lowbalance.null"));
            valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(thresholdsToSave, "reconnect", thresholdsValuesPanel.meterReconnectElement)) {
            valid = false;
        }
        if (thresholdsToSave.getReconnect() == null) {
            thresholdsValuesPanel.meterReconnectElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.reconnect.null"));
            valid = false;
        }

        if (!ClientValidatorUtil.getInstance().validateField(thresholdsToSave, "emergencyCredit", thresholdsValuesPanel.emergencyCreditElement)) {
            valid = false;
        }
        if (thresholdsToSave.getEmergencyCredit() == null) {
            thresholdsValuesPanel.emergencyCreditElement.setErrorMsg(MessagesUtil.getInstance().getMessage("error.field.emergencycredit.null"));
            valid = false;
        }

        //Disconnect must be <= Emergency Credit
        if (valid && thresholdsToSave.getDisconnect() != null && thresholdsToSave.getEmergencyCredit() != null
                && thresholdsToSave.getDisconnect().compareTo(thresholdsToSave.getEmergencyCredit()) > 0) {
            errorMsg.setVisible(true);
            errorMsg.setText(MessagesUtil.getInstance().getMessage("groupthreshold.error.disconnect.greater.emergency.credit"));
            valid = false;
        }

        //Disconnect must be <= Reconnect
        if (valid && thresholdsToSave.getDisconnect() != null && thresholdsToSave.getReconnect() != null
                && thresholdsToSave.getDisconnect().compareTo(thresholdsToSave.getReconnect()) > 0) {
            errorMsg.setVisible(true);
            errorMsg.setText(MessagesUtil.getInstance().getMessage("groupthreshold.error.disconnect.greater.reconnect"));
            valid = false;
        }


        //Emergency Credit <= Low Balance
        if (valid && thresholdsToSave.getEmergencyCredit() != null && thresholdsToSave.getLowBalance() != null
                && thresholdsToSave.getEmergencyCredit().compareTo(thresholdsToSave.getLowBalance()) > 0) {
            errorMsg.setVisible(true);
            errorMsg.setText(MessagesUtil.getInstance().getMessage("groupthreshold.error.emergency.credit.greater.low.balance"));
            valid = false;
        }

        logger.info("Done validation: " + valid);
        return valid;
    }

    private boolean haveValuesChanged(CustomerAccThresholds thresholdsToSave) {
        boolean change = false;
        if (thresholdsToSave.getDisconnect() != thresholds.getDisconnect()
                || thresholdsToSave.getEmergencyCredit() != thresholds.getEmergencyCredit()
                || thresholdsToSave.getReconnect() != thresholds.getReconnect()
                || thresholdsToSave.getLowBalance() != thresholds.getLowBalance()) {
            change = true;
        }
        return change;
    }

    private void deleteThresholds() {
        btnDelete.setEnabled(false);
        String source = "Parent";
        if (genGroupData.getParent() == null) {
             source = "Global";
        }
        Dialogs.confirm (
                MessagesUtil.getInstance().getMessage("groupthreshold.revert.parent.global", new String[] {source}),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (genGroupData.getParent() != null && genGroupData.getParent().getCustomerAccThresholdsId() != null) {
                                deleteThresholdsStep2(genGroupData.getParent().getCustomerAccThresholdsId());
                            } else {
                                deleteThresholdsStep2(null);   //"set" to global
                            }
                        } else {
                            btnDelete.setEnabled(true);
                        }
                    }
                });
    }

    private void deleteThresholdsStep2(final Long revertToThresholdId) {
        if (genGroupData.getChildren() != null && !genGroupData.getChildren().isEmpty()) {
            Dialogs.confirm (
                    MessagesUtil.getInstance().getMessage("groupthreshold.children.change.alert"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.positive"),
                    ResourcesFactoryUtil.getInstance().getMessages().getMessage("option.no"),
                    ResourcesFactoryUtil.getInstance().getQuestionIcon(),
                    new ConfirmHandler() {
                        @Override
                        public void confirmed(boolean confirm) {
                            if (confirm) {
                                revertThreshold(revertToThresholdId);
                            } else {
                                btnDelete.setEnabled(true);
                            }
                        }
                    });
        } else {
            revertThreshold(revertToThresholdId);
        }
    }

    private void revertThreshold(final Long revertToThresholdId) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getGroupRpc().deleteThresholds(genGroupData, revertToThresholdId,
                        new ClientCallback<CustomerAccThresholds>(btnDelete.getAbsoluteLeft(), btnDelete.getAbsoluteTop() + btnDelete.getOffsetHeight()) {
                            @Override
                            public void onSuccess(CustomerAccThresholds result) {
                                //if Don't go back to main tree page but press delete here directly after create, "refresh" the current genGroupData instance
                                thresholdsValuesPanel.getHasDirtyData().setDirtyData(false);
                                Long existingThresholdId = genGroupData.getCustomerAccThresholdsId();
                                genGroupData.setCustomerAccThresholdsId(revertToThresholdId);
                                updateChildrenAfterSuccess(genGroupData.getChildren(), existingThresholdId, revertToThresholdId);

                                if (result == null) {
                                    genGroupData.setCustomerAccThresholdsId(null);
                                } else {
                                    genGroupData.setCustomerAccThresholdsId(result.getId());
                                }
                                loadThresholds(genGroupData);
                                btnDelete.setEnabled(true);
                                Dialogs.displayInformationMessage(
                                        MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("groupthreshold.title") }),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        btnBack.getAbsoluteLeft(), btnBack.getAbsoluteTop(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
                                parentWorkspace.reLoadGroups(genGroupData);
                            }

                            @Override
                            public void onFailureClient() {
                                btnDelete.setEnabled(true);
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }
    private void updateChildrenAfterSuccess(List<GenGroupData> genGroupChildren, Long existingThresholdId, Long newThresholdId) {
        if (genGroupChildren != null) {
            for (GenGroupData ggdChild : genGroupChildren) {
                if ((ggdChild.getCustomerAccThresholdsId() == null && existingThresholdId == null)
                    || (ggdChild.getCustomerAccThresholdsId() != null && existingThresholdId != null
                        && ggdChild.getCustomerAccThresholdsId().equals(existingThresholdId))) {
                    logger.info("Updating children: " + ggdChild.getName() + " thresholdId=" + newThresholdId);
                    ggdChild.setCustomerAccThresholdsId(newThresholdId);
                }
                updateChildrenAfterSuccess(ggdChild.getChildren(), existingThresholdId, newThresholdId);
            }
        }
    }

}
