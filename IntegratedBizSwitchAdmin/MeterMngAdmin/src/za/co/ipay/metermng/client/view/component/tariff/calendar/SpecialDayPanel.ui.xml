<!DOCTYPE ui:UiBinder SYSTEM "http://dl.google.com/gwt/DTD/xhtml.ent">
<ui:UiBinder xmlns:ui="urn:ui:com.google.gwt.uibinder" 
             xmlns:g="urn:import:com.google.gwt.user.client.ui"
             xmlns:g2="urn:import:com.google.gwt.user.cellview.client"
             xmlns:datep="urn:import:com.google.gwt.user.datepicker.client"
             xmlns:form="urn:import:za.co.ipay.gwt.common.client.form"
             xmlns:widget="urn:import:za.co.ipay.gwt.common.client.widgets">
  <ui:style>
    
  </ui:style>
  
    <ui:with field="msg" type="za.co.ipay.metermng.client.i18n.UiMessages" />

    <g:FlowPanel width="99%" height="100%">
        <g:VerticalPanel spacing="5"> 
	        <g:HTMLPanel ui:field="specialdayPanel" debugId="specialdayPanel">
	            <g:HTML text="{msg.getCalendarSpecialdayTitle}" styleName="dataTitle" />
	            <g:HTML text="{msg.getCalendarSpecialdayDescription}" styleName="dataDescription" />     
	            <g2:CellTable ui:field="specialdayTable" debugId="specialdayTable"/>
                <widget:TablePager ui:field="specialdayPager" styleName="pager" location="CENTER" />
	        </g:HTMLPanel>
	        <g:FlowPanel ui:field="formPanel" debugId="createSpecialdayPanel">
	            <g:Label ui:field="formHeading" styleName="sectionTitle"></g:Label>
	            <form:FormRowPanel>
	                <form:FormElement ui:field="nameElement" debugId="sdNameElement" labelText="{msg.getCalendarSpecialdayName}:" helpMsg="{msg.getCalendarSpecialdayNameHelp}" required="true">
	                    <g:TextBox text="" ui:field="nameTextBox" debugId="sdNameTextBox" title="{msg.getCalendarSpecialdayName}" styleName="gwt-TextBox-round" />
	                </form:FormElement>
	                <form:FormElement ui:field="dateElement" debugId="sdDateElement" labelText="{msg.getCalendarSpecialdayDate}:" helpMsg="{msg.getCalendarSpecialdayDateHelp}" required="true">
                      <datep:DateBox debugId="sdDate" styleName="gwt-TextBox" ui:field="sdDateBox" />
                  </form:FormElement>
                  <form:FormElement ui:field="dayProfileElement" debugId="sdDayProfileElement" labelText="{msg.getCalendarSpecialdayProfile}:" helpMsg="{msg.getCalendarSpecialdayProfileHelp}" required="true">
                      <g:SuggestBox ui:field="dayProfile" debugId="sdDayProfile" animationEnabled="true" autoSelectEnabled="true" styleName="gwt-TextBox-round" />
                  </form:FormElement>
	            </form:FormRowPanel>
	            <g:Label ui:field="errorMsg" debugId="sdErrorMsg" visible="false" styleName="error"></g:Label>
	            <g:HorizontalPanel spacing="5" ui:field="buttons">
	                 <g:Button ui:field="btnSave" debugId="sdBtnSave" text="{msg.getSaveButton}" />
	                 <g:Button ui:field="btnDelete" debugId="sdBtnDelete" text="{msg.getDeleteButton}" />
	                 <g:Button ui:field="btnCancel" debugId="sdBtnCancel" text="{msg.getCancelButton}" />
	            </g:HorizontalPanel>  
	        </g:FlowPanel>
	        
        </g:VerticalPanel>        
    </g:FlowPanel>
</ui:UiBinder> 