package za.co.ipay.metermng.client.view.component.meter;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.NoSelectionModel;
import com.google.gwt.view.client.SelectionChangeEvent;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.component.TransactionItemView;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.dto.MeterData;

public class MeterTransactionView extends BaseComponent implements IpayDataProviderFilter<CustomerTransAlphaDataWithTotals>{
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static MeterTransactionWidgetUiBinder uiBinder = GWT.create(MeterTransactionWidgetUiBinder.class);

    interface MeterTransactionWidgetUiBinder extends UiBinder<Widget, MeterTransactionView> {
    }

    @UiField(provided=true) CellTable<CustomerTransAlphaDataWithTotals> clltblTransactions;
    @UiField TablePager smplpgrTransactions;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField Button btnExportCsv;
    @UiField DateRangeFilterPanel dateFilter;

    private MeterData meterData;
    private Column<CustomerTransAlphaDataWithTotals, String> transDateCol;
    private TextColumn<CustomerTransAlphaDataWithTotals> customerTransType;
    private IPayDataProvider<CustomerTransAlphaDataWithTotals> dataProvider = new IPayDataProvider<>(this);
    private ArrayList<CustomerTransAlphaDataWithTotals> theTransactiondata;
    private ListHandler<CustomerTransAlphaDataWithTotals> columnSortHandler;
    private ColumnSortList columnSortList;

    private TextColumn<CustomerTransAlphaDataWithTotals> stdTokenAmtCol;
    private TextColumn<CustomerTransAlphaDataWithTotals> fixedAmtCol;
    private TextColumn<CustomerTransAlphaDataWithTotals> auxAmtCol;
    private TextColumn<CustomerTransAlphaDataWithTotals> stdUnitsCol;
    private TextColumn<CustomerTransAlphaDataWithTotals> fbeUnitsCol;

    int l;
	int t;

	private UsagePointWorkspaceView usagePointWorkspaceView;

    public MeterTransactionView(ClientFactory clientFactory, UsagePointWorkspaceView usagePointWorkspaceView) {
        setClientFactory(clientFactory);
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void initUi() {
        initView();
        initTable();
    }

    private void initView() {
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.txn.ref"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.txn.receiptnum"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("meter.txn.date"));
        dataName.setText(MessagesUtil.getInstance().getMessage("meter.txn.history"));
        dataDescription.setText(MessagesUtil.getInstance().getMessage("meter.txn.history.description"));
    }

    protected void createTable() {
    	 clltblTransactions = new CellTable<>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private void initTable() {
        transDateCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return FormatUtil.getInstance().formatDateTime(data.getTransDate());
            }
        };
        transDateCol.setSortable(true);
        transDateCol.setDefaultSortAscending(false);

        TextColumn<CustomerTransAlphaDataWithTotals> customer = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                String thename = "";
                if (data.getTitle() != null) {
                    thename += (data.getTitle() + " ");
                }
                if (data.getInitials() != null) {
                    thename += (data.getInitials() + " ");
                } else if (data.getFirstnames() != null) {
                    thename += (data.getFirstnames() + " ");
                }
                if (data.getSurname() != null) {
                    thename += data.getSurname();
                }
                return thename;
            }
        };

        customerTransType = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
        	if (data.getTransTypeName() != null) {
        	    return data.getTransTypeName();
	    	}
        	return "-";
            }
        };
        customerTransType.setSortable(true);

        TextColumn<CustomerTransAlphaDataWithTotals> client = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
        	if (data.getClient() != null) {
        	    return data.getClient();
    	    	}
        	return "-";
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> term = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
        	if (data.getTerminal()!= null) {
        	    return data.getTerminal();
    	    	}
        	return "-";
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> refReceived = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return data.getVendRefReceived();
            }
        };

        AbstractCell<String> reversalCell = new AbstractCell<String>() {
            @Override
            public void render(Context context, String value, SafeHtmlBuilder sb) {
                if (value == null) {
                  return;
                 }
                 if (value.equals(MessagesUtil.getInstance().getMessage("button.yes"))) {
                     sb.appendHtmlConstant("<span class=\"error\">" + value + "</span>");
                 } else {
                     //sb.appendHtmlConstant("<span class=\"success\">" + value + "</span>");
                     sb.appendHtmlConstant("<span style=\"text-align:center; display:block;\">" + value + "</span>");
                 }
            }
        };
        Column<CustomerTransAlphaDataWithTotals, String> reversalBool = new Column<CustomerTransAlphaDataWithTotals, String>(reversalCell) {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                if (data.isReversed()) {
                    return MessagesUtil.getInstance().getMessage("button.yes");
                } else {
                    return MessagesUtil.getInstance().getMessage("button.no");
                }
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> revRefReceived = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return data.getRevRefReceived();
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> reversalReason = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return data.getReversalReason();
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> reversedBy = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return data.getReversedBy();
            }
        };


        Column<CustomerTransAlphaDataWithTotals, String> reprintDateCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return FormatUtil.getInstance().formatDateTime(data.getLastReprintDt());
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> receiptNumber = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return data.getReceiptNum();
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> amtCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                return FormatUtil.getInstance().formatCurrency(data.getAmtInclTax(), true);
            }
        };

        TextColumn<CustomerTransAlphaDataWithTotals> hasEngineeringTokensCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                if (data.isHasEngineeringTokens()) {
                    return MessagesUtil.getInstance().getMessage("button.yes");
                } else {
                    return MessagesUtil.getInstance().getMessage("button.no");
                }
            }
        };

        Messages messages = MessagesUtil.getInstance();
        clltblTransactions.addColumn(transDateCol, messages.getMessage("meter.txn.date"));
        clltblTransactions.addColumn(customerTransType, messages.getMessage("meter.txn.type"));
        clltblTransactions.addColumn(customer, messages.getMessage("meter.txn.customer"));
        clltblTransactions.addColumn(client, messages.getMessage("meter.txn.client"));
        clltblTransactions.addColumn(term, messages.getMessage("meter.txn.term"));
        clltblTransactions.addColumn(refReceived, messages.getMessage("meter.txn.ref"));
        clltblTransactions.addColumn(reversalBool, messages.getMessage("meter.txn.isreversed"));
        clltblTransactions.addColumn(revRefReceived, messages.getMessage("meter.txn.revref"));
        clltblTransactions.addColumn(reversalReason, messages.getMessage("meter.txn.reversal.reason"));
        clltblTransactions.addColumn(reversedBy, messages.getMessage("meter.txn.reversed.by"));
        clltblTransactions.addColumn(reprintDateCol, messages.getMessage("meter.txn.reprint.date"));
        clltblTransactions.addColumn(receiptNumber, messages.getMessage("meter.txn.receiptnum"));
        clltblTransactions.addColumn(amtCol, messages.getMessage("meter.txn.amount.column"));
        clltblTransactions.addColumn(hasEngineeringTokensCol, messages.getMessage("meter.txn.engineeringtokens.column"));

        stdTokenAmtCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                if (data.getStandardTokenTotal()!= null) {
                    return FormatUtil.getInstance().formatCurrency(data.getStandardTokenTotal(), true);
                }
                return "-";
            }
        };

        fixedAmtCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                if (data.getFixedCostsTotal() != null) {
                    return FormatUtil.getInstance().formatCurrency(data.getFixedCostsTotal(), true);
                }
                return "-";
            }
        };

        auxAmtCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                if (data.getAuxPaymentTotal() != null) {
                    return FormatUtil.getInstance().formatCurrency(data.getAuxPaymentTotal(), true);
                }
                return "-";
            }
        };

        stdUnitsCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                if (data.getStdUnitsTotal() != null) {
                    return data.getStdUnitsTotal().toString();
                }
                return "-";
            }
        };

        fbeUnitsCol = new TextColumn<CustomerTransAlphaDataWithTotals>() {
            @Override
            public String getValue(CustomerTransAlphaDataWithTotals data) {
                if (data.getFbeUnitsTotal() != null) {
                    return data.getFbeUnitsTotal().toString();
                }
                return "-";
            }
        };

        toggleTotalsColumnsByAppSetting();

        dataProvider.addDataDisplay(clltblTransactions);
        smplpgrTransactions.setDisplay(clltblTransactions);

        final NoSelectionModel<CustomerTransAlphaDataWithTotals> mySelectionModel = new NoSelectionModel<>();

        clltblTransactions.setSelectionModel(mySelectionModel);
        mySelectionModel.addSelectionChangeHandler( new SelectionChangeEvent.Handler() {

			@Override
			public void onSelectionChange(SelectionChangeEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution sessionCheckResolution) {
                        showSelectedTransactionItem(mySelectionModel.getLastSelectedObject());
                    }
                };
			clientFactory.handleSessionCheckCallback(sessionCheckCallback);
			}
		});
        clltblTransactions.addDomHandler(new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                //Get parents absolute left so popup is always displayed starting on the left of visible table.
            	l = getParent().getAbsoluteLeft();
            	t = event.getClientY();
            }
        }, ClickEvent.getType());


        t = clltblTransactions.getAbsoluteTop()-(clltblTransactions.getBodyHeight()+clltblTransactions.getHeaderHeight());
        dateFilter.setDataProvider(dataProvider);
    }

    public void toggleTotalsColumnsByAppSetting() {
        clientFactory.getAppSettingRpc().getAppSettingByKey("transaction.history.table.totals.enabled", new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                String appSettingValue = result.getValue();
                if ("true".equals(appSettingValue.toLowerCase())) {
                    if (clltblTransactions.getColumnIndex(stdTokenAmtCol) == -1) {
                        clltblTransactions.addColumn(stdTokenAmtCol,
                                MessagesUtil.getInstance().getMessage("transaction.history.column.header.stdtoken"));
                    }
                    if (clltblTransactions.getColumnIndex(fixedAmtCol) == -1) {
                        clltblTransactions.addColumn(fixedAmtCol,
                                MessagesUtil.getInstance().getMessage("transaction.history.column.header.fixedamt"));
                    }
                    if (clltblTransactions.getColumnIndex(auxAmtCol) == -1) {
                        clltblTransactions.addColumn(auxAmtCol,
                                MessagesUtil.getInstance().getMessage("transaction.history.column.header.auxamt"));
                    }
                    if (clltblTransactions.getColumnIndex(stdUnitsCol) == -1) {
                        clltblTransactions.addColumn(stdUnitsCol,
                                MessagesUtil.getInstance().getMessage("transaction.history.column.header.stdunits"));
                    }
                    if (clltblTransactions.getColumnIndex(fbeUnitsCol) == -1) {
                        clltblTransactions.addColumn(fbeUnitsCol,
                                MessagesUtil.getInstance().getMessage("transaction.history.column.header.fbeunits"));
                    }
                } else {
                    if (clltblTransactions.getColumnIndex(stdTokenAmtCol) > -1) {
                        clltblTransactions.removeColumn(stdTokenAmtCol);
                    }
                    if (clltblTransactions.getColumnIndex(fixedAmtCol) > -1) {
                        clltblTransactions.removeColumn(fixedAmtCol);
                    }
                    if (clltblTransactions.getColumnIndex(auxAmtCol) > -1) {
                    clltblTransactions.removeColumn(auxAmtCol);
                    }
                    if (clltblTransactions.getColumnIndex(stdUnitsCol) > -1) {
                        clltblTransactions.removeColumn(stdUnitsCol);
                    }
                    if (clltblTransactions.getColumnIndex(fbeUnitsCol) > -1) {
                        clltblTransactions.removeColumn(fbeUnitsCol);
                    }
                }
                clltblTransactions.redraw();
            }
        });
    }

    private void showSelectedTransactionItem(final CustomerTransAlphaDataWithTotals lastSelectedObject) {
        WaitingDialogUtil.getInstance(MediaResourceUtil.getInstance().getWaitIcon(), l, t);
        TransactionItemView transactionItemView = new TransactionItemView(clientFactory, lastSelectedObject, usagePointWorkspaceView);
        transactionItemView.showMeterTransactionDetails(clltblTransactions, l, t);
    }

    public void setMeterTransactionList(ArrayList<CustomerTransAlphaDataWithTotals> thedata) {
        theTransactiondata = thedata;
        dataProvider.setList(theTransactiondata);
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<>(dataProvider.getList());
            columnSortHandler.setComparator(transDateCol, new Comparator<CustomerTransAlphaDataWithTotals>() {
                public int compare(CustomerTransAlphaDataWithTotals o1, CustomerTransAlphaDataWithTotals o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getTransDate().compareTo(o2.getTransDate()) : 1;
                    }
                    return -1;
                }
            });
            clltblTransactions.addColumnSortHandler(columnSortHandler);

            columnSortHandler.setComparator(customerTransType, new Comparator<CustomerTransAlphaDataWithTotals>() {
                public int compare(CustomerTransAlphaDataWithTotals o1, CustomerTransAlphaDataWithTotals o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getTransTypeName().compareTo(o2.getTransTypeName()) : 1;
                    }
                    return -1;
                }
            });
            clltblTransactions.addColumnSortHandler(columnSortHandler);
            // We know that the data is sorted alphabetically by default.
            columnSortList = clltblTransactions.getColumnSortList();
            columnSortList.push(transDateCol);
            ColumnSortEvent.fire(clltblTransactions, columnSortList);
        } else {
            columnSortHandler.setList(dataProvider.getList());
            ColumnSortEvent.fire(clltblTransactions, columnSortList);
        }

    }

    public void setMeterData(MeterData meterData) {
        this.meterData = meterData;
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrTransactions.firstPage();
    }

    @Override
    public boolean isValid(CustomerTransAlphaDataWithTotals value, String filter) {
        String selectedFilter = filterDropdown.getSelectedValue();
        Messages messagesInstance = MessagesUtil.getInstance();
        if (selectedFilter.equals(messagesInstance.getMessage("meter.txn.date"))) {
            return dateFilter.isValid(value.getTransDate(), filter);
        } else if (selectedFilter.equals(messagesInstance.getMessage("meter.txn.ref"))) {
            return value.getVendRefReceived().toLowerCase().contains(filter.toLowerCase());
        } else if (selectedFilter.equals(messagesInstance.getMessage("meter.txn.receiptnum"))) {
            return value.getReceiptNum().toLowerCase().contains(filter.toLowerCase());
        } else {
            return value.getSerialNum().toLowerCase().contains(filter);
        }
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        txtbxfilter.setText("");
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage("meter.txn.date"));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }

    // Note: Similar code in UsagePointTransactionView.java - changes might need to be reflected there
    @UiHandler("btnExportCsv")
    void handleExportCsvButton(ClickEvent clickEvent) {
        if (!theTransactiondata.isEmpty()) {
            List<CustomerTransAlphaDataWithTotals> filteredList = dataProvider.getFilteredList();
            if (filteredList.size() > 3000) {
                Dialogs.displayErrorMessage(
                        MessagesUtil.getInstance().getMessage("export.error.too.many.records", new String[]{"3000"}),
                        MediaResourceUtil.getInstance().getErrorIcon());
            } else {
                proceedWithExport();
            }
        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("export.error.nodata"),
                    MediaResourceUtil.getInstance().getErrorIcon());
        }
    }

    private void proceedWithExport() {
        List<CustomerTransAlphaDataWithTotals> filteredList = dataProvider.getFilteredList();
        final List<Long> transIds = new ArrayList<>();
        for (CustomerTransAlphaDataWithTotals data : filteredList) {
            transIds.add(data.getId());
        }
        final Date now = new Date();
        final String userName = clientFactory.getUser().getUserName();
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                // Perform batch RPC update
                clientFactory.getMeterRpc().updateCustTransLastReprintDateBulk(transIds, now, userName, new ClientCallback<Void>() {
                    @Override
                    public void onSuccess(Void result) {
                        // Proceed with export after successful update
                        String filterValue = dataProvider.getFilter();
                        if (filterValue == null) filterValue = "";

                        String encodedUrl = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                                .withTargetUrl("customertransexport")
                                .addParam("exporttype", "meter")
                                .addParam("filenameprefix", meterData.getMeterNum())
                                .addParam("meter", meterData.getId().toString())
                                .addParam("filterelement", filterDropdown.getSelectedItemText())
                                .addParam("filtervalue", filterValue)
                                .toEncodedUrl();

                        if (encodedUrl != null) {
                            int top = Window.getClientHeight() / 2 - 50;
                            int left = Window.getClientWidth() / 2 - 50;
                            Window.open(encodedUrl, "_blank", "width=100, height=100, top=" + top + ", left=" + left);
                        }
                        usagePointWorkspaceView.getMeterInfo().populateMeterTransactions();
            }

                    @Override
                    public void onFailure(Throwable caught) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("export.error"),
                                MediaResourceUtil.getInstance().getErrorIcon());
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }
}
