package za.co.ipay.metermng.client.view.component.tariff.impl.blockthin;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.HasKeyboardSelectionPolicy.KeyboardSelectionPolicy;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.HasHorizontalAlignment;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell;
import za.co.ipay.gwt.common.client.widgets.DecimalInputCell.ViewData;
import za.co.ipay.gwt.common.client.widgets.PercentageTextBox;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.tariff.ContainsPayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.ITariffUIClass;
import za.co.ipay.metermng.client.view.component.tariff.PayTypeDiscountPanel;
import za.co.ipay.metermng.client.view.component.tariff.impl.BaseTariffView;
import za.co.ipay.metermng.datatypes.CycleE;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.tariff.BlockDto;
import za.co.ipay.metermng.shared.tariff.ITariffData;
import za.co.ipay.metermng.shared.tariff.ITariffInitData;
import za.co.ipay.metermng.shared.tariff.block.Block;
import za.co.ipay.metermng.shared.tariff.block.BlockThinCalcContents;
import za.co.ipay.metermng.shared.tariff.block.BlockThinCalcContentsTemplate;
import za.co.ipay.metermng.shared.tariff.block.BlockThinCalcInitData;
import za.co.ipay.metermng.shared.tariff.block.ThresholdChargeData;
import za.co.ipay.metermng.shared.tariff.units.UnitChargeData;

public class BlockThinTariffView extends BaseTariffView implements ITariffUIClass, ContainsPayTypeDiscountPanel {
    
    @UiField FormElement taxElement;
    @UiField FormElement percentChargeNameElement;
    @UiField FormElement percentChargeElement;
    @UiField FormElement blocksElement;
    @UiField FormElement cyclicChargeNameElement;
    @UiField FormElement cyclicChargeElement;
    @UiField FormElement cycleListBoxElement;

    @UiField ListBox cycleListBox;
    @UiField TextBox cyclicChargeNameBox;
    @UiField Label cyclicChargeCurrencyLabel;
    @UiField BigDecimalValueBox cyclicChargeBox;

    @UiField PercentageTextBox taxBox;

    @UiField TextBox percentChargeNameBox;
    @UiField PercentageTextBox percentChargeBox;

    @UiField FormRowPanel paytypeDiscountFormRowPanel;
    
    @UiField CellTable<BlockDto> blocksTable;
   
    private PayTypeDiscountPanel payTypeDiscountPanel;
    private ListDataProvider<BlockDto> blocksDataProvider;
    private DecimalInputCell unitPriceCell;
    private DecimalInputCell thresholdCell;
    private String unitSymbol;
    private ITariffInitData tariffInitData;
    private BlockThinCalcContents templateCalcContents;
        
    private static Logger logger = Logger.getLogger(BlockThinTariffView.class.getName());

    private static BlockThinTariffViewUiBinder uiBinder = GWT.create(BlockThinTariffViewUiBinder.class);

    interface BlockThinTariffViewUiBinder extends UiBinder<Widget, BlockThinTariffView> {
    }

    public BlockThinTariffView(ClientFactory clientFactory, String unitSymbol) {
        this.clientFactory = clientFactory;
        this.unitSymbol = unitSymbol;
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
        this.ensureDebugId("blockTariffView");
    }

    private void initUi() {
        createCycleList();
    	payTypeDiscountPanel = new PayTypeDiscountPanel(this);
    	payTypeDiscountPanel.getDiscountElement().setHelpMsg(MessagesUtil.getInstance().getMessage("tariff.field.discount.blockThin.help"));;
    	paytypeDiscountFormRowPanel.add(payTypeDiscountPanel); 
        createBlocksTable();
        clearForm();
    }    
    
    private void createBlocksTable() {    
        if (blocksDataProvider == null) {            
            blocksDataProvider = new ListDataProvider<BlockDto>();               

            //BlockDto count
            TextColumn<BlockDto> countColumn = new TextColumn<BlockDto>() {
                @Override
                public String getValue(BlockDto data) {
                    return data.getPosition()+"";
                }                
            };  

            //Unit price
            if (FormatUtil.getInstance().isRightToLeft()) {
                unitPriceCell = new DecimalInputCell("", FormatUtil.getInstance().getCurrencySymbol(), MessagesUtil.getInstance().getMessage("error.numeric.value"));
            } else {
                unitPriceCell = new DecimalInputCell(FormatUtil.getInstance().getCurrencySymbol(), "", MessagesUtil.getInstance().getMessage("error.numeric.value"));
            }
            final Column<BlockDto, String> unitPrice = new Column<BlockDto, String>(unitPriceCell) {
                @Override
                public String getValue(BlockDto data) {
                    if (data.getBlock().getUnitPrice() == null) {
                        return null;
                    }
                    return FormatUtil.getInstance().formatDecimal(data.getBlock().getUnitPrice());
                }                
            };            
            unitPrice.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
                @Override
                public void update(int index, BlockDto block, String value) {
                    if (value != null && (unitPriceCell.isNumeric(value) || value.isEmpty())) {
                        getForm().setDirtyData(true);                    
                        block.getBlock().setUnitPrice(FormatUtil.getInstance().parseDecimal(value));
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(unitPrice));
                    } else {
                        unitPriceCell.getViewData(block).setInvalid(true);     // Mark as invalid.
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(unitPrice));
                    }
                }
            });

            //Threshold
            thresholdCell = new DecimalInputCell("", unitSymbol, MessagesUtil.getInstance().getMessage("error.numeric.value"));
            final Column<BlockDto, String> threshold = new Column<BlockDto, String>(thresholdCell) {
                @Override
                public String getValue(BlockDto data) {
                    if (data.getBlock().getThreshold() == null) {
                        return null;
                    }
                    return FormatUtil.getInstance().formatDecimal(data.getBlock().getThreshold());
                }                
            };            
            threshold.setFieldUpdater(new FieldUpdater<BlockDto, String>() {
                @Override
                public void update(int index, BlockDto block, String value) {
                    if (value != null && (thresholdCell.isNumeric(value) || value.isEmpty())) {
                        getForm().setDirtyData(true);                    
                        block.getBlock().setThreshold(FormatUtil.getInstance().parseDecimal(value));
                        thresholdCell.getViewData(block).setInvalid(false);
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(threshold));
                    } else {
                        thresholdCell.getViewData(block).setInvalid(true);     // Mark as invalid.
                        blocksTable.redraw();
                        MeterMngClientUtils.focusOnNext(blocksTable, index, blocksTable.getColumnIndex(threshold));
                    }
                }
            });

            // Add the columns
            blocksTable.addColumn(countColumn, MessagesUtil.getInstance().getMessage("tariff.field.block.single"));
            blocksTable.addColumn(unitPrice, MessagesUtil.getInstance().getMessage("tariff.field.unitprice"));
            blocksTable.addColumn(threshold, MessagesUtil.getInstance().getMessage("tariff.field.threshold"));
            blocksTable.setKeyboardSelectionPolicy(KeyboardSelectionPolicy.DISABLED);            
            blocksDataProvider.addDataDisplay(blocksTable);

            blocksTable.getColumn(0).setHorizontalAlignment(HasHorizontalAlignment.ALIGN_CENTER);
        }
    }

    @Override
    public void setFormReadOnly(boolean readOnly) {
    }

    @Override
    public void setTariffInitData(ITariffInitData tariffInitData) {
        this.tariffInitData = tariffInitData;
        BlockThinCalcInitData blockThinCalcInitData = (BlockThinCalcInitData) tariffInitData;
        if (blockThinCalcInitData == null) {
            return;
        }
        BlockThinCalcContentsTemplate template = blockThinCalcInitData.getBlockThinCalcContentsTemplate();
        if (template == null) {
            template = new BlockThinCalcContentsTemplate();
        }
        templateCalcContents = template.getBlockThinCalcContents();
        if (templateCalcContents == null) {
            ArrayList<Block> blocks = new ArrayList<>();
            for (int i = 0; i < 8; i++) {
                blocks.add(new Block());
            }
            templateCalcContents = new BlockThinCalcContents(blocks);
            template.setBlockThinCalcContents(templateCalcContents);
        }

        List<Block> templateBlocks = templateCalcContents.getBlocks();
        List<Block> blocks = new ArrayList<>(templateBlocks.size());
        List<UnitChargeData> blockCharges = null;
        ThresholdChargeData thresholdCharge = null;
        for (Block block : templateBlocks) {
            if (block.getUnitCharges() != null && !block.getUnitCharges().isEmpty()) {
                blockCharges = new ArrayList<>(block.getUnitCharges().size());
                for (UnitChargeData charge : block.getUnitCharges()) {
                    blockCharges.add(new UnitChargeData(charge.getName(), charge.isPercentage(), charge.getCharge(),
                            charge.isTaxable(), charge.getCode()));
                }
            }
            if (block.getThresholdCharge() != null) {
                thresholdCharge = new ThresholdChargeData(block.getThresholdCharge().getName(),
                        block.getThresholdCharge().getCharge(), block.getThresholdCharge().isTaxable());
            }
            blocks.add(new Block(null, null, blockCharges, thresholdCharge, block.getCode(), block.getTaxCode(),
                    block.isTaxable()));
        }
        setBlocksData(blocks, true);
    }

    @Override
    public void setTariffData(ITariffData tariffData) {
        logger.info("Setting tariffData:" + tariffData);
        BlockThinCalcContents calcContents = (BlockThinCalcContents) tariffData;

        cyclicChargeNameBox.setText(calcContents.getCyclicChargeName());
        cyclicChargeBox.setValue(calcContents.getCyclicCharge());
        Long cycleId = null;
        if (calcContents.getCycle() != null) {
            cycleId = calcContents.getCycle().getId();
            for (int i = 1; i < cycleListBox.getItemCount(); i++) {
                cycleListBox.setSelectedIndex(i);
                if (Integer.parseInt(cycleListBox.getValue(cycleListBox.getSelectedIndex())) == cycleId) {
                    break;
                }
            }
        }

        percentChargeNameBox.setText(calcContents.getPercentChargeName());

        BigDecimal percentChargeMultiplier = calcContents.getPercentChargeMultiplier();
        if (percentChargeMultiplier != null) {
            percentChargeMultiplier = percentChargeMultiplier.movePointRight(2);
        }
        percentChargeBox.setAmount(percentChargeMultiplier);

        BigDecimal taxMultiplier = calcContents.getTaxMultiplier();
        if (taxMultiplier != null) {
            taxMultiplier = taxMultiplier.subtract(BigDecimal.ONE).multiply(BigDecimal.valueOf(100L));
        }
        taxBox.setAmount(taxMultiplier);

        // discounts
        payTypeDiscountPanel.setCalcContentsPayTypeDiscount(calcContents.getPayTypeDiscounts());

        // blocks
        setBlocksData(calcContents.getBlocks(), false);
    }

    public void setBlocksData(List<Block> blocks, boolean init) {
        blocksDataProvider.getList().clear();
        ArrayList<BlockDto> dtos = new ArrayList<BlockDto>(blocks.size());
        List<UnitChargeData> uiCharges = null;
        List<UnitChargeData> blockCharges = null;
        for (int i = 0; i < blocks.size(); i++) {
            Block block = blocks.get(i);
            // make a copy because celltable will modify underlying object which will modify
            // template and it must stay static so that if the form is cleared the template
            // or tariff can be referenced again unchanged.
            blockCharges = block.getUnitCharges();
            if (blockCharges != null && !blockCharges.isEmpty()) {
                uiCharges = new ArrayList<>(blockCharges.size());
                UnitChargeData uiCharge = null;
                for (UnitChargeData charge : blockCharges) {
                    uiCharge = new UnitChargeData(charge.getName(), charge.isPercentage(), charge.getCharge(),
                            charge.isTaxable(), charge.getCode());
                    uiCharges.add(uiCharge);
                }
            }

            block = new Block(block.getUnitPrice(), block.getThreshold(), uiCharges, null, block.getCode(),
                    block.getTaxCode(), block.isTaxable());
            dtos.add(new BlockDto(block, (i + 1)));
            if (i == 0 && init) {
                if (blockCharges != null && !blockCharges.isEmpty()) {
                    createBlockUnitChargeColumns(blockCharges, false);
                }
            }
        }
        blocksDataProvider.getList().addAll(dtos);
        initDefaultTableRows();
    }

    private void createBlockUnitChargeColumns(final List<UnitChargeData> blockUnitCharges,
            final boolean hasThresholdCharge) {
        createBlockUnitChargeColumns(blocksTable, blockUnitCharges, DEFAULT_BLOCKS_COLUMNS_COUNT, hasThresholdCharge);
    }

    private void initDefaultTableRows() {
        int count = blocksDataProvider.getList().size();
        int allowedBlocks = templateCalcContents != null ? templateCalcContents.getBlocks().size() : MeterMngStatics.ALLOWED_BLOCKS;
        if (count < allowedBlocks) {
            Block uiBlock = null;
            Block missingBlock = null;
            List<UnitChargeData> uiCharges = null;
            List<UnitChargeData> blockCharges = null;
            ThresholdChargeData uiThresholdCharge = null;
            ThresholdChargeData blockThresholdCharge = null;
            for (int i = count; i < allowedBlocks; i++) {
                if (templateCalcContents != null) {
                    missingBlock = templateCalcContents.getBlocks().get(i);
                    blockCharges = missingBlock.getUnitCharges();
                    if (blockCharges != null && !blockCharges.isEmpty()) {
                        uiCharges = new ArrayList<>(blockCharges.size());
                        UnitChargeData uiCharge = null;
                        for (UnitChargeData charge : blockCharges) {
                            uiCharge = new UnitChargeData(charge.getName(), charge.isPercentage(), charge.getCharge(),
                                    charge.isTaxable(), charge.getCode());
                            uiCharges.add(uiCharge);
                        }
                    }

                    blockThresholdCharge = missingBlock.getThresholdCharge();
                    if (blockThresholdCharge != null) {
                        uiThresholdCharge = new ThresholdChargeData(blockThresholdCharge.getName(),
                                blockThresholdCharge.getCharge(), blockThresholdCharge.isTaxable());
                    }

                    uiBlock = new Block(missingBlock.getUnitPrice(), missingBlock.getThreshold(), uiCharges,
                            uiThresholdCharge, missingBlock.getCode(), missingBlock.getTaxCode(),
                            missingBlock.isTaxable());
                } else {
                    uiBlock = new Block();
                }
                blocksDataProvider.getList().add(new BlockDto(uiBlock, (i + 1)));
            }
        }
    }

    @Override
    public boolean tariffDataRequired() {
        return true;
    }

    @Override
    public ITariffData getTariffData() {
        boolean valid = true;
        clearErrors();
        
        //Cyclic charge
        BigDecimal cyclicCharge = cyclicChargeBox.getValue();
        String cyclicChargeName = cyclicChargeNameBox.getText();
        if ((cyclicChargeName == null || cyclicChargeName.trim().equals("")) && cyclicCharge != null) {
            valid = false;
            cyclicChargeNameElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.monthlycost.name"));        
        } else if ((cyclicChargeName != null && !cyclicChargeName.trim().equals("")) && cyclicCharge == null) {
            valid = false;
            cyclicChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.monthlycost")); 
        } else if (cyclicCharge != null && cyclicCharge.doubleValue() < 0) {
            valid = false;
            cyclicChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.monthlycost.positive"));        
        }        
        
        CycleE cycle = CycleE.fromId(4);     // default to monthly
        try {
            if (cyclicCharge != null) {
                int index = cycleListBox.getSelectedIndex();
                if (index < 1) {
                    valid = false;
                    cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.error"));
                } else {
                    cycle = CycleE.fromId(Integer.parseInt(cycleListBox.getValue(cycleListBox.getSelectedIndex())));
                }
            }
        } catch (Exception e) {
            valid = false;
            cycleListBoxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("cycle.error"));
        } 

        //Percent charge and name
        BigDecimal percentCharge = percentChargeBox.getAmount();
        String percentChargeName = percentChargeNameBox.getText();
        if ((percentChargeName == null || percentChargeName.trim().equals("")) && percentCharge != null) {
            valid = false;
            percentChargeNameElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.percent_charge.name"));        
        } else if ((percentChargeName != null && !percentChargeName.trim().equals("")) && percentCharge == null) {
            valid = false;
            percentChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.percent_charge")); 
        } else if (percentCharge != null && percentCharge.doubleValue() < 0) {
            valid = false;
            percentChargeElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.error.percent_charge.positive_not_zero"));        
        }         

        //Tax
        BigDecimal tax = taxBox.getAmount();
        BigDecimal taxMultiplier = null;
        if (tax == null) {
            valid = false;
            taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.no.tax"));
        } else if (tax.doubleValue() < 0.0) {
            valid = false;
            taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("tou.thin.error.tax.positive"));
        } else {
            try {
                // taxMultiplier is 1 + (percent / 100) eg. 14% = 1.14
                // more efficient to do this calculation here than to store 14% and have the tariff calculator do this calculation on every vend
                taxMultiplier = BigDecimal.ONE.add(tax.divide(BigDecimal.valueOf(100L)));
            } catch (Exception e) {
                valid = false;
                taxElement.showErrorMsg(MessagesUtil.getInstance().getMessage("error.numeric.value"));
            }
        }       
        
        //Discounts
        if (!payTypeDiscountPanel.isValid()) {
            valid = false;
        }
        
        //Blocks
        boolean validBlockUnitCharges = true;
        boolean validBlocks = true;
        int count = 0;
        for(BlockDto b : blocksDataProvider.getList()) {
            if ((b.getBlock().getUnitPrice() == null && b.getBlock().getThreshold() != null)) {
                valid = false;
                validBlocks = false;
                blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.incomplete"));
                break;
            } else if (b.getBlock().getUnitPrice() != null) {
                ViewData viewData = null;
                if (b.getBlock().getUnitPrice().doubleValue() < 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error"));
                    break;
                } else if (count > 0 && b.getBlock().getUnitPrice().compareTo(BigDecimal.ZERO) == 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.zero.error"));
                    break;
                } else if (b.getBlock().getThreshold() != null && b.getBlock().getThreshold().doubleValue() < 0) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error"));
                    break;
                } else if ((viewData = ((DecimalInputCell) (blocksTable.getColumn(1).getCell())).getViewData(b)) != null
                        && viewData.isInvalid()
                        || (viewData = ((DecimalInputCell) (blocksTable.getColumn(2).getCell())).getViewData(b)) != null
                                && viewData.isInvalid()) {
                    valid = validBlocks = false;
                    break;
                } else {
                    count++;
                }
            }
        }

        // Validate Block UnitCharges
        List<UnitChargeData> charges = null;
        Map<String, List<UnitChargeData>> blockUnitChargeMap = new HashMap<>();
        for(BlockDto b : blocksDataProvider.getList()) {
            if (b.getBlock().getUnitCharges() != null && !b.getBlock().getUnitCharges().isEmpty()) {
                for (UnitChargeData unitChargeData : b.getBlock().getUnitCharges()) {
                    if (b.getBlock().getUnitPrice() != null) {
                        charges = blockUnitChargeMap.get(unitChargeData.getName());
                        if (charges == null) {
                            charges = new ArrayList<>();
                            blockUnitChargeMap.put(unitChargeData.getName(), charges);
                            charges.add(unitChargeData);
                        } else {
                            charges.add(unitChargeData);
                        }
                    } else if (b.getBlock().getUnitPrice() == null && unitChargeData.getCharge() != null) {
                        valid = false;
                        validBlocks = false;
                        validBlockUnitCharges = false;
                        blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.unitcharge.error.incomplete"));
                        break;
                    }
                }
                if (!validBlockUnitCharges) {
                    break;
                }
            }
        }
        
        // Validate all UnitCharges are set OR all are empty.
        if (validBlockUnitCharges) {
            for (Map.Entry<String, List<UnitChargeData>> dataList : blockUnitChargeMap.entrySet()) {
                UnitChargeData currentCharge = null;
                UnitChargeData periousCharge = null;
                List<UnitChargeData> mapCharges = dataList.getValue();
                for (int i = 0; i < mapCharges.size(); i++) {
                    currentCharge = mapCharges.get(i);
                    if (i > 0) {
                        periousCharge = mapCharges.get(i-1);
                        if ((currentCharge.getCharge() == null && periousCharge.getCharge() != null) ||
                                (currentCharge.getCharge() != null && periousCharge.getCharge() == null)) {
                            valid = false;
                            validBlocks = false;
                            validBlockUnitCharges = false;
                            blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.unitcharge.error.empty"));
                            break;
                        }
                    }
                }
                if (!validBlockUnitCharges) {
                    break;
                }
            }
        }
        
        //Repack the blocks
        ArrayList<Block> newBlocks = new ArrayList<Block>();
        for(BlockDto b : blocksDataProvider.getList()) {
            if (b.getBlock().getUnitPrice() != null) {
                newBlocks.add(b.getBlock());
            }
        }
        
        //Get the last block with no threshold and check all the ones before that have a threshold set
        int lastIndex = newBlocks.size() - 1;
        for(int i=newBlocks.size()-1; i>=0; i--) {
            Block b = newBlocks.get(i);
            if (b.getUnitPrice() != null && b.getThreshold() == null) {
                if (i != lastIndex) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.last"));
                    break;
                }    
            }
        }
        
        if (newBlocks.size() > 0) {
            Block lastBlock = newBlocks.get(newBlocks.size() - 1);
            if (lastBlock.getThreshold() != null) {
                valid = false;
                validBlocks = false;
                blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.last.none"));
            }
        }
        
        //now check that each succeeding threshold is > than one before
        BigDecimal previousThreshold = BigDecimal.ZERO;
        if (valid && validBlocks) {
            for(int i=0; i < newBlocks.size(); i++) {
                Block b = newBlocks.get(i);
                if (b.getThreshold() != null && b.getThreshold().compareTo(previousThreshold) < 1) {
                    valid = false;
                    validBlocks = false;
                    blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.increasing.thresholds"));
                    break;
                }
                previousThreshold = b.getThreshold();
            }    
        }
    
        //Valid blocks?
        if (count == 0 && validBlocks) {
            valid = false;
            blocksElement.setErrorMsg(MessagesUtil.getInstance().getMessage("tariff.blocks.error.none"));
        }
        
        //Valid input?
        if (!valid) {
            return null;
        } else {
            BigDecimal percentChargeMultiplier = null;
            if (percentCharge != null) {
                percentChargeMultiplier = percentCharge.movePointLeft(2);
            }
            BlockThinCalcContents cc = new BlockThinCalcContents(newBlocks, percentChargeName, percentChargeMultiplier, 
                                    taxMultiplier, payTypeDiscountPanel.getThisPayTypeDiscountsList(), cycle, cyclicChargeName, cyclicCharge);
            return cc;
        }    
    }
    
    @Override
    public void setCalcContents(String contents) {
    }

    @Override
    public String getCalcContents() {
        // gets past validator will be replaced on server
        return "placeholder";
    }

    public void createCycleList() {
        cycleListBox.addItem("", "");
        cycleListBox.addItem(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.daily"), "3");    //addItem(text, CycleE.id)
        cycleListBox.addItem(MessagesUtil.getInstance().getMessage("tariff.cost.cycle.monthly"), "4");
    }

    @Override
    public void clearErrors() {        
        taxElement.setErrorMsg(null);
        payTypeDiscountPanel.clearErrors();
        blocksElement.setErrorMsg(null);
        percentChargeElement.setErrorMsg(null);
        percentChargeNameElement.setErrorMsg(null);
        payTypeDiscountPanel.clearErrors();
        cycleListBoxElement.clearErrorMsg();
        cyclicChargeNameElement.clearErrorMsg();
        cyclicChargeElement.clearErrorMsg();
    }
    
    public void clearForm() {
        taxBox.setAmount(null);
        percentChargeBox.setAmount(null);
        percentChargeNameBox.setText("");
        payTypeDiscountPanel.initDiscountDefaultTableRows();
        blocksDataProvider.getList().clear();
        blocksDataProvider.flush();
        clearErrors();
        setTariffInitData(tariffInitData);
    }
    
    @Override
    protected void addFieldHandlers() {
        taxBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        percentChargeBox.getBigDecimalValueBox().addChangeHandler(new FormDataChangeHandler(form));
        percentChargeNameBox.addChangeHandler(new FormDataChangeHandler(form));
        cycleListBox.addChangeHandler(new FormDataChangeHandler(form));
        cyclicChargeNameBox.addChangeHandler(new FormDataChangeHandler(form));
        cyclicChargeBox.addChangeHandler(new FormDataChangeHandler(form));
    }

}
