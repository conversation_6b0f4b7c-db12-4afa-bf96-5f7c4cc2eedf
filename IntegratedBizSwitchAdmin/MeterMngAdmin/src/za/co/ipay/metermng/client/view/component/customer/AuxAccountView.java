package za.co.ipay.metermng.client.view.component.customer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.logging.Logger;

import com.google.gwt.cell.client.ClickableTextCell;
import com.google.gwt.cell.client.DateCell;
import com.google.gwt.cell.client.FieldUpdater;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ChangeHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.event.dom.client.KeyUpHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.place.shared.Place;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.form.Format;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.form.SimpleForm;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.metermng.client.event.AuxChargeScheduleUpdatedEvent;
import za.co.ipay.metermng.client.event.AuxChargeScheduleUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.SearchRpc;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.util.MeterMngClientUtils;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.client.widget.StatusTableColumn;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.mybatis.generated.model.AuxType;
import za.co.ipay.metermng.shared.AuxAccountData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.HistoryData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class AuxAccountView extends BaseComponent implements FormManager<AuxAccountData>, IpayDataProviderFilter<AuxAccountData> {
    private static final int DEFAULT_PAGE_SIZE = 15;

    @UiField(provided=true) CellTable<AuxAccountData> clltblTransactions;
    @UiField TablePager smplpgrTransactions;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField SimpleForm form;

    //auxiliary account transaction view with adjustment
    @UiField(provided=true) CustomerAccountTransactionView customerAccTransactions;

    private IPayDataProvider<AuxAccountData> dataProvider;
    private AuxAccountPanel auxAccountFormPanel;

    private ArrayList<AuxAccountData> auxAccountDataList;
    private AuxAccountData auxAccountData;
    private CustomerAgreementData customerAgreementData;

    private TextColumn<AuxAccountData> accountName;
    ListHandler<AuxAccountData> columnSortHandler;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private boolean viewConstructed = false;
    private DateTimeFormat dateTimeFormat;
    private SearchRpc searchRpc;

    private static Logger logger = Logger.getLogger(AuxAccountView.class.getName());

    private static AuxAccountViewUiBinder uiBinder = GWT.create(AuxAccountViewUiBinder.class);

    interface AuxAccountViewUiBinder extends UiBinder<Widget, AuxAccountView> {
    }

    public AuxAccountView(UsagePointWorkspaceView usagePointWorkspaceView, ClientFactory clientFactory) {
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        setClientFactory(clientFactory);
        createTable();
        customerAccTransactions = new CustomerAccountTransactionView(usagePointWorkspaceView, null, clientFactory, true);
        initWidget(uiBinder.createAndBindUi(this));
        setLatestAuxAccountId(usagePointWorkspaceView.getUsagePointData());
        initUi();
    }

    protected void createTable() {
         clltblTransactions = new CellTable<AuxAccountData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private void initUi() {
    	initView();
        initForm();
        initTable();
        dateTimeFormat = DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat());
        clientFactory.getEventBus().addHandler(AuxChargeScheduleUpdatedEvent.TYPE, new AuxChargeScheduleUpdatedEventHandler() {
            @Override
            public void processAuxChargeScheduleUpdatedEvent(AuxChargeScheduleUpdatedEvent event) {
                AuxAccountData thedata = null;
                if(event.getAuxChargeSchedule().isAccountSpecific() ) {
                    auxAccountFormPanel.lstbxChargeSchedule.setSelectedIndex(1);
                }
                for (int i=0; i<auxAccountDataList.size(); i++) {
                    thedata = auxAccountDataList.get(i);
                    if (thedata.getAuxChargeScheduleId().equals(event.getAuxChargeSchedule().getId())) {
                        thedata.setAuxChargeSchedule(event.getAuxChargeSchedule());
                        break;
                    }
                }
                if (thedata != null && auxAccountData != null && auxAccountData.getId().equals(thedata.getId())) {
                    auxAccountData = thedata;
                }
            }
        });
        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initView() {
        dataName.setText(MessagesUtil.getInstance().getMessage("customer.title.auxaccounts"));
        dataDescription.setText(MessagesUtil.getInstance().getMessage("customer.title.auxaccounts.description"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("customer.auxaccount.type.column"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("customer.auxaccount.name"));
        filterDropdown.addChangeHandler(new ChangeHandler() {

			@Override
			public void onChange(ChangeEvent event) {
				dataProvider.resetFilter();
	    		txtbxfilter.setText("");
			}
		});
        txtbxfilter.addKeyUpHandler(new KeyUpHandler() {

			@Override
			public void onKeyUp(KeyUpEvent event) {
				if (txtbxfilter.getText().trim().isEmpty()) {
	                dataProvider.resetFilter();
	            } else {
	                dataProvider.setFilter(txtbxfilter.getText());
	            }
	            dataProvider.setList(auxAccountDataList);
	            columnSortHandler.setList(dataProvider.getList());
	            smplpgrTransactions.firstPage();
			}
		});
    }

    private void initForm() {
        auxAccountFormPanel = new AuxAccountPanel(form, clientFactory);
        form.setHasDirtyDataManager(usagePointWorkspaceView);
        auxAccountFormPanel.specificChargeSchedule.form.setHasDirtyDataManager(form.getHasDirtyDataManager());
    	form.getFormFields().add(auxAccountFormPanel);
    	form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("customer.auxaccount.add"));
    	form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
    	form.getSaveBtn().ensureDebugId("auxAccSaveBtn");
    	form.getSaveBtn().addClickHandler(new ClickHandler() {
    		@Override
    		public void onClick(ClickEvent event) {
    			onSaveButtonClick();
    		}
    	});
    	form.getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
    	form.getOtherBtn().ensureDebugId("auxAccCancelBtn");
    	form.getOtherBtn().addClickHandler(new ClickHandler() {
    		@Override
    		public void onClick(ClickEvent event) {
    			onCancelButtonClick();
    		}
    	});

    	handleButtonsVisibility();
    }

    private void handleButtonsVisibility() {
        MeterMngUser user = clientFactory.getUser();
        boolean hasDebtRights = user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_DEBT);
        boolean hasRefundRights = user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_REFUND);
        boolean hasAdminRights = user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_ACCOUNT);
        boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(usagePointWorkspaceView.getUsagePoint(), clientFactory.isGroupGroupUser());

        if (clientFactory.isEnableAccessGroups() && (groupHasGlobal || clientFactory.isGroupGlobalUser())) {
            hasDebtRights = hasRefundRights = hasAdminRights = false;;
        }

        Button btnSave = form.getSaveBtn();
        Button btnCancel = form.getOtherBtn();

        setButtonsVisibility(false, btnSave, btnCancel);

        if (hasAdminRights) {
            auxAccountFormPanel.auxDebtBalance.setVisible(true);
            auxAccountFormPanel.auxRefundBalance.setVisible(true);
            setButtonsVisibility(true, btnSave, btnCancel);
        } else {
            if (auxAccountData == null) {
                auxAccountFormPanel.auxDebtBalance.setVisible(hasDebtRights);
                auxAccountFormPanel.auxRefundBalance.setVisible(hasRefundRights);
                if (hasDebtRights && !hasRefundRights) {
                    auxAccountFormPanel.auxDebtBalance.setValue(true);
                    setButtonsVisibility(true, btnSave, btnCancel);
                } else if (!hasDebtRights && hasRefundRights) {
                    auxAccountFormPanel.auxRefundBalance.setValue(true);
                    setButtonsVisibility(true, btnSave, btnCancel);
                }
            } else {
                auxAccountFormPanel.auxDebtBalance.setVisible(true);
                auxAccountFormPanel.auxRefundBalance.setVisible(true);
                boolean isRefund = auxAccountData.getBalance().compareTo(BigDecimal.ZERO) == 1;
                boolean visible = ((!isRefund && hasDebtRights) || (isRefund && hasRefundRights));
                setButtonsVisibility(visible, btnSave, btnCancel);
            }
        }
        auxAccountFormPanel.handleBalanceTypeChange();
    }

    private void setButtonsVisibility(boolean visible, Button btnSave, Button btnCancel) {
        btnSave.setVisible(visible);
        btnCancel.setVisible(visible);
    }

    private void initTable() {

        if (dataProvider == null) {
            dataProvider = new IPayDataProvider<AuxAccountData>(this);

            accountName = new TextColumn<AuxAccountData>() {
                @Override
                public String getValue(AuxAccountData data) {
                    if (data.getAccountName() != null) {
                        return data.getAccountName();
                    }
                    return " ? ";
                }
            };
            accountName.setSortable(true);

            DateCell dateCell = new DateCell(DateTimeFormat.getFormat(FormatUtil.getInstance().getDateTimeFormat()));
            Column<AuxAccountData, Date> startDateCol = new Column<AuxAccountData, Date>(dateCell) {
                @Override
                public Date getValue(AuxAccountData data) {
                    if (data.getStartDate() != null) {
                        Format format = FormatUtil.getInstance();
                        return format.parseDateTime(format.formatDateTime(data.getStartDate()));
                    }
                    return null;
                }
            };
            startDateCol.setSortable(true);


            Column<AuxAccountData, Date> suspendUntil = new Column<AuxAccountData, Date>(dateCell) {
                @Override
                public Date getValue(AuxAccountData data) {
                	if (data.getSuspendUntil() != null) {
                		Format format = FormatUtil.getInstance();
                		return format.parseDateTime(format.formatDateTime(data.getSuspendUntil()));
                	}
                	return null;
                }
            };
            suspendUntil.setSortable(true);

            TextColumn<AuxAccountData> balance = new TextColumn<AuxAccountData>() {
                @Override
                public String getValue(AuxAccountData data) {
                    if (data.getBalance() != null) {
                        return FormatUtil.getInstance().formatCurrency(data.getBalance(), true);
                    }
                    return " ? ";
                }
            };
            balance.setSortable(true);

            TextColumn<AuxAccountData> principleAmount = new TextColumn<AuxAccountData>() {
                @Override
                public String getValue(AuxAccountData data) {
                    if (data.getPrincipleAmount() != null) {
                        return FormatUtil.getInstance().formatCurrency(data.getPrincipleAmount(), true);
                    }
                    return " ? ";
                }
            };
            principleAmount.setSortable(true);

            TextColumn<AuxAccountData> priority = new TextColumn<AuxAccountData>() {
                @Override
                public String getValue(AuxAccountData data) {
                    if (data.getAccountPriority() != null) {
                        return data.getAccountPriority().toString();
                    }
                    return " ? ";
                }
            };
            priority.setSortable(true);

            final Column<AuxAccountData, String> chargeScheduleName = new Column<AuxAccountData, String>(new ClickableTextCell()) {
                @Override
                public String getValue(AuxAccountData data) {
                    if (data.getAuxChargeSchedule() != null && data.getAuxChargeSchedule().getScheduleName() != null) {
                        return data.getAuxChargeSchedule().getScheduleName();
                    }
                    if (data.getBalance().compareTo(BigDecimal.ZERO) == 1) {
                        return null;
                    } else {
                        return " ? ";
                    }
                }

            };

            chargeScheduleName.setFieldUpdater(new FieldUpdater<AuxAccountData, String>() {
                @Override
                public void update(int index, AuxAccountData data, String value) {
                    if (data.getAuxChargeSchedule() != null && data.getAuxChargeSchedule().getScheduleName() != null) {

                        if(data.getAuxChargeSchedule().isAccountSpecific()) {
                            displaySelected(data);
                        } else {
                            PopupPanel simplePopup = new PopupPanel(true);
                            simplePopup.setAnimationEnabled(true);
                            simplePopup.setStylePrimaryName("ipaypopup");
                            simplePopup.setWidget(new AuxChargeScheduleDetails(clientFactory, data.getAuxChargeSchedule(), true));
                            simplePopup.center();
                            simplePopup.show();
                        }

                    } else if (data.getBalance().compareTo(BigDecimal.ZERO) != 1) {
                        Messages messages = MessagesUtil.getInstance();
                        Dialogs.centreMessage(messages.getMessage("no.aux.charge.schedule.defined"),
                                new Image(MediaResourceUtil.getInstance().getInformationIcon()),
                                StyleNames.POPUP_MESSAGE, messages.getMessage("button.close"), null);
                    }
                }

            });
            chargeScheduleName.setSortable(true);
            chargeScheduleName.setCellStyleNames("gwt-Label-iPayLink");

            TextColumn<AuxAccountData> auxtype = new TextColumn<AuxAccountData>() {
                @Override
                public String getValue(AuxAccountData data) {
                    if (data.getAuxType() != null && data.getAuxType().getName() != null) {
                        return data.getAuxType().getName();
                    }
                    return " ? ";
                }
            };
            auxtype.setSortable(true);

            TextColumn<AuxAccountData> freeIssue = new TextColumn<AuxAccountData>() {
                @Override
                public String getValue(AuxAccountData data) {
                    if (data.isFreeIssueAccount()) {
                        return MessagesUtil.getInstance().getMessage("customer.auxaccount.freeissue");
                    }
                    return " - ";
                }
            };

            TextColumn<AuxAccountData> statusColumn = new StatusTableColumn<AuxAccountData>();

            // Add the columns.
            clltblTransactions.addColumn(auxtype, MessagesUtil.getInstance().getMessage("customer.auxaccount.type.column"));
            clltblTransactions.addColumn(accountName, MessagesUtil.getInstance().getMessage("customer.auxaccount.name"));
            clltblTransactions.addColumn(startDateCol, MessagesUtil.getInstance().getMessage("customer.auxaccount.start.date.lbl"));
            clltblTransactions.addColumn(suspendUntil, MessagesUtil.getInstance().getMessage("customer.auxaccount.suspend.until"));
            clltblTransactions.addColumn(principleAmount, MessagesUtil.getInstance().getMessage("customer.auxaccount.principle.amt"));
            clltblTransactions.addColumn(balance, MessagesUtil.getInstance().getMessage("customer.auxaccount.balance"));
            clltblTransactions.addColumn(priority, MessagesUtil.getInstance().getMessage("customer.auxaccount.priority"));
            clltblTransactions.addColumn(chargeScheduleName, MessagesUtil.getInstance().getMessage("customer.auxaccount.chargeschedule"));
            if (clientFactory.isEnableSTS()) {
                clltblTransactions.addColumn(freeIssue, MessagesUtil.getInstance().getMessage("customer.auxaccount.freeissue"));
            }
            clltblTransactions.addColumn(statusColumn, MessagesUtil.getInstance().getMessage("auxtype.field.status"));

            columnSortHandler = new ListHandler<AuxAccountData>(dataProvider.getList());
            columnSortHandler.setComparator(auxtype, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getAuxType() != null) {
                        return (o2 != null && o2.getAuxType() != null) ? o1.getAuxType().getName().compareTo(o2.getAuxType().getName()) : 1;
                    }
                    return -1;
                }
            });

            columnSortHandler.setComparator(accountName, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getAccountName().compareTo(o2.getAccountName()) : 1;
                    }
                    return -1;
                }
            });

            columnSortHandler.setComparator(suspendUntil, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    Date suspendUntil1 = o1.getSuspendUntil();
                    Date suspendUntil2 = o2.getSuspendUntil();
                    if (suspendUntil1 != null) {
                        if (suspendUntil2 == null) {
                            return 1;
                        }
                        return suspendUntil1.compareTo(suspendUntil2);
                    } else if (suspendUntil2 == null) {
                        return 0;
                    } else {
                        return -1;
                    }
                }
            });

            columnSortHandler.setComparator(startDateCol, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    Date dt1 = o1.getStartDate();
                    Date dt2 = o2.getStartDate();
                    if (dt1 != null) {
                        if (dt2 == null) {
                            return 1;
                        }
                        return dt1.compareTo(dt2);
                    } else if (dt2 == null) {
                        return 0;
                    } else {
                        return -1;
                    }
                }
            });

            columnSortHandler.setComparator(balance, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getBalance().compareTo(o2.getBalance()) : 1;
                    }
                    return -1;
                }
            });

            columnSortHandler.setComparator(priority, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getAccountPriority().compareTo(o2.getAccountPriority()) : 1;
                    }
                    return -1;
                }
            });

            columnSortHandler.setComparator(chargeScheduleName, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null && o1.getAuxChargeSchedule() != null) {
                        return (o2 != null && o2.getAuxChargeSchedule() != null) ? o1.getAuxChargeSchedule().getScheduleName().compareTo(o2.getAuxChargeSchedule().getScheduleName()) : 1;
                    }
                    return -1;
                }
            });

            columnSortHandler.setComparator(statusColumn, new Comparator<AuxAccountData>() {
                public int compare(AuxAccountData o1, AuxAccountData o2) {
                    if (o1 == o2) {
                        return 0;
                    }
                    if (o1 != null) {
                        return (o2 != null) ? o1.getRecordStatus().compareTo(o2.getRecordStatus()) : 1;
                    }
                    return -1;
                }
            });


            clltblTransactions.addColumnSortHandler(columnSortHandler);
            clltblTransactions.getColumnSortList().push(priority);
            ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());

            dataProvider.addDataDisplay(clltblTransactions);
            smplpgrTransactions.setDisplay(clltblTransactions);


            final SingleSelectionModel<AuxAccountData> selectionModel = new SingleSelectionModel<AuxAccountData>();
            CellPreviewEvent.Handler<AuxAccountData> handler = new CellPreviewEvent.Handler<AuxAccountData>() {
                final CellPreviewEvent.Handler<AuxAccountData> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
                @Override
                public void onCellPreview(final CellPreviewEvent<AuxAccountData> event) {
                        if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                            if (!event.isCanceled()) {
                                if (event.getColumn() == clltblTransactions.getColumnIndex(chargeScheduleName)) {
                                    return;
                                }
                                if (form.isDirtyData()) {
                                    Dialogs.confirmDiscardChanges(
                                            new ConfirmHandler() {
                                                @Override
                                                public void confirmed(boolean confirm) {
                                                    if (confirm) {
                                                        form.setDirtyData(false);
                                                        event.getDisplay().getSelectionModel().setSelected(event.getValue(), true);
                                                        logger.fine("Discarding changes for selection event");
                                                    } else {
                                                        event.setCanceled(true);
                                                        logger.fine("Cancelled selection event, staying with current selection");
                                                    }

                                                }});
                                } else {
                                    selectionEventManager.onCellPreview(event);
                                }

                            }
                    } else {
                        selectionEventManager.onCellPreview(event);
                    }
                }};
                clltblTransactions.setSelectionModel(selectionModel, handler);
                selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
                    public void onSelectionChange(SelectionChangeEvent event) {

                        final AuxAccountData selected = selectionModel.getSelectedObject();
                        if (selected != null) {
                            displaySelected(selected);
                        }
                    }
                });

                logger.info("Created AuxAccountView table");
        }
    }

    private void setAuxAccountData(AuxAccountData auxAccountData) {
    	auxAccountFormPanel.clearErrors();
        auxAccountFormPanel.clearFields();
        this.auxAccountData = auxAccountData;
        boolean update = false;

        if (auxAccountData != null) {
            form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
            form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("customer.auxaccount.update"));
            update = true;
            auxAccountFormPanel.setSpecialActionValue(SpecialActionsData.UPDATE_AUX_ACCOUNT);
            BigDecimal balance = auxAccountData.getBalance();
            if (balance != null && balance.compareTo(BigDecimal.ZERO) != 1) {
                // Display all values as positive (even dept) and correlate to radio button
                auxAccountFormPanel.txtbxBalance.setValue(balance.negate());
                auxAccountFormPanel.auxDebtBalance.setValue(true);
            } else {
                auxAccountFormPanel.txtbxBalance.setValue(balance);
                auxAccountFormPanel.auxRefundBalance.setValue(true);
            }
            auxAccountFormPanel.mridComponent.setMrid(auxAccountData.getMrid());
            auxAccountFormPanel.mridComponent.setIsExternal(auxAccountData.isMridExternal());
        } else {
            auxAccountData = new AuxAccountData();
            auxAccountData.setRecordStatus(RecordStatus.ACT);
            form.getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
            form.getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("customer.auxaccount.add"));
            customerAccTransactions.clearTransactionTable();
            customerAccTransactions.setVisible(false);
            auxAccountFormPanel.txtbxBalance.setEnabled(true);
            auxAccountFormPanel.auxDebtBalance.setEnabled(true);
            auxAccountFormPanel.auxRefundBalance.setEnabled(true);
            auxAccountFormPanel.mridComponent.setMrid(null);
            auxAccountFormPanel.mridComponent.initMrid(clientFactory);
        }

        auxAccountFormPanel.txtbxName.setText(auxAccountData.getAccountName());
        if (auxAccountData.getAuxTypeId() != null) {
            auxAccountFormPanel.lstbxAuxType.selectItemByValue(auxAccountData.getAuxTypeId().toString());
        }
        if (auxAccountData.getAccountPriority() != null) {
        	auxAccountFormPanel.txtbxPriority.setText(auxAccountData.getAccountPriority().toString());
        }
        if (auxAccountData.getAuxChargeScheduleId() != null) {
            auxAccountFormPanel.lstbxChargeSchedule.selectItemByValue(auxAccountData.getAuxChargeScheduleId().toString());
            if (auxAccountData.getAuxChargeSchedule().isAccountSpecific()) {
                auxAccountFormPanel.lstbxChargeSchedule.setSelectedIndex(1);
                auxAccountFormPanel.showSpecificAuxChargeSched(auxAccountData.getAuxChargeSchedule());
            }
        }
        if (auxAccountData.getStartDate() != null) {
            auxAccountFormPanel.startDateBox.setValue(auxAccountData.getStartDate());
        } else {
            auxAccountFormPanel.startDateBox.setValue(null);
        }
        if (auxAccountData.getLastAuxPaymentDate() != null) {
            auxAccountFormPanel.lastChargeDateInfo.setText(dateTimeFormat.format(auxAccountData.getLastAuxPaymentDate()));
            auxAccountFormPanel.startDateBox.setEnabled(false);
        } else {
            auxAccountFormPanel.lastChargeDateInfo.setText("");
            auxAccountFormPanel.startDateBox.setEnabled(true);
        }
        if (auxAccountData.getSuspendUntil() != null) {
        	auxAccountFormPanel.suspendUntil.setValue(auxAccountData.getSuspendUntil());
        }
        auxAccountFormPanel.debtStatusInfo.setText(auxAccountData.getDebtStatus());

        auxAccountFormPanel.chckbxActive.setValue(RecordStatus.ACT.equals(auxAccountData.getRecordStatus()));

        if (update) {
            auxAccountFormPanel.txtbxBalance.setEnabled(false);
            auxAccountFormPanel.auxDebtBalance.setEnabled(false);
            auxAccountFormPanel.auxRefundBalance.setEnabled(false);
            customerAccTransactions.displayAuxAccountData(customerAgreementData, auxAccountData);
            customerAccTransactions.setVisible(true);
        }
        handleButtonsVisibility();
    }

    public void populateAuxAccountList() {
        clientFactory.getCustomerRpc().fetchAuxAccounts(customerAgreementData, new ClientCallback<ArrayList<AuxAccountData>>() {
            @Override
            public void onSuccess(ArrayList<AuxAccountData> result) {
                if (result != null) {
                    auxAccountDataList = result;
                    logger.info("Displaying AuxAccountData: "+result.size());
                    dataProvider.getList().clear();
                    dataProvider.getList().addAll(result);
                    dataProvider.refresh();
                    logger.info("height:    " + clltblTransactions.getOffsetHeight());

                    columnSortHandler.setList(dataProvider.getList());
                    ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
                }
            }
        });
    }

    public void setCustomerAgreement(CustomerAgreementData customerAgreementData) {
        this.customerAgreementData = customerAgreementData;
    }

    private void onSaveButtonClick() {
        auxAccountFormPanel.specificChargeSchedule.onSaveClick();
        AuxAccountData auxAccData = mapFormToData();
        if (isValid(auxAccData)) {
            this.auxAccountData = auxAccData;
            boolean isCheckBoxActive = auxAccountFormPanel.chckbxActive.getValue();
            boolean isCheckBoxEnabled = auxAccountFormPanel.chckbxActive.isEnabled();
            if (!isCheckBoxActive && isCheckBoxEnabled) {
                Messages msgInstance = MessagesUtil.getInstance();
                Dialogs.confirm(msgInstance.getMessage("auxaccount.checkbox.active.status"),
                        msgInstance.getMessage("option.positive"),
                        msgInstance.getMessage("option.negative"),
                        MediaResourceUtil.getInstance().getQuestionIcon(),
                        new ConfirmHandler() {
                            @Override
                            public void confirmed(boolean confirm) {
                                if (confirm) {
                                    auxAccountFormPanel.chckbxActive.setValue(true);
                                    auxAccountData.setRecordStatus(RecordStatus.ACT);
                                }
                                onSave();
                            }
                        },
                        null,
                        null);
            } else {
                onSave();
            }
        }
    }

    private void onSave() {
        final Button btnSave = form.getSaveBtn();
        btnSave.setEnabled(false);
        // If Dept save balance as negative
        if (auxAccountFormPanel.auxDebtBalance.getValue() == true) {
            auxAccountData.setBalance(auxAccountData.getBalance().negate());
        }


        final UsagePointWorkspaceView upv = usagePointWorkspaceView;
        final UsagePointData upd = upv.getUsagePointData();
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getSearchRpc().checkUsagePointDataIntegrity(upd, new ClientCallback<Boolean>() {
                    @Override
                    public void onSuccess(Boolean result) {
                        if (result) {
                            if (btnSave.getText().equals(MessagesUtil.getInstance().getMessage("button.create"))) {
                                addAuxAccount();
                            } else {
                                updateAuxAccount();
                            }
                        } else {
                            Place place = upv.getPlace();
                            upv.processInvalidState(place);
                        }
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private AuxAccountData mapFormToData() {
        AuxAccountData auxAccData = null;
        if (this.auxAccountData != null) {
            auxAccData = new AuxAccountData(this.auxAccountData);
            auxAccData.setFreeIssueAccount(auxAccountData.isFreeIssueAccount());
        }
        if (auxAccData == null) {
            auxAccData = new AuxAccountData();
            auxAccData.setCustomerAgreementId(customerAgreementData.getId());
        }

        auxAccData.setAccountName(
                auxAccountFormPanel.txtbxName.getText().isEmpty() ? null : auxAccountFormPanel.txtbxName.getText());
        BigDecimal balance = auxAccountFormPanel.txtbxBalance.getAmount();
        if (balance == null) {
            balance = BigDecimal.ZERO;
        }
        auxAccData.setBalance(balance);
        auxAccData.setAccountPriority(auxAccountFormPanel.txtbxPriority.getText().isEmpty() ? null : Integer.valueOf(auxAccountFormPanel.txtbxPriority.getText()));

        if (auxAccountFormPanel.lstbxAuxType.getSelectedIndex() != -1
                && !auxAccountFormPanel.lstbxAuxType.getSelectedValues().get(0).equals("")) {
            auxAccData.setAuxTypeId(Long.valueOf(auxAccountFormPanel.lstbxAuxType.getSelectedValues().get(0)));
        }

        if (auxAccountFormPanel.lstbxChargeSchedule.getSelectedIndex() != -1
                && !auxAccountFormPanel.lstbxChargeSchedule.getSelectedValues().get(0).equals("")) {
            if(auxAccountFormPanel.lstbxChargeSchedule.getSelectedValues().get(0).equals(AuxAccountPanel.ACCOUNT_SPECIFIC_ACS)) {
                auxAccData.setAuxChargeSchedule(auxAccountFormPanel.specificChargeSchedule.getAuxChargeSchedule());
                if (auxAccountFormPanel.specificChargeSchedule.getAuxChargeSchedule().getId() == null) {
                    auxAccData.setAuxChargeScheduleId(null); // if it is a new aux specific acs the set aux acc acs as null and backend will insert it
                }else {
                    auxAccData.setAuxChargeScheduleId(auxAccountFormPanel.specificChargeSchedule.getAuxChargeSchedule().getId());
                }
            } else {
                auxAccData.setAuxChargeScheduleId(
                        Long.valueOf(auxAccountFormPanel.lstbxChargeSchedule.getSelectedValues().get(0)));
            }
        }

        auxAccData.setStartDate(auxAccountFormPanel.startDateBox.getValue());
        auxAccData.setSuspendUntil(auxAccountFormPanel.suspendUntil.getValue());

        auxAccData.setRecordStatus(auxAccountFormPanel.chckbxActive.getValue() ? RecordStatus.ACT : RecordStatus.DAC);
        auxAccData.setSpecialActionReasonsLog(auxAccountFormPanel.specialactionreasons.getLogEntry());

        auxAccData.setMrid(auxAccountFormPanel.mridComponent.getMrid());
        auxAccData.setMridExternal(auxAccountFormPanel.mridComponent.isExternal());
        return auxAccData;
    }

    private boolean isValid(AuxAccountData auxAccData) {
        boolean valid = true;

        auxAccountFormPanel.clearErrors();

        if (!ClientValidatorUtil.getInstance().validateField(auxAccData, "accountName", auxAccountFormPanel.nameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(auxAccData, "balance", auxAccountFormPanel.balanceElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(auxAccData, "accountPriority", auxAccountFormPanel.priorityElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(auxAccData, "suspendUntil", auxAccountFormPanel.suspendUntilElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(auxAccData, "startDate", auxAccountFormPanel.startDateElement)) {
            valid = false;
        }

        if (auxAccData.getAccountPriority() == null
                || (auxAccData.getAccountPriority() != null && auxAccData.getAccountPriority() <= 0)) {
            auxAccountFormPanel.priorityElement.showErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.error.unique.priority"));
            valid = false;
        }

        if (!valid || ! auxAccountFormPanel.specialactionreasons.validate()) {
        	valid = false;
        }

        BigDecimal balance = auxAccData.getBalance();
        int balanceComparison = balance.compareTo(BigDecimal.ZERO);
        if (auxAccountFormPanel.auxDebtBalance.getValue() && balanceComparison == -1) {
            auxAccountFormPanel.balanceElement
                    .showErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.error.balance"));
            valid = false;
        } else if (auxAccountFormPanel.auxRefundBalance.getValue() && balanceComparison != 1) {
            auxAccountFormPanel.balanceElement
                    .showErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.error.balance.refund"));
            valid = false;
        }

        if (!auxAccountFormPanel.isBalanceTypeValid()) {
        	auxAccountFormPanel.auxBalanceTypePanel.showErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.balance.type.error.required"));
        	valid = false;
        }

        Date startDate = auxAccountFormPanel.startDateBox.getValue();
        if (startDate != null) {
            if (!MeterMngClientUtils.isValidDate(auxAccountFormPanel.startDateBox.getTextBox().getText())) {
                valid = false;
                auxAccountFormPanel.startDateElement.setErrorMsg(MessagesUtil.getInstance().getMessage(
                        "error.field.datetime.invalid", new String[] { FormatUtil.getInstance().getDateTimeFormat() }));
            }
        }
        if (auxAccountFormPanel.startDateBox.getTextBox().getValue().isEmpty()) {
            valid = false;
            auxAccountFormPanel.startDateElement.showErrorMsg(MessagesUtil.getInstance().getMessage(
                    "error.date.field.invalid", new String[] { FormatUtil.getInstance().getDateTimeFormat() }));
        }

        Date suspendDate = auxAccountFormPanel.suspendUntil.getValue();
        if (auxAccountData != null && auxAccountData.getId() != null && auxAccountData.getSuspendUntil() != null
                && auxAccountData.getSuspendUntil().equals(suspendDate)) {
            //don't need to validate; haven't changed it; otherwise will invalidate those already past!
        } else {
            if (suspendDate != null) {
                //suspendDate should not be before startDate
                if (suspendDate.before(startDate)) {
                    valid = false;
                    auxAccountFormPanel.suspendUntilElement.setErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.suspend.until.smlr.start"));
                }
                // suspendDate may not be in the past.
                Date currentDate = new Date();
                if (suspendDate.before(currentDate)) {
                    valid = false;
                    auxAccountFormPanel.suspendUntilElement.setErrorMsg(MessagesUtil.getInstance().getMessage("customer.auxaccount.suspend.until.error"));
                }
                if (!MeterMngClientUtils.isValidDate(auxAccountFormPanel.suspendUntil.getTextBox().getText())) {
                    valid = false;
                    auxAccountFormPanel.suspendUntilElement.setErrorMsg(MessagesUtil.getInstance().getMessage(
                            "error.field.datetime.invalid", new String[] { FormatUtil.getInstance().getDateTimeFormat() }));
                }
            } else if (!auxAccountFormPanel.suspendUntil.getTextBox().getValue().isEmpty()) {
                valid = false;
                auxAccountFormPanel.suspendUntilElement.showErrorMsg(MessagesUtil.getInstance().getMessage(
                        "error.date.field.invalid", new String[] { FormatUtil.getInstance().getDateTimeFormat() }));
            }
        }

        if(!auxAccountFormPanel.mridComponent.validate()) {
            valid = false;
        }

        if(auxAccountFormPanel.lstbxChargeSchedule.getSelectedValues().get(0).equals(AuxAccountPanel.ACCOUNT_SPECIFIC_ACS)) {
            if (!auxAccountFormPanel.specificChargeSchedule.validateForm()) {
                valid = false;
            }
        }

        return valid;
    }
    private void setLatestAuxAccountId(final UsagePointData usagePointData){
        if(usagePointData != null) {
            clientFactory.getSearchRpc().setLatestAuxAccountId(usagePointData, new AsyncCallback<HistoryData>() {
                @Override
                public void onSuccess(HistoryData historyData) {
                    usagePointData.setHistoryData(historyData);
                }

                @Override
                public void onFailure(Throwable caught) {
                }
            });
        } else{
            logger.severe("UsagePointData is null");
        }

    }

    public void addAuxAccount() {
        clientFactory.getAuxAccountsRpc().updateAuxAccount(auxAccountData, new ClientCallback<AuxAccountData>(
                form.getSaveBtn().getAbsoluteLeft(), form.getSaveBtn().getAbsoluteTop()) {
            @Override
            public void onSuccess(AuxAccountData result) {
                form.getSaveBtn().setEnabled(true);
                if (result != null) {
                    Dialogs.displayInformationMessage(
                            MessagesUtil.getInstance()
                                    .getSavedMessage(new String[] {
                                            MessagesUtil.getInstance().getMessage("customer.auxaccount.title") }),
                            MediaResourceUtil.getInstance().getInformationIcon(), form.getSaveBtn().getAbsoluteLeft(),
                            form.getSaveBtn().getAbsoluteTop(), MessagesUtil.getInstance().getMessage("button.close"));

                    auxAccountDataList.add(result);
                    dataProvider.setList(auxAccountDataList);
                    dataProvider.refresh();
                    columnSortHandler.setList(dataProvider.getList());
                    ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
                    clearUi();

                    final UsagePointData upd = usagePointWorkspaceView.getUsagePointData();
                    setLatestAuxAccountId(upd);

                    clientFactory.getWorkspaceContainer().notifyWorkspaces(
                            new WorkspaceNotification(WorkspaceNotification.NotificationType.DATA_UPDATED,
                                    MeterMngStatics.AUX_ACCOUNT_HISTORY_UPDATE));
                }
            }

            public void onFailure(Throwable caught) {
                form.getSaveBtn().setEnabled(true);
                super.onFailure(caught);
            }
        });
    }

    public void updateAuxAccount() {
        clientFactory.getAuxAccountsRpc().updateAuxAccount(auxAccountData, new ClientCallback<AuxAccountData>(
                form.getSaveBtn().getAbsoluteLeft(), form.getSaveBtn().getAbsoluteTop()) {
        	boolean isFreeIssue = auxAccountData.isFreeIssueAccount();
            @Override
            public void onSuccess(AuxAccountData result) {
                form.getSaveBtn().setEnabled(true);
                if (result != null) {
                    int index = -1;
                    for (int i = 0; i < auxAccountDataList.size(); i++) {
                        if (auxAccountDataList.get(i).getId().equals(result.getId())) {
                            index = i;
                            break;
                        }
                    }
                    if (index > -1) {
                        clearUi();
                        Dialogs.displayInformationMessage(
                                MessagesUtil.getInstance()
                                        .getSavedMessage(new String[] {
                                                MessagesUtil.getInstance().getMessage("customer.auxaccount.title") }),
                                MediaResourceUtil.getInstance().getInformationIcon(),
                                form.getSaveBtn().getAbsoluteLeft(), form.getSaveBtn().getAbsoluteTop(),
                                MessagesUtil.getInstance().getMessage("button.close"));
                        auxAccountDataList.set(index, result);
                        dataProvider.setList(auxAccountDataList);
                        dataProvider.refresh();
                        columnSortHandler.setList(dataProvider.getList());
                        ColumnSortEvent.fire(clltblTransactions, clltblTransactions.getColumnSortList());
                    }

                    if (isFreeIssue && !result.isFreeIssueAccount()) {
                    	customerAgreementData.setFreeIssueAuxAccId(null);
                    	customerAgreementData.setFreeIssueAuxAccountName(null);
                    }
                    clientFactory.getWorkspaceContainer().notifyWorkspaces(
                            new WorkspaceNotification(WorkspaceNotification.NotificationType.DATA_UPDATED,
                                    MeterMngStatics.AUX_ACCOUNT_HISTORY_UPDATE));
                }
                final UsagePointData upd = usagePointWorkspaceView.getUsagePointData();
                setLatestAuxAccountId(upd);

            }

            public void onFailure(Throwable caught) {
                form.getSaveBtn().setEnabled(true);
                super.onFailure(caught);
            }
        });
    }

    void onCancelButtonClick() {
        if (auxAccountFormPanel.specificChargeSchedule.form.isDirtyData()) {
            form.setDirtyData(true);
        }
        form.checkDirtyData(new ConfirmHandler() {

            @Override
            public void confirmed(boolean confirm) {
                if(confirm) {
                    clearUi();
                }
            }
        });
    }

    @SuppressWarnings("unchecked")
    private void clearUi() {
        setAuxAccountData(null);

        //clear Table selection
        SingleSelectionModel<AuxAccountData> selectionModel = ((SingleSelectionModel<AuxAccountData>) clltblTransactions.getSelectionModel());
        AuxAccountData selected = selectionModel.getSelectedObject();
        if (selected != null) {
            selectionModel.setSelected(selected, false);
        }

        auxAccountFormPanel.specificChargeSchedule.clear();
    }

    @Override
    public void displaySelected(AuxAccountData selected) {
        setAuxAccountData(selected);
    }

	@Override
    public boolean isValid(AuxAccountData value, String filter) {
        if (filterDropdown.getValue(filterDropdown.getSelectedIndex())
                .equals(MessagesUtil.getInstance().getMessage("customer.auxaccount.type.column"))) {
            AuxType auxType = value.getAuxType();
            return auxType != null && auxType.getName().toLowerCase().contains(filter.toLowerCase());
        } else {
            return value.getAccountName().toLowerCase().contains(filter.toLowerCase());
        }
    }

    public void refreshTransactionTable() {
        clearUi();
        populateAuxAccountList();
    }

    public void createRadioButtonsGroup(String place) {
        auxAccountFormPanel.auxDebtBalance.setName(place);
        auxAccountFormPanel.auxRefundBalance.setName(place);
    }

    public AuxAccountData getAuxAccountData() {
        return auxAccountData;
    }

}
