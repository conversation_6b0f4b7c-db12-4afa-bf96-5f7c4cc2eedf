package za.co.ipay.metermng.client.view.component.meter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.cell.client.ButtonCell;
import com.google.gwt.cell.client.Cell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.SimplePanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactory;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.component.SpecialActionsReasonComponent;
import za.co.ipay.metermng.client.view.component.usagepoint.UsagePointInformation;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.MeterTypeE;
import za.co.ipay.metermng.datatypes.TransItemTypeE;
import za.co.ipay.metermng.mybatis.custom.model.CustomerTransAlphaDataWithTotals;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTrans;
import za.co.ipay.metermng.mybatis.generated.model.CustomerTransExtra;
import za.co.ipay.metermng.shared.CustomerTransItemData;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SpecialActionsData;
import za.co.ipay.metermng.shared.StsEngineeringTokenData;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.StsMeterData;
import za.co.ipay.metermng.shared.dto.UsagePointData;
import za.co.ipay.metermng.shared.dto.meter.VerifyTokenDto;

public class TransactionItemsWidget extends BaseComponent implements IpayDataProviderFilter<CustomerTransItemData> {

    private static TransactionItemsWidgetUiBinder uiBinder = GWT.create(TransactionItemsWidgetUiBinder.class);
    @UiField CellTable<CustomerTransItemData> clltbltransitems;
    @UiField CellTable<StsEngineeringTokenData> clltbltranstokens;
    @UiField Button sendReprintBtn;
    @UiField Button vendReversalBtn;
    @UiField Label reversalCommentLabel;
    @UiField VerticalPanel panelReversed;
    @UiField SimplePanel sendReprintPanel;
    @UiField VerticalPanel stsTokensPanel;
    @UiField VerticalPanel stsTokenVerifiedPanel;
    @UiField Image imgValidating;
    @UiField HTML verifiedHeader;
    @UiField Label lblTokenClassValue;
    @UiField Label lblTokenSubclassValue;
    @UiField Label lblTokenIdValue;
    @UiField Label lblUnitsValue;
    @UiField Label lblTokenDateValue;

    private PopupPanel simplePopup;
    private IPayDataProvider<CustomerTransItemData> dataProvider = new IPayDataProvider<CustomerTransItemData>(this);
    private ArrayList<CustomerTransItemData> theTransactionitemdata;
    private IPayDataProvider<StsEngineeringTokenData> tokenDataProvider = new IPayDataProvider<StsEngineeringTokenData>(new StsEngineeringTokenProviderFilter());
    private ArrayList<StsEngineeringTokenData> theTokenData;
    private TextColumn<CustomerTransItemData> tokenCol;
    private CustomerTrans customerTrans;
    private final DateTimeFormat dateTimeFormat = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss");
    private Map<Long, VerifyTokenDto> verifiedTokenIds = null;


    private static final boolean IS_CENTER = true;
    private static final String noShowTokenmessage = MessagesUtil.getInstance().getMessage("meter.noshow.token");

    //these are to re-fresh the TransactionItemView after a reversal.
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private SpecialActionsReasonComponent specialActionsReasonComponent = null;
    private TextBox commentBox = null;
    private VerticalPanel panelReversalDetail = null;
    private PopupPanel reversalPopup = null;

    private Logger logger = Logger.getLogger(TransactionItemsWidget.class.getName());


    public TransactionItemsWidget(UsagePointWorkspaceView usagePointWorkspaceView, Widget widget, CustomerTrans customerTrans, ClientFactory clientFactory, boolean isScheduledTrans) {
        this(widget);
        this.usagePointWorkspaceView = usagePointWorkspaceView;
        this.customerTrans = customerTrans;
        this.clientFactory = clientFactory;
        if (isScheduledTrans) {
            sendReprintPanel.setVisible(false);
        }
        if (customerTrans.isReversed()) {
            sendReprintBtn.removeFromParent();
            panelReversed.setVisible(true);
            reversalCommentLabel.setText(((CustomerTransAlphaDataWithTotals) customerTrans).getReversalComment());
        } else {
            specialActionsReasonComponent = new SpecialActionsReasonComponent(clientFactory, null,
                    SpecialActionsData.REVERSE_TRANSACTION);
            panelReversalDetail = new VerticalPanel();
            panelReversalDetail.add(specialActionsReasonComponent);
            FormElement formElement = new FormElement();
            formElement.setLabelText(MessagesUtil.getInstance().getMessage("customer.txn.comment"));
            commentBox = new TextBox();
            formElement.add(commentBox);
            panelReversalDetail.add(formElement);
        }
        if (customerTrans.getMeterTypeId().equals(MeterTypeE.STS.getId())) {
            stsTokensPanel.setVisible(true);
            imgValidating.setResource(MediaResourceUtil.getInstance().getWaitIcon());
            imgValidating.setVisible(false);
            stsTokenVerifiedPanel.setVisible(false);
            populateEngineeringTokenRecordsForSts(customerTrans.getId(), customerTrans.getMeterNumber());
            setupTokenTable();
        } else {
            stsTokensPanel.removeFromParent();
            stsTokenVerifiedPanel.removeFromParent();
        }

        checkUserPermissions();
    }

    public TransactionItemsWidget(Widget parent) {
        initWidget(uiBinder.createAndBindUi(this));
        setupTable();
        setListeners();
    }

    private void setListeners() {
        sendReprintBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                // Trigger action - showTransactionDetails another popup
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        showReprintOptions();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        vendReversalBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        reverseVend();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
    }

    protected void setupTable() {

        TextColumn<CustomerTransItemData> transtype = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return data.getTransItemTypeDetails().getName();
            }
        };

        TextColumn<CustomerTransItemData> description = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return data.getDescription();
            }
        };

        tokenCol = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                if (data.getToken() != null &&
                        (data.getTransItemType().equals(TransItemTypeE.stdToken.getId())
                                || data.getTransItemType().equals(TransItemTypeE.bsstToken.getId())
                                || data.getTransItemType().equals(TransItemTypeE.bsstRepeat.getId()))) {
                    return noShowTokenmessage;
                }
                return data.getToken();
            }
        };

        TextColumn<CustomerTransItemData> amtCol = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return FormatUtil.getInstance().formatCurrency(data.getAmtInclTax(), true);
            }
        };

        TextColumn<CustomerTransItemData> taxCol = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return FormatUtil.getInstance().formatCurrency(data.getAmtTax(), true);
            }
        };

        TextColumn<CustomerTransItemData> unitsCol = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                if (data.getUnits() != null) {
                    return data.getUnits().toString();
                }
                return "-";
            }
        };

        TextColumn<CustomerTransItemData> tariff = new TextColumn<CustomerTransItemData>() {
            @Override
            public String getValue(CustomerTransItemData data) {
                return data.getTariff();
            }
        };

        clltbltransitems.addColumn(transtype, MessagesUtil.getInstance().getMessage("meter.txn.type"));
        clltbltransitems.addColumn(description, MessagesUtil.getInstance().getMessage("meter.txn.description"));
        clltbltransitems.addColumn(tokenCol, MessagesUtil.getInstance().getMessage("meter.txn.token"));
        clltbltransitems.addColumn(amtCol, MessagesUtil.getInstance().getMessage("meter.txn.amount"));
        clltbltransitems.addColumn(taxCol, MessagesUtil.getInstance().getMessage("meter.txn.tax"));
        clltbltransitems.addColumn(unitsCol, MessagesUtil.getInstance().getMessage("meter.txn.units"));
        clltbltransitems.addColumn(tariff, MessagesUtil.getInstance().getMessage("meter.txn.tariff"));

        dataProvider.addDataDisplay(clltbltransitems);
    }

    protected void setupTokenTable() {

        TextColumn<StsEngineeringTokenData> tokenType = new TextColumn<StsEngineeringTokenData>() {
            @Override
            public String getValue(StsEngineeringTokenData data) {
                return data.getTokenTypeName();
            }
        };

        TextColumn<StsEngineeringTokenData> token = new TextColumn<StsEngineeringTokenData>() {
            @Override
            public String getValue(StsEngineeringTokenData data) {
                return noShowTokenmessage;
            }
        };

        final ButtonCell verifyBtnCell = new ButtonCell() {
            @Override
            public void render(Context context, SafeHtml data, SafeHtmlBuilder sb) {
                if (!customerTrans.isReversed()) {
                    sb.appendHtmlConstant("<button type=\"button\" class=\"gwt-Button\" tabindex=\"-1\">");
                    if (data != null) {
                        sb.append(data);
                    }
                    sb.appendHtmlConstant("</button>");
                }
            }
        };
        final Column<StsEngineeringTokenData,String> verifyBtnCol = new Column<StsEngineeringTokenData,String>(verifyBtnCell) {



            @Override
            public String getValue(StsEngineeringTokenData object) {
                return MessagesUtil.getInstance().getMessage("verify.token");
            }

            @Override
            public void onBrowserEvent(Cell.Context context, final Element elem,
                                       final StsEngineeringTokenData stsEngineeringTokenData, NativeEvent event) {
                super.onBrowserEvent(context,elem,stsEngineeringTokenData,event);
                if (BrowserEvents.CLICK.equals(event.getType()) || BrowserEvents.KEYDOWN.equals(event.getType())) {
                    if (verifiedTokenIds != null && verifiedTokenIds.containsKey(stsEngineeringTokenData.getId())) {
                        displayTokenDetails(verifiedTokenIds.get(stsEngineeringTokenData.getId()),
                                stsEngineeringTokenData.getTokenTypeName());
                    } else {
                        stsTokenVerifiedPanel.setVisible(false);
                        imgValidating.setVisible(true);

                        final StsMeterData stsMeterData = new StsMeterData();
                        stsMeterData.setKeyRevisionNum(stsEngineeringTokenData.getNewKeyRev());
                        stsMeterData.setSupplierGroupRef(stsEngineeringTokenData.getNewSupGroup());
                        stsMeterData.setTariffIndex(stsEngineeringTokenData.getNewTariffIdx());
                        if (usagePointWorkspaceView != null && usagePointWorkspaceView.getStsMeter() != null)
                            stsMeterData.setAlgorithmCode(usagePointWorkspaceView.getStsMeter().getStsAlgorithmCode());
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution resolution) {
                                clientFactory.getMeterRpc().verifyToken(stsEngineeringTokenData.getToken1(), stsEngineeringTokenData.getMeterNumber(),
                                        stsMeterData, new ClientCallback<VerifyTokenDto>() {
                                            @Override
                                            public void onSuccess(VerifyTokenDto result) {
                                                imgValidating.setVisible(false);
                                                //elem.setAttribute("visible", "false");
                                                if (verifiedTokenIds == null) {
                                                    verifiedTokenIds = new HashMap<>();
                                                }
                                                verifiedTokenIds.put(stsEngineeringTokenData.getId(), result);
                                                displayTokenDetails(result, stsEngineeringTokenData.getTokenTypeName());
                                            }

                                            @Override
                                            public void onFailure(Throwable caught) {
                                                super.onFailure(caught);
                                                imgValidating.setVisible(false);
                                            }
                                        });
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            }
        };

        clltbltranstokens.addColumn(tokenType, MessagesUtil.getInstance().getMessage("meter.txn.token.type"));
        clltbltranstokens.addColumn(token, MessagesUtil.getInstance().getMessage("token.label"));

        if (!groupHasGlobal() && clientFactory.getUser().hasPermission(MeterMngStatics.ADMIN_PERMISSION_VEND_REPRINT)) {
            clltbltranstokens.addColumn(verifyBtnCol, MessagesUtil.getInstance().getMessage(""));
        }

        tokenDataProvider.addDataDisplay(clltbltranstokens);
    }

    private void displayTokenDetails(VerifyTokenDto result, String tokenTypeName) {
        if (result!=null) {
            verifiedHeader.setText(tokenTypeName);

            if (result.getVerificationError() != null) {
                Dialogs.displayErrorMessage(result.getVerificationError(),
                        MediaResourceUtil.getInstance().getErrorIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            } else {
                lblTokenClassValue.setText(result.getTokenClassName()+" ("+result.getTokenClass()+")");
                lblTokenSubclassValue.setText(result.getSubClassName()+" ("+result.getSubClass()+")");
                lblTokenIdValue.setText(result.getTokenId().toString());
                BigDecimal units = new BigDecimal((double) result.getTransferAmt()/10).setScale(1, BigDecimal.ROUND_HALF_UP);
                lblUnitsValue.setText(units.toPlainString());
                lblTokenDateValue.setText(FormatUtil.getInstance().formatDateTime(result.getDateGenerated()));
                stsTokenVerifiedPanel.setVisible(true);
            }

        } else {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("verification.error.timeout"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
        }
    }

    public void setMeterTransactionList(ArrayList<CustomerTransItemData> thedata) {
        theTransactionitemdata = thedata;
        dataProvider.setList(theTransactionitemdata);
    }

    private PopupPanel createPopupPanel(final boolean isCenter, final int left, final int top) {
        simplePopup = new PopupPanel(true) {
            @Override
            protected void onLoad() {
                super.onLoad();
                Scheduler.get().scheduleDeferred(new Scheduler.ScheduledCommand() {
                    @Override
                    public void execute() {
                        if (isCenter) {
                            simplePopup.center();
                        } else {
                            simplePopup.setPopupPosition(left, top+20);
                        }
                    }
                });
            }
        };
        simplePopup.setModal(true);
        simplePopup.setGlassEnabled(true);
        simplePopup.setAnimationEnabled(true);
        simplePopup.setStylePrimaryName("transpopup");
        simplePopup.setWidget(this);

        return simplePopup;
    }

    @Override
    public boolean isValid(CustomerTransItemData value, String filter) {
        return true;
    }

    public void showTransactionDetails() {
        int left = 0, top = 0;
        simplePopup = createPopupPanel(IS_CENTER, left, top);
        simplePopup.show();
    }

    public void showTransactionDetails(int left, int top) {
        simplePopup = createPopupPanel(!IS_CENTER, left, top);
        simplePopup.setTitle("Left: " + left + "px Top: " + top + "px");
        simplePopup.show();
    }

    private void showReprintOptions() {
        Map<String, String> tokenInfo = new HashMap<>();
        int i = 1;
        for (CustomerTransItemData data : theTransactionitemdata) {
            if (data.getUnits() != null && data.getToken() != null) {
                tokenInfo.put(data.getToken()+"^"+i, data.getTransItemTypeDetails().getName());
            }
            i++;
        }
        updateCustomerTransReprintDate(tokenInfo);
    }

    private void updateCustomerTransReprintDate(final Map<String, String> tokenInfo) {
        final Date reprintDate = new Date();
        final Long customerTransId = customerTrans.getId();
        //update the customer_trans last_reprint_dt
        clientFactory.getMeterRpc().updateCustTransLastReprintDate(customerTransId, reprintDate, clientFactory.getUser().getUserName(), new ClientCallback<Void>() {
            @Override
            public void onSuccess(Void result) {
                logger.log(Level.INFO, dateTimeFormat.format(reprintDate) + ": Reprinted Vend Token info for CustomerTransID=" + customerTransId);
                        customerTrans.setLastReprintDt(reprintDate);
                        MeterInformation meterInfo = usagePointWorkspaceView.getMeterInfo();
                        if (meterInfo != null) {
                            MeterData md = meterInfo.getMeterData();
                            if (md != null) {
                                meterInfo.populateMeterTransactions();
                            }
                        }
                        UsagePointInformation upInfo = usagePointWorkspaceView.getUpInfo();
                        if (upInfo != null) {
                            UsagePointData upd = upInfo.getUsagePointData();
                            if (upd != null) {
                                upInfo.populateUsagePointTransactions();
                            }
                        }
                        new TokenReprintWidget(clientFactory, simplePopup, tokenInfo, customerTrans,
                                theTransactionitemdata);
            }

            @Override
            public void onFailure(Throwable caught) {
                logger.log(Level.WARNING, "Could not update CustomerTrans reprint date. CustomerTransId=" + customerTransId + "]");
                super.onFailure(caught);
            }
        });
    }

    private void populateEngineeringTokenRecordsForSts(Long customerTransId, String meterNumber) {
        clientFactory.getMeterRpc().getStdBsstEngineeringTokens(customerTrans.getId(), meterNumber, new ClientCallback<ArrayList<StsEngineeringTokenData>>() {
            @Override
            public void onSuccess(ArrayList<StsEngineeringTokenData> result) {
                if (result != null) {
                    if (result.isEmpty()) {
                        stsTokensPanel.removeFromParent();
                        stsTokenVerifiedPanel.removeFromParent();
                    } else {
                        theTokenData = result;
                        tokenDataProvider.setList(theTokenData);
                    }
                }
            }
        });
    }

    private void checkUserPermissions() {

        vendReversalBtn.setVisible(true);
        sendReprintBtn.setVisible(true);

        if (groupHasGlobal()) {
            vendReversalBtn.setVisible(false);
            sendReprintBtn.setVisible(false);
        }

        if (!clientFactory.isAllowReversalsLastTrans()
                || !clientFactory.getUser().hasPermission(MeterMngStatics.ADMIN_PERMISSION_VEND_REVERSALS)
                || customerTrans.isReversed()) {
            vendReversalBtn.removeFromParent();
        }

        if (!clientFactory.getUser().hasPermission(MeterMngStatics.ADMIN_PERMISSION_VEND_REPRINT)
                || customerTrans.isReversed()) {
            sendReprintBtn.removeFromParent();
        }
    }

    private boolean groupHasGlobal() {
        boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(usagePointWorkspaceView.getUsagePoint(), clientFactory.isGroupGroupUser());
        return (clientFactory.isEnableAccessGroups() && (groupHasGlobal || clientFactory.isGroupGlobalUser()));
    }

    private boolean isReprinted(CustomerTrans customerTrans) {
        return customerTrans.getLastReprintDt() != null;
    }

    private void reverseVend() {
            //check if it is the last trans being reversed && whether config settings allow it
        vendReversalBtn.setEnabled(false);
        boolean hasPermission = clientFactory.getUser().hasPermission(MeterMngStatics.ALLOW_REVERSAL_WHEN_REPRINTED);
        if(isReprinted(customerTrans) && !hasPermission) {
            // If the transaction has been reprinted and the user does not have the permission, display an error message and return
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("token.reversal.reprinted.error"),
                    MediaResourceUtil.getInstance().getErrorIcon(),
                    MessagesUtil.getInstance().getMessage("button.close"));
            return;
        }
        if (customerTrans.getLastReprintDt() != null) {
            //go get customerTransExtra list for reprint info
            clientFactory.getMeterRpc().getCustTransExtraListOrderByReprintDateAsc(customerTrans.getId(), new ClientCallback<List<CustomerTransExtra>>() {
                @Override
                public void onSuccess(List<CustomerTransExtra> result) {
                    String reverseConfirmMsg = "";
                    if (result != null && result.size() > 0) {
                        CustomerTransExtra firstReprint = result.get(0);
                        StringBuilder whoReprinted = new StringBuilder(firstReprint.getClient()).append(":");
                        if (firstReprint.getUsername() == null) {
                            whoReprinted.append(firstReprint.getTerminal());
                        } else {
                            whoReprinted.append(firstReprint.getUsername());
                        }

                        reverseConfirmMsg = ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("vend.trans.already.reprinted",
                                            new String[] {String.valueOf(result.size()),
                                                          dateTimeFormat.format(firstReprint.getReprintDate()),
                                                          whoReprinted.toString()}));
                    } else {
                        //shouldn't happen that have a last_reprint_dt on Customer_trans and no extra record - but balance the null check in case
                        reverseConfirmMsg = ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("vend.trans.already.reprinted",
                                new String[] {"1",
                                              dateTimeFormat.format(customerTrans.getLastReprintDt()),
                                              ResourcesFactoryUtil.getInstance().getMessages().getMessage(MessagesUtil.getInstance().getMessage("vend.reprint.user.not.known"))} ));
                    }
                    checkReprintInfo(reverseConfirmMsg);
                }
                @Override
                public void onFailure(Throwable caught) {
                    vendReversalBtn.setEnabled(true);
                    super.onFailure(caught);
                }
            });

        } else {
            checkReprintInfo("");
        }
    }

    private void checkReprintInfo(String reverseConfirmMsg) {
        final int left = vendReversalBtn.getAbsoluteLeft() + vendReversalBtn.getOffsetWidth();
        final int top = vendReversalBtn.getAbsoluteTop() - vendReversalBtn.getOffsetHeight();
        ResourcesFactory resourcesFactory = ResourcesFactoryUtil.getInstance();
        Messages messages = resourcesFactory.getMessages();
        reversalPopup = Dialogs.confirm(new String[] { reverseConfirmMsg + messages.getMessage("vend.reversal.confirm") },
                messages.getMessage("option.positive"), messages.getMessage("option.no"), // cancel
                resourcesFactory.getQuestionIcon(), new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            if (specialActionsReasonComponent.validate()) {
                                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                    @Override
                                    public void callback(SessionCheckResolution resolution) {
                                        reversalPopup.hide();
                                        checkVendReversalConfig(left, top);
                                    }
                                };
                                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                            }
                        } else {
                            vendReversalBtn.setEnabled(true);
                            specialActionsReasonComponent.clearFields();
                            specialActionsReasonComponent.clearErrorMessages();
                            commentBox.setText(null);
                        }
                    }
                }, left, top, panelReversalDetail);
    }

    private void checkVendReversalConfig(final int left, final int top) {
        if (clientFactory != null) {
            ClientCallback<Long> lastVendTransIdSvcAsyncCallback = new ClientCallback<Long>() {
                @Override
                public void onSuccess(Long lastCustAgrTransId) {
                	boolean isLastCustAgrTrans = lastCustAgrTransId.equals(customerTrans.getId());

                    if (!isLastCustAgrTrans && !clientFactory.isAllowReversalsOlderTrans()) {
                    	//not the latest trans & config does not allow reverse older trans
                    	Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("vend.reversal.fail"),
                                MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                                MessagesUtil.getInstance().getMessage("button.close"));
                    	vendReversalBtn.removeFromParent();

                    } else {
                    	doVendReversal(isLastCustAgrTrans, left, top);
                    }
                }

                public void onFailure(Throwable caught) {
                	vendReversalBtn.setEnabled(true);
                	super.onFailure(caught);
                }
            };

            clientFactory.getMeterRpc().selectLastCustAgrTransId(customerTrans.getCustomerAgreementId(), lastVendTransIdSvcAsyncCallback);
        }

    }

    private void doVendReversal(final boolean isLastCustAgrTrans, final int msgLeft, final int msgTop) {
        if (clientFactory != null) {
            ClientCallback<IpayResponseData> reverseVendSvcAsyncCallback = new ClientCallback<IpayResponseData>() {
                @Override
                public void onSuccess(IpayResponseData result) {
                    if (result==null) {
                        Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("vend.reversal.connection.error"),
                                MediaResourceUtil.getInstance().getErrorIcon(), msgLeft, msgTop,
                                MessagesUtil.getInstance().getMessage("button.close"));

                    } else if (!result.getResCode().equals("meterMng000")) {
                        String errMessage = MessagesUtil.getInstance().getMessage("vend.reversal.error", new String[]{result.getResMsg()});
                        if (result.getResCode().equals("meterMng022")) {
                            errMessage = MessagesUtil.getInstance().getMessage("vend.reversal.exceeds.time.limit");
                        }
                        Dialogs.displayErrorMessage(errMessage,
                                                    MediaResourceUtil.getInstance().getErrorIcon(), msgLeft, msgTop,
                                                    MessagesUtil.getInstance().getMessage("button.close"));
                    } else {
                        boolean otherReversalsThisMonth = false;
                        if (result.getExtraData().equalsIgnoreCase("true")) {
                            otherReversalsThisMonth = true;
                        }
                        String mesg = new String();
                        if (!isLastCustAgrTrans) {
                            mesg += MessagesUtil.getInstance().getMessage("vend.older.trans.info");
                        } else if (otherReversalsThisMonth) {   //is the last vend this month but other reversals in the month
                            mesg += MessagesUtil.getInstance().getMessage("vend.reversal.last.with.older");
                        }
                        Dialogs.displayInformationMessages(new String[] {MessagesUtil.getInstance().getMessage("vend.reversal.success", new String[]{result.getOrigRef(), result.getResRef()}),
                                                    mesg},
                                                    MediaResourceUtil.getInstance().getInformationIcon());
                        simplePopup.hide();
                        MeterData md = usagePointWorkspaceView.getMeterInfo().getMeterData();
                        if (md != null) {
                            usagePointWorkspaceView.getMeterInfo().populateMeterTransactions();
                        }
                        UsagePointData upd = usagePointWorkspaceView.getUpInfo().getUsagePointData();
                        if (upd != null) {
                            usagePointWorkspaceView.getUpInfo().populateUsagePointTransactions();
                        }
                    }
                    vendReversalBtn.setEnabled(true);
                }

                public void onFailure(Throwable caught) {
                	vendReversalBtn.setEnabled(true);
                	super.onFailure(caught);
                }
            };
            clientFactory.getMeterRpc().sendVendReversalMsg(customerTrans, clientFactory.isAllowReversalsOlderTrans(),
                    clientFactory.getUser().getUserName(), specialActionsReasonComponent.getLogEntry(),
                    commentBox.getText(), reverseVendSvcAsyncCallback);
        }
    }

    interface TransactionItemsWidgetUiBinder extends UiBinder<Widget, TransactionItemsWidget> {
    }

    class StsEngineeringTokenProviderFilter implements IpayDataProviderFilter<StsEngineeringTokenData> {

        @Override
        public boolean isValid(StsEngineeringTokenData value, String filter) {
            return true;
        }
    }
}
