package za.co.ipay.metermng.client.view.component.customer;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactory;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProvider;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.component.SpecialActionsReasonComponent;
import za.co.ipay.metermng.datatypes.BillPayTransTypeE;
import za.co.ipay.metermng.shared.BillPayTransData;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SpecialActionsData;

import com.google.gwt.cell.client.AbstractCell;
import com.google.gwt.cell.client.ButtonCell;
import com.google.gwt.cell.client.Cell;
import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.ButtonElement;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.NativeEvent;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

public class BillPaymentsView extends BaseComponent implements IpayDataProviderFilter<BillPayTransData>{

    private static final int DEFAULT_PAGE_SIZE = 15;

    interface BillPaymentsWidgetUiBinder extends UiBinder<Widget, BillPaymentsView> {
    }

    private static BillPaymentsWidgetUiBinder uiBinder = GWT.create(BillPaymentsWidgetUiBinder.class);

    @UiField(provided=true) CellTable<BillPayTransData>  clltblHistory;
    @UiField TablePager smplpgrHistory;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;

    private IPayDataProvider<BillPayTransData> dataProvider;
    private List<BillPayTransData> billPayTransDataList;
    private CustomerInformation customerInformation;

    private ListHandler<BillPayTransData> columnSortHandler;

    private boolean viewConstructed = false;

    private PopupPanel reversalPopup = null;
    private SpecialActionsReasonComponent specialActionsReasonComponent = null;
    private VerticalPanel panelReversalDetail = null;
    private TextBox commentBox = null;

    public BillPaymentsView(ClientFactory clientFactory, CustomerInformation customerInformation)  {
        this.clientFactory = clientFactory;
        this.customerInformation = customerInformation;
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
        viewConstructed = true;
    }

    private void initUi() {
        initView();
        initTable();

        specialActionsReasonComponent = new SpecialActionsReasonComponent(clientFactory, null,
                SpecialActionsData.REVERSE_TRANSACTION);
        panelReversalDetail = new VerticalPanel();
        panelReversalDetail.add(specialActionsReasonComponent);
        FormElement formElement = new FormElement();
        formElement.setLabelText(MessagesUtil.getInstance().getMessage("customer.txn.comment"));
        commentBox = new TextBox();
        formElement.add(commentBox);
        panelReversalDetail.add(formElement);
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }

    private void initView() {
        Messages messages = MessagesUtil.getInstance();
        filterDropdown.addItem(messages.getMessage("usagepoint.txn.reference"));
        filterDropdown.addItem(messages.getMessage("usagepoint.txn.date"));
        filterDropdown.addItem(messages.getMessage("usagepoint.txn.receipt"));
    }

    protected void createTable() {
    	 clltblHistory = new CellTable<BillPayTransData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private void initTable() {

        if (dataProvider == null) {
            dataProvider = new IPayDataProvider<BillPayTransData>(this);

            if (columnSortHandler == null || columnSortHandler.getList() == null) {
                columnSortHandler = new ListHandler<BillPayTransData>(dataProvider.getList());

                clltblHistory.addColumnSortHandler(columnSortHandler);
            }

            addColumn("usagepoint.txn.date");
            addColumn("usagepoint.txn.client");
            addColumn("usagepoint.txn.term");
            addColumn("bill.payments.provider");
            addColumn("usagepoint.txn.reference");
            addColumn("usagepoint.txn.revref");
            addColumn("bill.payments.reversal.request.received");

            final Messages messages = MessagesUtil.getInstance();
            Column<BillPayTransData, String> reversalColumn = new Column<BillPayTransData, String>(
                    new AbstractCell<String>() {
                        @Override
                        public void render(Context context, String value, SafeHtmlBuilder sb) {
                            if (value == null) {
                                return;
                            }
                            if (value.equals(messages.getMessage("button.yes"))) {
                                sb.appendHtmlConstant("<span class=\"error\">" + value + "</span>");
                            } else {
                                sb.appendHtmlConstant(
                                        "<span style=\"text-align:center; display:block;\">" + value + "</span>");
                            }
                        }
                    }) {
                @Override
                public String getValue(BillPayTransData data) {
                    return getDataValue("usagepoint.txn.isreversed", data);
                }
            };
            reversalColumn.setSortable(true);
            clltblHistory.addColumn(reversalColumn, messages.getMessage("usagepoint.txn.isreversed"));

            addColumn("meter.txn.reversal.reason");
            addColumn("meter.txn.reversed.by");
            addColumn("usagepoint.txn.receipt");
            addColumn("meter.number");
            addColumn("customer.txn.tax");
            addColumn("customer.txn.amt");
            addColumn("bill.payments.pay.type");
            addColumn("bill.payments.pay.type.details");
            addColumn("usagepoint.field.name");
            addColumn("bill.payments.transaction.type");

            clltblHistory.addColumn(new Column<BillPayTransData, String>(new ButtonCell() {
                @Override
                public void render(Context context, SafeHtml data, SafeHtmlBuilder sb) {
                    if (clientFactory.getUser().hasPermission(MeterMngStatics.ADMIN_PERMISSION_VEND_REVERSALS)
                            && !((BillPayTransData) context.getKey()).isReversed()) {
                        sb.appendHtmlConstant("<button type=\"button\" class=\"gwt-Button\" tabindex=\"-1\">");
                        if (data != null) {
                            sb.append(data);
                        }
                        sb.appendHtmlConstant("</button>");
                    }
                }
            }) {
                @Override
                public String getValue(BillPayTransData object) {
                    return messages.getMessage("reverse.payment");
                }

                @Override
                public void onBrowserEvent(Cell.Context context, final Element elem,
                        final BillPayTransData billPayTransData, NativeEvent event) {
                    super.onBrowserEvent(context, elem, billPayTransData, event);
                    final ButtonElement button = (ButtonElement) elem.getChild(0);
                    button.setDisabled(true);
                    ResourcesFactory resourcesFactory = ResourcesFactoryUtil.getInstance();
                    final Messages messages = resourcesFactory.getMessages();
                    reversalPopup = Dialogs.confirm(
                            new String[] { messages.getMessage("confirm.bill.payment.reversal") },
                            messages.getMessage("option.positive"), messages.getMessage("option.no"),
                            resourcesFactory.getQuestionIcon(), new ConfirmHandler() {
                                @Override
                                public void confirmed(boolean confirm) {
                                    if (confirm) {
                                        if (specialActionsReasonComponent.validate()) {
                                            reversalPopup.hide();
                                            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                                                @Override
                                                public void callback(SessionCheckResolution resolution) {
                                                    clientFactory.getCustomerRpc().sendBillPaymentReversalMsg(billPayTransData,
                                                            clientFactory.getUser().getUserName(),
                                                            specialActionsReasonComponent.getLogEntry(), commentBox.getText(),
                                                            new ClientCallback<IpayResponseData>() {
                                                                @Override
                                                                public void onSuccess(IpayResponseData result) {
                                                                    if (result == null) {
                                                                        Dialogs.displayErrorMessage(
                                                                                messages.getMessage(
                                                                                        "vend.reversal.connection.error"),
                                                                                MediaResourceUtil.getInstance().getErrorIcon(),
                                                                                messages.getMessage("button.close"));
                                                                    } else {
                                                                        if ("meterMng000".equals(result.getResCode())) {
                                                                            customerInformation.loadBillPaymentsView();
                                                                            Dialogs.displayInformationMessages(
                                                                                    new String[] { messages.getMessage(
                                                                                            "bill.payment.reversal.success",
                                                                                            new String[] { result.getOrigRef(),
                                                                                                    result.getResRef() }) },
                                                                                    MediaResourceUtil.getInstance()
                                                                                            .getInformationIcon());
                                                                        } else {
                                                                            Dialogs.displayErrorMessage(
                                                                                    messages.getMessage("vend.reversal.error",
                                                                                            new String[] {
                                                                                                    result.getResMsg() }),
                                                                                    MediaResourceUtil.getInstance()
                                                                                            .getErrorIcon(),
                                                                                    messages.getMessage("button.close"));
                                                                        }

                                                                    }
                                                                    button.setDisabled(false);
                                                                }

                                                                @Override
                                                                public void onFailure(Throwable caught) {
                                                                    button.setDisabled(false);
                                                                    super.onFailure(caught);
                                                                }
                                                            });
                                                }
                                            };
                                            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                                        }
                                    } else {
                                        button.setDisabled(false);
                                    }
                                    specialActionsReasonComponent.clearFields();
                                    specialActionsReasonComponent.clearErrorMessages();
                                    commentBox.setText(null);
                                }
                            }, null, null, panelReversalDetail);
                }
            }, "");
            dataProvider.addDataDisplay(clltblHistory);
            smplpgrHistory.setDisplay(clltblHistory);
        }
        dateFilter.setDataProvider(dataProvider);
    }

    private void addColumn(final String key) {
        TextColumn<BillPayTransData> column = new TextColumn<BillPayTransData>() {
            @Override
            public String getValue(BillPayTransData data) {
                return getDataValue(key, data);
            }
        };
        columnSortHandler.setComparator(column, new Comparator<BillPayTransData>() {
            public int compare(BillPayTransData o1, BillPayTransData o2) {
                return getDataValue(key, o1).compareTo(getDataValue(key, o2));
            }
        });
        column.setSortable(true);
        clltblHistory.addColumn(column, MessagesUtil.getInstance().getMessage(key));
    }

    private String getDataValue(String key, BillPayTransData data) {
        switch (key) {
        case "usagepoint.txn.client":
            return data.getClient();
        case "usagepoint.txn.term":
            return data.getTerminal();
        case "bill.payments.provider":
            return data.getProvider();
        case "usagepoint.txn.reference":
            return data.getRefReceived();
        case "usagepoint.txn.date":
            return getDateValue(data.getTransDate());
        case "usagepoint.txn.revref":
            return data.getRevRefReceived();
        case "bill.payments.reversal.request.received":
            return getDateValue(data.getRevReqReceived());
        case "usagepoint.txn.isreversed":
            Messages messages = MessagesUtil.getInstance();
            return data.isReversed() ? messages.getMessage("button.yes") : messages.getMessage("button.no");
        case "meter.txn.reversal.reason":
            return data.getReversalReason();
        case "meter.txn.reversed.by":
            return data.getReversedBy();
        case "usagepoint.txn.receipt":
            return data.getReceiptNum();
        case "meter.number":
            return data.getMeterNum();
        case "customer.txn.tax":
            return getAmountValue(data.getAmtTax());
        case "customer.txn.amt":
            return getAmountValue(data.getAmtInclTax());
        case "bill.payments.pay.type":
            return data.getPayTypeName();
        case "bill.payments.pay.type.details":
            return data.getPayTypeDetailsName();
        case "usagepoint.field.name":
            return data.getUsagePointName();
        case "bill.payments.transaction.type":
            return BillPayTransTypeE.fromId(data.getBillPayTransTypeId()).name();
        }
        return "";
    }

    private String getDateValue(Date value) {
        if (value == null) {
            return "";
        }
        return FormatUtil.getInstance().formatDateTime(value);
    }

    private String getAmountValue(BigDecimal value) {
        if (value == null) {
            return "";
        }
        return FormatUtil.getInstance().formatCurrency(value, true);
    }

    public void setBillPayTransDataList(List<BillPayTransData> thedata) {
        billPayTransDataList = thedata;
        dataProvider.setList(billPayTransDataList);
        if (columnSortHandler == null || columnSortHandler.getList() == null) {
            columnSortHandler = new ListHandler<BillPayTransData>(dataProvider.getList());

            clltblHistory.addColumnSortHandler(columnSortHandler);
        } else {
            columnSortHandler.setList(dataProvider.getList());
        }
        ColumnSortEvent.fire(clltblHistory, clltblHistory.getColumnSortList());
    }

    @Override
    public boolean isValid(BillPayTransData value, String filter) {
        filter = filter.toLowerCase();
        boolean valid = false;
        String filterValue = filterDropdown.getSelectedValue();
        Messages messages = MessagesUtil.getInstance();
        if (filterValue.equals(messages.getMessage("usagepoint.txn.date"))) {
            Date transDate = value.getTransDate();
            if (transDate != null) {
                valid = dateFilter.isValid(transDate, filter);
            }
        } else if (filterValue.equals(messages.getMessage("usagepoint.txn.reference"))) {
            String refReceived = value.getRefReceived();
            if (refReceived != null) {
                valid = refReceived.toLowerCase().contains(filter);
            }
        } else if (filterValue.equals(messages.getMessage("usagepoint.txn.receipt"))) {
            String receiptNum = value.getReceiptNum();
            if (receiptNum != null) {
                valid = receiptNum.toLowerCase().contains(filter);
            }
        } else {
            valid = true;
        }
        return valid;
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            dataProvider.setFilter(txtbxfilter.getText());
        }
        dataProvider.setList(dataProvider.getList());
        columnSortHandler.setList(dataProvider.getList());
        smplpgrHistory.firstPage();
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        dataProvider.resetFilter();
        boolean isDateSelected = filterDropdown.getSelectedItemText()
                .equals(MessagesUtil.getInstance().getMessage("usagepoint.txn.date"));
        dateFilter.changeFilter(isDateSelected);
        txtbxfilter.setVisible(!isDateSelected);
    }
}
