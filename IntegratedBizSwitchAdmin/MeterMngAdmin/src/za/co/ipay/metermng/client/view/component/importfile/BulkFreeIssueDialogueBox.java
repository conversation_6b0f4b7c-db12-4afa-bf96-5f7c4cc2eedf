package za.co.ipay.metermng.client.view.component.importfile;

import java.math.BigDecimal;
import java.util.List;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueImportRecord;

/**
 * Dialogue box for editing bulk free issue import items
 */
public class BulkFreeIssueDialogueBox extends ImportFileItemBaseDialogueBox {

    private BulkFreeIssueImportRecord recordIn;

    public BulkFreeIssueDialogueBox(ClientFactory clientFactory, ImportFileItemView parent) {
        super(clientFactory, parent);
    }

    @Override
    protected List<ImportRecordField> createDataList(ImportFileItemDto itemDto) {
        BulkFreeIssueImportRecord record = recordIn = itemDto.getBulkFreeIssueImportRecord();
        dataList.clear();
        dataList.add(new ImportRecordField("Meter Number", record.getMeterNumber()));
        dataList.add(new ImportRecordField("Units", record.getUnits() != null ? record.getUnits().toString() : ""));
        dataList.add(new ImportRecordField("Description", record.getDescription() != null ? record.getDescription() : ""));
        dataList.add(new ImportRecordField("Reference", record.getReference() != null ? record.getReference() : ""));
        dataList.add(new ImportRecordField("Reason", record.getReason() != null ? record.getReason() : ""));
        return dataList;
    }

    @Override
    protected void checkDirtyData() {
        BulkFreeIssueImportRecord chgRec = createRecordFromList();
        isDirtyData = false;
        
        if (!chgRec.getMeterNumber().equals(recordIn.getMeterNumber())) {
            isDirtyData = true;
            return;
        }
        if (!chgRec.getUnits().equals(recordIn.getUnits())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(chgRec.getDescription(), recordIn.getDescription())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(chgRec.getReference(), recordIn.getReference())) {
            isDirtyData = true;
            return;
        }
        if (!safeEquals(chgRec.getReason(), recordIn.getReason())) {
            isDirtyData = true;
            return;
        }
    }
    
    private boolean safeEquals(String str1, String str2) {
        if (str1 == null && str2 == null) return true;
        if (str1 == null || str2 == null) return false;
        return str1.equals(str2);
    }

    private BulkFreeIssueImportRecord createRecordFromList() {
        BulkFreeIssueImportRecord chgRec = new BulkFreeIssueImportRecord();
        for (ImportRecordField field : dataProvider.getList()) {
            if (field.getFieldname().equals("Meter Number")) {
                chgRec.setMeterNumber(field.getFieldValue());
            }
            if (field.getFieldname().equals("Units")) {
                try {
                    chgRec.setUnits(new BigDecimal(field.getFieldValue()));
                } catch (NumberFormatException e) {
                    chgRec.setUnits(BigDecimal.ZERO);
                }
            }
            if (field.getFieldname().equals("Description")) {
                chgRec.setDescription(field.getFieldValue());
            }
            if (field.getFieldname().equals("Reference")) {
                chgRec.setReference(field.getFieldValue());
            }
            if (field.getFieldname().equals("Reason")) {
                chgRec.setReason(field.getFieldValue());
            }
        }
        return chgRec;
    }

    @Override
    protected void updateParentRow() {
        importFileItemDto.setGenericImportRecord(createRecordFromList());
    }

    @Override
    protected void displayUpdateMessage() {
        BulkFreeIssueImportRecord record = createRecordFromList();
        Dialogs.displayInformationMessage(
                MessagesUtil.getInstance().getMessage("import.edit.item.update.success",
                        new String[] { record.getMeterNumber(), record.getUnits().toString() }),
                MediaResourceUtil.getInstance().getInformationIcon());
    }

    @Override
    protected void prepareImportFileItem(ImportFileItemDto importFileItemDto) {
        importFileItemDto.setGenericImportRecord(createRecordFromList());
    }
}
