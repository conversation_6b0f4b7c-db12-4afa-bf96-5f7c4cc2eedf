package za.co.ipay.metermng.client.view.component.group.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.i18n.client.DateTimeFormat;
import com.google.gwt.regexp.shared.MatchResult;
import com.google.gwt.regexp.shared.RegExp;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.user.datepicker.client.DateBox;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.BigDecimalValueBox;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.handler.FormDataClickHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.widgets.StrictDateFormat;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.GenGroupParentComponent;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.GroupEntity;
import za.co.ipay.metermng.shared.GenGroupData;
import za.co.ipay.metermng.shared.UsagePointGroupCustomField;

public class EntityContactPanel extends BaseComponent {


    @UiField FlowPanel contactPanel;
    @UiField FlowPanel contactFormPanel;

    @UiField FormElement contactNameElement;
    @UiField TextBox contactNameBox;
    @UiField FormElement contactNumberElement;
    @UiField TextBox contactNumberBox;
    @UiField FormElement contactEmailElement;
    @UiField TextBox contactEmailBox;
    @UiField FormElement contactAddressElement;
    @UiField TextBox contactAddressBox;
    @UiField FormElement taxRefElement;
    @UiField TextBox taxRefBox;

    @UiField HTMLPanel buttons;
    @UiField Button btnSave;
    @UiField Button btnCancel;
    @UiField Button btnBack;

    private HasDirtyData hasDirtyData;

    private GenGroupParentComponent parentWorkspace;
    private GenGroupData genGroupData;
    private GroupEntity entity;

    private final Map<String, Pair<FormElement, Widget>> widgetMap = new HashMap<>();
    private Map<String, UsagePointGroupCustomField> availableCustomFields = new HashMap<>();

    private final String customFieldPrefix = "customField";
    final String textFieldLabel = MessagesUtil.getInstance().getMessage("user.custom.field.datatype.text");
    final String checkBoxFieldLabel = MessagesUtil.getInstance().getMessage("user.custom.field.datatype.boolean");
    final String dropDownListFieldLabel = MessagesUtil.getInstance().getMessage("user.custom.field.datatype.list");
    final String dateFieldLabel = MessagesUtil.getInstance().getMessage("user.custom.field.datatype.date");
    final String numericFieldLabel = MessagesUtil.getInstance().getMessage("user.custom.field.datatype.numeric");

    final String optionalField = MessagesUtil.getInstance().getMessage("user.custom.field.status.optional");
    final String requiredField = MessagesUtil.getInstance().getMessage("user.custom.field.status.required");
    final String unavailableField = MessagesUtil.getInstance().getMessage("user.custom.field.status.unavailable");

    private static EntityPanelUiBinder uiBinder = GWT.create(EntityPanelUiBinder.class);

    interface EntityPanelUiBinder extends UiBinder<Widget, EntityContactPanel> {
    }

    private static Logger logger = Logger.getLogger(EntityContactPanel.class.getName());

    public EntityContactPanel(GenGroupParentComponent parentWorkspace, ClientFactory clientFactory) {
        this(null,  parentWorkspace, clientFactory, null);
    }

    public EntityContactPanel(Runnable afterLoad, GenGroupParentComponent parentWorkspace, ClientFactory clientFactory, HasDirtyData hasDirtyData) {
        this.parentWorkspace = parentWorkspace;

        if (parentWorkspace != null && parentWorkspace.getHasDirtyDataManager()!=null) {
            this.hasDirtyData = parentWorkspace.getHasDirtyDataManager().createAndRegisterHasDirtyData();
        } else {
            this.hasDirtyData = hasDirtyData;
        }
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
        addFieldHandlersForStaticFields();
        loadCustomComponents(afterLoad);
    }

    public void loadCustomComponents(final Runnable afterLoad) {
        this.clientFactory.getAppSettingRpc().getAppSettingsForUsagePointGroupCustomFields(new ClientCallback<ArrayList<AppSetting>>() {
            @Override
            public void onSuccess(ArrayList<AppSetting> appSettingList) {
                Map<String, UsagePointGroupCustomField> fieldMap = loadUsagePointGroupCustomFields(appSettingList);
                populateWidgets(fieldMap);
                if(afterLoad != null) {
                    afterLoad.run();
                }
            }
        });
    }

    @UiHandler("btnSave")
    public void save(ClickEvent e) {
        saveEntity();
    }

    @UiHandler("btnCancel")
    public void cancel(ClickEvent e) {
        hasDirtyData.checkDirtyData(new ConfirmHandler() {
            @Override
            public void confirmed(boolean confirm) {
                if (confirm) {
                    hasDirtyData.setDirtyData(false);
                    clear();
                }
            }
        });
    }

    @UiHandler("btnBack")
    public void back(ClickEvent e) {
        parentWorkspace.goBack();
    }

    public void changeButtonsPanelVisibility(boolean isVisible) {
        buttons.setVisible(isVisible);
    }

    public void removeSomeBtns() {
        //for when Entity Panel is used on UsagePointPage where there is NO back and cancel has different function
        btnBack.removeFromParent();
        btnCancel.removeFromParent();
    }

    public HTMLPanel getButtonPanel() {
        return buttons;
    }

    public void loadEntity(GenGroupData genGroupData) {
        logger.info("EntityContactPanel: loadEntity(" + genGroupData.getName() + ")");
        clear();
        this.genGroupData = genGroupData;
        if (genGroupData.getGroupEntityId() != null) {
            clientFactory.getGroupRpc().getEntity(genGroupData.getGroupEntityId(), new ClientCallback<GroupEntity>() {
                @Override
                public void onSuccess(GroupEntity result) {
                    display(result);
                }
            });
        }
    }

    public void setGenGroupData(GenGroupData genGroupData) {
        this.genGroupData = genGroupData;
    }

    public void display(GroupEntity entity) {
        this.entity = entity;
        contactNameBox.setText(entity.getContactName());
        contactNumberBox.setText(entity.getContactNumber());
        contactEmailBox.setText(entity.getContactEmail());
        contactAddressBox.setText(entity.getContactAddress());
        taxRefBox.setText(entity.getTaxRef());

        displayForCustomFields(entity);
    }

    private void clear() {
        clearErrors();
        clearFields();
    }

    private void clearFields() {
        this.entity = null;
        contactNameBox.setText("");
        contactNumberBox.setText("");
        contactEmailBox.setText("");
        contactAddressBox.setText("");
        taxRefBox.setText("");

        for (Map.Entry<String, Pair<FormElement, Widget>> entry : widgetMap.entrySet()) {
            Widget widget = entry.getValue().getB();
            if (widget instanceof TextBox) {
                ((TextBox) widget).setText("");
            } else if (widget instanceof CheckBox) {
                ((CheckBox) widget).setValue(false);
            } else if (widget instanceof ListBox) {
                ListBox listBox = ((ListBox) widget);
                if (!listBox.getValue(0).trim().isEmpty()) {
                    listBox.removeItem(0);
                }
                ((ListBox) widget).setSelectedIndex(0);
            } else if (widget instanceof DateBox) {
                ((DateBox) widget).setValue(null);
            } else if (widget instanceof BigDecimalValueBox) {
                ((BigDecimalValueBox) widget).setValue(null);
            }
        }
        hasDirtyData.setDirtyData(false);
    }

    private void clearErrors() {
        contactNameElement.clearErrorMsg();
        contactNumberElement.clearErrorMsg();
        contactEmailElement.clearErrorMsg();
        contactAddressElement.clearErrorMsg();
        taxRefElement.clearErrorMsg();

        for (Map.Entry<String, Pair<FormElement, Widget>> entry : widgetMap.entrySet()) {
            Widget widget = entry.getValue().getA();
            if (widget instanceof FormElement) {
                ((FormElement) widget).clearErrorMsg();
            }
        }
    }

    public void addFieldHandlersForStaticFields() {
        // handlers for custom fields are added in the createXXX() methods
        contactNameBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        contactNumberBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        contactEmailBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        contactAddressBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        contactNumberBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        taxRefBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    private void saveEntity() {
        mapFormToData();
        if (isValidInput()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    clientFactory.getGroupRpc().updateEntity(genGroupData.getId(), entity,
                            new ClientCallback<GroupEntity>(btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop()) {
                                @Override
                                public void onSuccess(GroupEntity result) {
                                    entity = result;
                                    genGroupData.setGroupEntityId(entity.getId());
                                    display(result);
                                    // clear dirty data after successful save
                                    hasDirtyData.setDirtyData(false);
                                    Dialogs.displayInformationMessage(
                                            MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("groupentity.title") }),
                                            MediaResourceUtil.getInstance().getInformationIcon(),
                                            btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(),
                                            MessagesUtil.getInstance().getMessage("button.close"));
                                }
                            });
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    private boolean isValidInput() {
        logger.info("Doing validation...");
        boolean valid = true;
        clearErrors();
        if (genGroupData == null) {
            logger.info("Validation: genGroup is null! " + " for entity: "+entity.getContactEmail());
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(entity, "contactName", contactNameElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(entity, "contactNumber", contactNumberElement)) {
            valid = false;
        }
        if (!contactEmailBox.getText().trim().equals("")) {
            if (!ClientValidatorUtil.getInstance().validateField(entity, "contactEmail", contactEmailElement)) {
                valid = false;
            }
        }
        if (!ClientValidatorUtil.getInstance().validateField(entity, "contactAddress", contactAddressElement)) {
            valid = false;
        }
        if (!ClientValidatorUtil.getInstance().validateField(entity, "taxRef", taxRefElement)) {
            valid = false;
        }
        for (Map.Entry<String, Pair<FormElement, Widget>> entry : widgetMap.entrySet()) {

            FormElement customElement = (FormElement) entry.getValue().getA();
            UsagePointGroupCustomField customField = availableCustomFields.get(entry.getKey());
            Widget widget = entry.getValue().getB();
            if (customField.getFieldType().equals(numericFieldLabel)) {
                BigDecimalValueBox numericBox = (BigDecimalValueBox) widget;
                if (numericBox.getValue() == null && (!"".equals(numericBox.getText())
                        || requiredField.equalsIgnoreCase(customField.getFieldStatus()))) {
                    valid = false;
                    customElement.showErrorMsg(
                            MessagesUtil.getInstance().getMessage("usagepointgroup.custom.field.error.numeric"));
                }
            } else if(customField.getFieldType().equals(dateFieldLabel)) {
                TextBox dateTextBox = ((DateBox) widget).getTextBox();
                dateTextBox.addStyleName("dateBoxWidth");
                String dateValue = dateTextBox.getText();
                if (dateValue.trim().isEmpty()) {
                    if (requiredField.equalsIgnoreCase(customField.getFieldStatus())) {
                        valid = false;
                        customElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepointgroup.custom.field.error.date.empty"));
                    }
                } else {
                    DateTimeFormat dtf = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss");
                    try {
                        dtf.parseStrict(dateValue);
                    } catch (IllegalArgumentException e) {
                        valid = false;
                        customElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepointgroup.custom.field.error.date.invalid"));
                    }
                }
            } else if(customField.getFieldType().equals(dropDownListFieldLabel)){
                ListBox listBox = ((ListBox) widget);
                String fieldValue = listBox.getValue(listBox.getSelectedIndex());
                if(customField.getFieldStatus().equals(requiredField) && fieldValue.isEmpty()) {
                    valid = false;
                    customElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepointgroup.custom.field.error.text"));
                }
            } else if(customField.getFieldType().equals(textFieldLabel)) {
                TextBox textBox = (TextBox)widget;
                String fieldValue = textBox.getText();
                if(fieldValue.trim().isEmpty() && customField.getFieldStatus().equals(requiredField)) {
                    valid = false;
                    customElement.showErrorMsg(MessagesUtil.getInstance().getMessage("usagepointgroup.custom.field.error.text"));
                }
            }
        }
        logger.info("Done validation: "+valid + " for entity: "+entity.getContactEmail());
        return valid;
    }

    private void mapFormToData() {
        if (entity == null) {
            this.entity = new GroupEntity();
        }
        entity.setContactName(contactNameBox.getText());
        entity.setContactNumber(contactNumberBox.getText());
        entity.setContactEmail(contactEmailBox.getText());
        entity.setContactAddress(contactAddressBox.getText());
        entity.setTaxRef(taxRefBox.getText());

        mapCustomFormFieldsToData(entity);

        logger.info("MapFormToData entity: "+entity.getContactEmail());
    }

    // Custom Fields
    private FormElement addWidgetToPanel(Widget widget, String label, String helpMsg, String errorMsg, boolean isRequired) {
        FormRowPanel formRowPanel = new FormRowPanel();
        FormElement formElement = new FormElement();
        formElement.setRequired(isRequired);
        formElement.setLabelText(label);
        formElement.add(widget);
        formRowPanel.add(formElement);
        contactFormPanel.add(formRowPanel);
        return formElement;
    }

    private CheckBox createCheckBox(String name, boolean isChecked) {
        CheckBox checkBox = new CheckBox();
        checkBox.setValue(isChecked);
        checkBox.setName(name);
        checkBox.addClickHandler(new FormDataClickHandler(hasDirtyData));
        return checkBox;
    }

    private DateBox createDateBox(String name, Date defaultDate) {
        DateTimeFormat sdf = DateTimeFormat.getFormat("yyyy-MM-dd HH:mm:ss");
        DateBox.Format format = new StrictDateFormat(sdf);
        DateBox dateBox = new DateBox();
        dateBox.setFormat(format);
        dateBox.getTextBox().setName(name);
        if(defaultDate!=null) {
            dateBox.getTextBox().setText(sdf.format(defaultDate));
        }
        dateBox.addValueChangeHandler(new FormDataValueChangeHandler<Date>(hasDirtyData));
        return dateBox;
    }

    private TextBox createTextBox(String name, String text) {
        TextBox textBox = new TextBox();
        textBox.setVisibleLength(40);
        textBox.setName(name);
        textBox.setText(text);
        textBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        return textBox;
    }

    private ListBox createListBox(String name, List<String> items) {

        ListBox listBox = new ListBox();
        listBox.setName(name);
        listBox.addItem("","");
        for (String entry : items) {
            listBox.addItem(entry, entry);
        }
        listBox.setSelectedIndex(0);
        listBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        return listBox;
    }

    private BigDecimalValueBox createNumericBox(String name) {
        BigDecimalValueBox numericBox = new BigDecimalValueBox();
        numericBox.setVisibleLength(40);
        numericBox.setName(name);
        numericBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        return numericBox;
    }

    private Map<String, UsagePointGroupCustomField> loadUsagePointGroupCustomFields(List<AppSetting> appSettingList) {
        Map<String, UsagePointGroupCustomField> fieldMap = new HashMap<String, UsagePointGroupCustomField>();
        final RegExp pattern = RegExp.compile("^usagepointgroup\\.[a-zA-Z_]+(\\d{1,})\\.(status|label|datatype)$");
        for (AppSetting setting : appSettingList) {
            MatchResult matcher = pattern.exec(setting.getKey());
            if (pattern.test(setting.getKey())) {
                String key = matcher.getGroup(1);
                String value = matcher.getGroup(2);

                UsagePointGroupCustomField customField = fieldMap.get(customFieldPrefix+key);
                if (customField == null) {
                    customField = new UsagePointGroupCustomField();
                    fieldMap.put(customFieldPrefix+key, customField);
                }

                if ("datatype".equals(value)) {
                    customField.setFieldType(setting.getValue());
                    customField.setFieldName(customFieldPrefix+key);
                    customField.setDefaultValue(setting.getDescription());
                } else if ("label".equals(value)) {
                    customField.setFieldLabel(setting.getValue());
                    customField.setFieldHelpMsg(setting.getDescription());
                } else if ("status".equals(value)) {
                    customField.setFieldStatus(setting.getValue());
                    customField.setFieldErrorMsg(setting.getDescription());
                }
            }
        }
        return fieldMap;
    }

    private void populateWidgets(Map<String, UsagePointGroupCustomField> widgetsMap) {
        if (widgetsMap == null) {
            logger.log(Level.WARNING, ">>>PW: widgetsMap is null. No further processing for ExtraInfoPanel::populateWidgets(Map)");
            return;
        }

        availableCustomFields.clear();
        removeCustomFields();
        for (Map.Entry<String, UsagePointGroupCustomField> entry : widgetsMap.entrySet()) {
            if (!entry.getValue().getFieldStatus().equalsIgnoreCase("UNAVAILABLE")) {
                availableCustomFields.put(entry.getKey(), entry.getValue());
                UsagePointGroupCustomField field = entry.getValue();
                String key = entry.getKey();
                String type = field.getFieldType();
                String boxName = key+"Box";

                Widget widget;
                if(dropDownListFieldLabel.equals(type)) {
                    String[] listItems = field.getDefaultValue().split(",");
                    for(int i = 0, j = listItems.length; i<j; i++) {
                        listItems[i] = listItems[i].replace("%2C", ",").trim();
                    }
                    widget = createListBox(boxName, Arrays.asList(listItems));
                } else if (checkBoxFieldLabel.equals(type)) {
                    widget = createCheckBox(boxName, false);
                } else if(dateFieldLabel.equals(type)) {
                    widget = createDateBox(boxName, null);
                } else if(numericFieldLabel.equals(type)) {
                    widget = createNumericBox(boxName);
                }else {
                    widget = createTextBox(boxName, "");
                }

                FormElement formElement = addWidgetToPanel(widget,
                                 field.getFieldLabel() + ":",
                                 field.getFieldHelpMsg(),
                                 field.getFieldErrorMsg(),
                                 field.getFieldStatus().equalsIgnoreCase(requiredField));
                formElement.ensureDebugId(key+"Element");
                widgetMap.put(key, new Pair<FormElement, Widget>(formElement, widget));
            }
        }
        displayForCustomFields(entity);
    }

    private void mapCustomFormFieldsToData(GroupEntity entity) {
        if(entity==null) {
            logger.log(Level.WARNING, ">>PW: Entity is currently not initialized. Skipping");
            return;
        }
        for (Map.Entry<String, Pair<FormElement, Widget>> entry : widgetMap.entrySet()) {
            String key = entry.getKey();

            String fieldValue = "";

            Widget widget = entry.getValue().getB();
            if (widget instanceof TextBox) {
                fieldValue = ((TextBox) widget).getText();
            } else if(widget instanceof CheckBox) {
               boolean value = ((CheckBox) widget).getValue();
               fieldValue = String.valueOf(value);
            } else if(widget instanceof ListBox) {
                ListBox listBox = ((ListBox) widget);
                fieldValue = listBox.getValue(listBox.getSelectedIndex());
                if (fieldValue.trim().isEmpty()) {
                    fieldValue=null;
                }
            } else if(widget instanceof DateBox) {
                fieldValue = ((DateBox) widget).getTextBox().getValue();
            } else if (widget instanceof BigDecimalValueBox) {
                fieldValue = ((BigDecimalValueBox) widget).getText();
            }

            if (key.equals(customFieldPrefix + 1)) {
                entity.setCustomField1(fieldValue);
            } else if (key.equals(customFieldPrefix + 2)) {
                entity.setCustomField2(fieldValue);
            } else if (key.equals(customFieldPrefix + 3)) {
                entity.setCustomField3(fieldValue);
            } else if (key.equals(customFieldPrefix + 4)) {
                entity.setCustomField4(fieldValue);
            } else if (key.equals(customFieldPrefix + 5)) {
                entity.setCustomField5(fieldValue);
            } else if (key.equals(customFieldPrefix + 6)) {
                entity.setCustomField6(fieldValue);
            } else if (key.equals(customFieldPrefix + 7)) {
                entity.setCustomField7(fieldValue);
            } else if (key.equals(customFieldPrefix + 8)) {
                entity.setCustomField8(fieldValue);
            } else if (key.equals(customFieldPrefix + 9)) {
                entity.setCustomField9(fieldValue);
            } else if (key.equals(customFieldPrefix + 10)) {
                entity.setCustomField10(fieldValue);
            }
        }
    }

    public void displayForCustomFields(GroupEntity entity) {
        if(entity==null) {
            logger.log(Level.WARNING, ">>PW: Entity is currently not initialized. Skipping");
            return;
        }
        for (Map.Entry<String, Pair<FormElement, Widget>> entry : widgetMap.entrySet()) {
            String key = entry.getKey();
            String fieldValue = "";
            if (key.equals(customFieldPrefix + 1)) {
                fieldValue = entity.getCustomField1();
            } else if (key.equals(customFieldPrefix + 2)) {
                fieldValue = entity.getCustomField2();
            } else if (key.equals(customFieldPrefix + 3)) {
                fieldValue = entity.getCustomField3();
            } else if (key.equals(customFieldPrefix + 4)) {
                fieldValue = entity.getCustomField4();
            } else if (key.equals(customFieldPrefix + 5)) {
                fieldValue = entity.getCustomField5();
            } else if (key.equals(customFieldPrefix + 6)) {
                fieldValue = entity.getCustomField6();
            } else if (key.equals(customFieldPrefix + 7)) {
                fieldValue = entity.getCustomField7();
            } else if (key.equals(customFieldPrefix + 8)) {
                fieldValue = entity.getCustomField8();
            } else if (key.equals(customFieldPrefix + 9)) {
                fieldValue = entity.getCustomField9();
            } else if (key.equals(customFieldPrefix + 10)) {
                fieldValue = entity.getCustomField10();
            }

            Widget widget = entry.getValue().getB();
            if (widget instanceof TextBox) {
                ((TextBox) widget).setText(fieldValue);
            } else if( widget instanceof DateBox) {
                if (fieldValue != null) {
                    ((DateBox) widget).getTextBox().setText(fieldValue);
                }
            } else if(widget instanceof CheckBox) {
                ((CheckBox) widget).setValue(Boolean.valueOf(fieldValue));
            } else if(widget instanceof ListBox) {
                ListBox listBox = ((ListBox) widget);
                boolean itemFound = false;
                for(int i = 0, j = listBox.getItemCount(); i < j; i++) {
                    if(listBox.getItemText(i).equalsIgnoreCase(fieldValue)) {
                        itemFound = true;
                        listBox.setSelectedIndex(i);
                        break;
                    }
                }
                if (!itemFound && fieldValue != null) {
                    listBox.insertItem(fieldValue, 0);
                    listBox.setSelectedIndex(0);
                }
            } else if (widget instanceof BigDecimalValueBox) {
                if(fieldValue != null && !fieldValue.isEmpty()) {
                    ((BigDecimalValueBox) widget).setValue(new BigDecimal(fieldValue));
                }
            }
        }
    }

    private void removeCustomFields() {
        for (Map.Entry<String, Pair<FormElement, Widget>> entry : widgetMap.entrySet()) {
            entry.getValue().getA().getParent().getElement().getParentElement().removeFromParent();
        }
        widgetMap.clear();
    }

    private class Pair<A, B>{
        A a; B b;
        Pair(A a, B b){
            this.a = a;
            this.b = b;
        }

        B getB() {
            return b;
        }

        A getA() {
            return a;
        }
    }

}
