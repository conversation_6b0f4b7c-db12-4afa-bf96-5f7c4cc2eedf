package za.co.ipay.metermng.client.view.component.customer;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.logical.shared.OpenEvent;
import com.google.gwt.event.logical.shared.OpenHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.ui.DisclosurePanel;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import java.util.ArrayList;
import java.util.List;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.event.AuxAccountEvent;
import za.co.ipay.metermng.client.event.AuxAccountEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.customer.auxaccounthistory.AuxAccountHistSelectorView;
import za.co.ipay.metermng.client.view.component.location.LocationHistoryView;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.mybatis.generated.model.CustomerAgreement;
import za.co.ipay.metermng.shared.BillPayTransData;
import za.co.ipay.metermng.shared.CustomerAccountTransData;
import za.co.ipay.metermng.shared.CustomerAgreementHistData;
import za.co.ipay.metermng.shared.CustomerHistData;
import za.co.ipay.metermng.shared.LocationHistData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.dto.CustomerAgreementData;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

public class CustomerInformation extends BaseComponent {

    public static final String STYLES_CONTAINER = "container";
    private static CustomerInformationUiBinder uiBinder = GWT.create(CustomerInformationUiBinder.class);

    interface CustomerInformationUiBinder extends UiBinder<Widget, CustomerInformation> {
    }

    @UiField DisclosurePanel dsclsrAuxAccounts;
    @UiField VerticalPanel auxAccountViewVP;
    @UiField DisclosurePanel dsclsrAuxAccountsHistory;
    @UiField VerticalPanel auxAccountHistoryVP;
    @UiField DisclosurePanel dsclsrCustAccTrans;
    @UiField VerticalPanel customerAccTransactionsVP;
    @UiField DisclosurePanel dsclsrCustHistory;
    @UiField VerticalPanel custHistoryVP;
    @UiField DisclosurePanel dsclsrBillPayments;
    @UiField VerticalPanel billPaymentsVP;

    private AuxAccountView auxAccountView;
    private AuxAccountHistSelectorView auxAccountHistSelectorView;
    private CustomerAccountTransactionViews customerAccTransactions;
    private CustomerHistoryView customerHistory;
    private CustomerAgreementHistoryView customerAgreementHistory;
    private LocationHistoryView locationHistory;
    private BillPaymentsView billPaymentsView;

    private ClientFactory clientFactory;
    private CustomerAgreementData customerAgreement;
    private ArrayList<CustomerHistData> customerHistoryList;
    private ArrayList<CustomerAgreementHistData> customerAgreementHistoryList;
    private ArrayList<LocationHistData> locationHistoryList;
    private UsagePointWorkspaceView usagePointWorkspaceView;
    private ArrayList<AppSetting> customFieldList;
    private List<BillPayTransData> billPayTransDataList;

    private boolean usagePointActive;

    public CustomerInformation(UsagePointWorkspaceView usagePointWorkspaceView, ClientFactory clientFactory, ArrayList<AppSetting> customFieldList) {
        this.clientFactory = clientFactory;
    	this.usagePointWorkspaceView = usagePointWorkspaceView;
    	this.customFieldList = customFieldList;
    	initWidget(uiBinder.createAndBindUi(this));
    	init();
    }

    protected void init() {
        MeterMngUser user = clientFactory.getUser();
        if (user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_ACCOUNT)
                || user.hasPermission(MeterMngStatics.VIEW_ONLY_MM_AUX_ACCOUNTS)
                || user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_DEBT)
                || user.hasPermission(MeterMngStatics.ADMIN_PERMISSION_MM_AUX_REFUND)) {
            clientFactory.getEventBus().addHandler(AuxAccountEvent.TYPE, new AuxAccountEventHandler() {
                @Override
                public void processAuxAccountEvent(AuxAccountEvent event) {
                    if (customerAgreement != null
                            && customerAgreement.getId() != null
                            && event.getCustomerAgreementId() != null
                            && event.getCustomerAgreementId().equals(customerAgreement.getId())) {
                        populateAuxAccounts();
                    }
                }
            });
        } else {
            dsclsrAuxAccounts.removeFromParent();
        }

        this.dsclsrAuxAccounts.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (auxAccountView == null || !auxAccountView.isViewConstructed()) {
                    auxAccountView = new AuxAccountView(usagePointWorkspaceView, clientFactory);
                    auxAccountViewVP.add(auxAccountView);
                }
                auxAccountView.setCustomerAgreement(customerAgreement);
                auxAccountView.createRadioButtonsGroup(usagePointWorkspaceView.getPlaceString());
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateAuxAccounts();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        this.dsclsrAuxAccountsHistory.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (auxAccountHistSelectorView == null || !auxAccountHistSelectorView.isViewConstructed()) {
                    auxAccountHistSelectorView = new AuxAccountHistSelectorView(usagePointWorkspaceView, clientFactory);
                    auxAccountHistoryVP.add(auxAccountHistSelectorView);
                }
                auxAccountHistSelectorView.setCustomerAgreement(customerAgreement);
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateAuxAccountHistSelector();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        this.dsclsrCustAccTrans.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (customerAccTransactions == null || !customerAccTransactions.isViewConstructed()) {
                    customerAccTransactions =  new CustomerAccountTransactionViews(usagePointWorkspaceView, clientFactory);
                    customerAccTransactions.setSize("100%", "550px");
                    customerAccTransactionsVP.add(customerAccTransactions);
                }
                customerAccTransactions.getTablePanel().setCustomerAgreementData(customerAgreement);
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateCustomerTransactions();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        this.dsclsrCustHistory.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (customerHistory == null || !customerHistory.isViewConstructed()) {
                    customerHistory = new CustomerHistoryView(customFieldList, clientFactory);
                    customerHistory.addStyleName(STYLES_CONTAINER);

                    customerAgreementHistory = new CustomerAgreementHistoryView();
                    customerAgreementHistory.addStyleName(STYLES_CONTAINER);

                    locationHistory = new LocationHistoryView();
                    locationHistory.addStyleName(STYLES_CONTAINER);

                    custHistoryVP.add(customerHistory);
                    custHistoryVP.add(customerAgreementHistory);
                    custHistoryVP.add(locationHistory);
                }
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        populateCustomerHistory();
                        populateCustomerAgreementHistory();
                        populatePhysicalLocationHistory();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });

        dsclsrBillPayments.addOpenHandler(new OpenHandler<DisclosurePanel>() {
            @Override
            public void onOpen(OpenEvent<DisclosurePanel> event) {
                if (billPaymentsView == null || !billPaymentsView.isViewConstructed()) {
                    billPaymentsView = new BillPaymentsView(clientFactory, CustomerInformation.this);
                    billPaymentsView.addStyleName(STYLES_CONTAINER);
                    billPaymentsVP.add(billPaymentsView);
                }
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        loadBillPaymentsView();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
    }

    void loadBillPaymentsView() {
        if (customerAgreement != null) {
            billPaymentsView.setVisible(true);
            clientFactory.getCustomerRpc().fetchBillPayments(customerAgreement.getId(),
                    new ClientCallback<List<BillPayTransData>>() {
                        @Override
                        public void onSuccess(List<BillPayTransData> result) {
                            if (result != null) {
                                billPayTransDataList = result;
                                billPaymentsView.setBillPayTransDataList(billPayTransDataList);
                            }
                        }
                    });
        } else {
            billPaymentsView.setVisible(false);
        }
    }

    public void populateCustomerHistory() {
        if (customerAgreement != null && customerAgreement.getCustomerId() != null) {
            customerHistory.setVisible(true);
            clientFactory.getCustomerRpc().fetchCustomerHistory(customerAgreement.getCustomerId(), new ClientCallback<ArrayList<CustomerHistData>>() {
                @Override
                public void onSuccess(ArrayList<CustomerHistData> result) {
                    if (result != null) {
                        customerHistoryList = result;
                        customerHistory.setCustomerHistoryList(customerHistoryList);
                    }
                }
            });
        } else {
            customerHistory.setVisible(false);
        }
    }

    public void populateCustomerAgreementHistory() {
        if (customerAgreement != null && customerAgreement.getId() != null) {
            customerAgreementHistory.setVisible(true);
            clientFactory.getCustomerRpc().fetchCustomerAgreementHistory(customerAgreement.getId(), new ClientCallback<ArrayList<CustomerAgreementHistData>>() {
                @Override
                public void onSuccess(ArrayList<CustomerAgreementHistData> result) {
                    if (result != null) {
                        customerAgreementHistoryList = result;
                        customerAgreementHistory.setCustomerAgreementHistoryList(customerAgreementHistoryList);
                    }
                }
            });
        } else {
            customerAgreementHistory.setVisible(false);
        }
    }

    public void populatePhysicalLocationHistory() {
        if (customerAgreement != null && customerAgreement.getCustomerData() != null) {
            if (customerAgreement.getCustomerData().getPhysicalLocationId() != null) {
                locationHistory.setVisible(true);
                locationHistory.setHeadingText(MessagesUtil.getInstance().getMessage("location.history.physical.address"));
                clientFactory.getLocationRpc().getLocationHistory(customerAgreement.getCustomerData().getPhysicalLocationId(), new ClientCallback<ArrayList<LocationHistData>>() {
                    @Override
                    public void onSuccess(ArrayList<LocationHistData> result) {
                        if (result != null) {
                            locationHistoryList = result;
                            locationHistory.setLocationHistoryList(locationHistoryList);
                        }
                    }
                });
            }
        } else {
            locationHistory.setVisible(false);
        }
    }

    protected void populateCustomerTransactions() {
        if (customerAgreement != null && customerAgreement.getCustomerId() != null) {
            clientFactory.getCustomerRpc().fetchCustomerAccountTransactions(customerAgreement, new ClientCallback<ArrayList<CustomerAccountTransData>>() {
                @Override
                public void onSuccess(ArrayList<CustomerAccountTransData> result) {
                    if (result != null) {
                        customerAccTransactions.getTablePanel().setCustomerAccTransList(result);
                        customerAccTransactions.getGraphPanel().populateTransactionData(result);
                    }
                }
            });
        }
    }

    protected void populateAuxAccountHistSelector() {
        if (customerAgreement != null && customerAgreement.getId() != null) {
            if (auxAccountHistSelectorView != null && auxAccountHistSelectorView.isViewConstructed()) {
                auxAccountHistSelectorView.populateAuxAccountList();

            }
        }
    }

            protected void populateAuxAccounts() {
        if (customerAgreement != null && customerAgreement.getId() != null) {
            if (auxAccountView != null && auxAccountView.isViewConstructed()) {
                auxAccountView.populateAuxAccountList();
            }
        }
    }

    public void setCustomerAgreementInfo(CustomerAgreementData customerAgreement) {
        this.customerAgreement = customerAgreement;
    }

    public boolean isUsagePointActive() {
        return usagePointActive;
    }

    public void setUsagePointActive(boolean usagePointActive) {
        this.usagePointActive = usagePointActive;
    }

    public CustomerAgreement getCustomerAgreement() {
        return customerAgreement;
    }

    public AuxAccountView getAuxAccountView() {
        return auxAccountView;
    }

    public CustomerAccountTransactionViews getCustomerAccTransactions() {
        return customerAccTransactions;
    }

    public void auxAccountViewRefreshTransactionTable() {
        if (auxAccountView != null && auxAccountView.isViewConstructed()) {
            auxAccountView.refreshTransactionTable();
        }
    }

    public void auxAccountHistoryViewRefreshTransactionTable() {
        if (auxAccountHistSelectorView != null && auxAccountHistSelectorView.isViewConstructed()) {
            auxAccountHistSelectorView.refreshTransactionTable();
        }
    }

    public void refreshCustomerHistoryAppSettings() {
        if (customerHistory != null && customerHistory.isViewConstructed()) {
            customerHistory.refreshCustomAppSettings();
        }
    }

    public void refreshCustomerAccountTransactionViewTable() {
        if (customerAccTransactions != null && customerAccTransactions.isViewConstructed()) {
            customerAccTransactions.getTablePanel().refreshCustomerTransactionTable();
        }
    }
}
