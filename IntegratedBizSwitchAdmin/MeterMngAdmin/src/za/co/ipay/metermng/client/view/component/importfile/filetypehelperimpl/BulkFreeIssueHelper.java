package za.co.ipay.metermng.client.view.component.importfile.filetypehelperimpl;

import java.util.Map;

import com.google.gwt.cell.client.Column;
import com.google.gwt.user.cellview.client.CellTable;

import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.BulkFreeIssueDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemBaseDialogueBox;
import za.co.ipay.metermng.client.view.component.importfile.ImportFileItemView;
import za.co.ipay.metermng.shared.dto.importfile.ImportFileItemDto;

/**
 * Client-side helper for bulk free issue import functionality
 */
public class BulkFreeIssueHelper extends BaseFiletypeHelper {
    
    public BulkFreeIssueHelper(ClientFactory clientfactory, ImportFileItemView parent) {
        super(clientfactory, parent);
    }
    
    @Override
    public ImportFileItemBaseDialogueBox setImportFileItemBaseDialogueBox() {
        return new BulkFreeIssueDialogueBox(clientFactory, parent);
    }

    @Override
    public String getMeterColumnValue(ImportFileItemDto object) {
        return object.getBulkFreeIssueImportRecord().getMeterNumber();
    }
    
    @SuppressWarnings({ "unchecked", "rawtypes" })
    @Override
    public void addCustomColumnsToTable(CellTable<ImportFileItemDto> table, Map<String, Column> tableColumnMap) {
        table.addColumn(tableColumnMap.get("meterCol"), messagesInstance.getMessage("import.meter.label"));
    }
    
    @Override
    public String getWaitingText() {
        return messagesInstance.getMessage("import.upload.twirly.waiting.text.bulk.free.issue");   
    }
}
