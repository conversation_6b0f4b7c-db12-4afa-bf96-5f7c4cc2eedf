package za.co.ipay.metermng.client.view.component.tariff.calendar;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.BlurEvent;
import com.google.gwt.event.dom.client.BlurHandler;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.FocusEvent;
import com.google.gwt.event.dom.client.FocusHandler;
import com.google.gwt.event.logical.shared.SelectionEvent;
import com.google.gwt.event.logical.shared.SelectionHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.Grid;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.SuggestBox;
import com.google.gwt.user.client.ui.SuggestOracle;
import com.google.gwt.user.client.ui.SuggestOracle.Suggestion;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.handler.FormDataValueChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.Workspace;
import za.co.ipay.metermng.client.event.DayProfileUpdatedEvent;
import za.co.ipay.metermng.client.event.DayProfileUpdatedEventHandler;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.mybatis.generated.model.TouCalendarSeason;
import za.co.ipay.metermng.mybatis.generated.model.TouDayProfile;
import za.co.ipay.metermng.shared.DayProfileSuggestion;
import za.co.ipay.metermng.shared.DayProfileSuggestionsOracle;
import za.co.ipay.metermng.shared.TouCalendarData;
import za.co.ipay.metermng.shared.TouDayProfileData;
import za.co.ipay.metermng.shared.TouSeasonDateData;

public class AssignDayProfilesPanel extends BaseComponent {

    private HashMap<Long, SuggestBoxes> suggestBoxesBySeason;
    private HashMap<Long, TouCalendarSeason> assignedCalendarSeasonsBySeason  = new HashMap<Long, TouCalendarSeason>();
    private TouCalendarData calendarData = new TouCalendarData();
    @UiField Grid calendarSeasonsGrid;
    @UiField HorizontalPanel buttons;
    @UiField Button btnCancel;
    @UiField Button btnSave;

    protected HasDirtyData hasDirtyData;
    protected boolean readOnly = false;

    private CalendarContainer parentContainer;

    @SuppressWarnings("unused")
    private static Logger logger = Logger.getLogger(AssignDayProfilesPanel.class.getName());

    private static AssignDayProfilesUiBinder uiBinder = GWT.create(AssignDayProfilesUiBinder.class);

    interface AssignDayProfilesUiBinder extends UiBinder<Widget, AssignDayProfilesPanel> {
    }

    public AssignDayProfilesPanel(final ClientFactory clientFactory, CalendarContainer parent) {
        this.clientFactory = clientFactory;
        this.parentContainer = parent;
        initWidget(uiBinder.createAndBindUi(this));
        this.hasDirtyData = ((Workspace) parentContainer).createAndRegisterHasDirtyData();
        clientFactory.getEventBus().addHandler(DayProfileUpdatedEvent.TYPE, new DayProfileUpdatedEventHandler() {

            @Override
            public void processDayProfileUpdatedEvent(DayProfileUpdatedEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        readAndSetTouCalendarData(calendarData);
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
    }

    public void readAndSetTouCalendarData(final TouCalendarData calendarData) {
        this.calendarData = calendarData;
        if (calendarData != null) {
            clientFactory.getCalendarRpc().getDayProfilesByCalendar(calendarData.getId(), new ClientCallback<ArrayList<TouDayProfileData>>() {
                @Override
                public void onSuccess(ArrayList<TouDayProfileData> result) {
                    calendarData.setAssignedDayProfiles(result);
                    setTouCalendarData(calendarData);
                }
            });
        }
    }

    public void setTouCalendarData(final TouCalendarData calendarData) {
        this.calendarData = calendarData;
        if (calendarData != null) {
            ArrayList<TouDayProfileData> result = calendarData.getAssignedDayProfileList();
            Collection<Suggestion> suggs = new ArrayList<Suggestion>(result.size());
            DayProfileSuggestion sug=null;
            for (TouDayProfileData thedata:result) {
                sug = new DayProfileSuggestion(calendarData.getId(),thedata.getCode()+"-"+thedata.getName(),thedata.getId(),thedata.getName(), thedata.getCode(),true);
                suggs.add(sug);
            }
            clientFactory.getDayProfileSuggestionsOracle().setDefaultSuggestions(suggs);
        }
        mapDataToForm();
    }

    protected void createGrid() {
        calendarSeasonsGrid.resizeRows(1);
        calendarSeasonsGrid.resizeRows(1+calendarData.getAssignedSeasons().size());
        int i=0;
        Label seasonname;
        SuggestBoxes suggestBoxes;

        DayProfileSuggestionsOracle dayProfileSuggestionsOracle;
        suggestBoxesBySeason = new HashMap<Long, AssignDayProfilesPanel.SuggestBoxes>(calendarData.getAssignedSeasons().size());

        for (TouSeasonDateData s : calendarData.getAssignedSeasons()) {
            i++;
            if (suggestBoxesBySeason.containsKey(s.getSeason().getId())) {
                calendarSeasonsGrid.removeRow(i);
                i--;
                continue;
            }
            seasonname = new Label(s.getSeason().getName());

            seasonname.setStylePrimaryName("dataTitle");
            calendarSeasonsGrid.setWidget(i, 0, seasonname);
            dayProfileSuggestionsOracle = clientFactory.getDayProfileSuggestionsOracle();
            dayProfileSuggestionsOracle.setCalendarId(calendarData.getId());
            dayProfileSuggestionsOracle.setSeasonId(s.getSeason().getId());
            suggestBoxes = new SuggestBoxes(dayProfileSuggestionsOracle);
            calendarSeasonsGrid.setWidget(i, 1, suggestBoxes.suggestBox1);
            calendarSeasonsGrid.setWidget(i, 2, suggestBoxes.suggestBox2);
            calendarSeasonsGrid.setWidget(i, 3, suggestBoxes.suggestBox3);
            calendarSeasonsGrid.setWidget(i, 4, suggestBoxes.suggestBox4);
            calendarSeasonsGrid.setWidget(i, 5, suggestBoxes.suggestBox5);
            calendarSeasonsGrid.setWidget(i, 6, suggestBoxes.suggestBox6);
            calendarSeasonsGrid.setWidget(i, 7, suggestBoxes.suggestBox7);
            suggestBoxesBySeason.put(s.getSeason().getId(), suggestBoxes);
        }

    }

    protected void mapDataToForm() {
        assignedCalendarSeasonsBySeason.clear();
        if (calendarData != null) {
            createGrid();

            clientFactory.getCalendarRpc().getCalendarSeasons(calendarData.getId(), new  ClientCallback<ArrayList<TouCalendarSeason>>() {

                @Override
                public void onSuccess(ArrayList<TouCalendarSeason> result) {
                    for(TouCalendarSeason cs:result) {
                        assignedCalendarSeasonsBySeason.put(cs.getTouSeasonId(), cs);
                    }
                    SuggestBoxes suggestBoxes;
                    TouCalendarSeason calendarSeason;
                    TouDayProfile dayProfile;
                    boolean complete = true;

                    if (calendarData.getAssignedSeasons()==null || calendarData.getAssignedSeasons().isEmpty()) {
                        parentContainer.setAssignDayProfilesComplete(false);
                    }

                    for (TouSeasonDateData s : calendarData.getAssignedSeasons()) {
                        calendarSeason = assignedCalendarSeasonsBySeason.get(s.getTouSeasonId());
                        suggestBoxes = suggestBoxesBySeason.get(s.getTouSeasonId());

                        if (calendarSeason != null && suggestBoxes != null) {
                            dayProfile = calendarData.getAssignedDayProfile(calendarSeason.getTouDayProfileId1());
                            if (dayProfile != null) {
                                suggestBoxes.updateSuggestBox1(dayProfile);
                            }
                            dayProfile = calendarData.getAssignedDayProfile(calendarSeason.getTouDayProfileId2());
                            if (dayProfile != null) {
                                suggestBoxes.updateSuggestBox2(dayProfile);
                            }
                            dayProfile = calendarData.getAssignedDayProfile(calendarSeason.getTouDayProfileId3());
                            if (dayProfile != null) {
                                suggestBoxes.updateSuggestBox3(dayProfile);
                            }
                            dayProfile = calendarData.getAssignedDayProfile(calendarSeason.getTouDayProfileId4());
                            if (dayProfile != null) {
                                suggestBoxes.updateSuggestBox4(dayProfile);
                            }
                            dayProfile = calendarData.getAssignedDayProfile(calendarSeason.getTouDayProfileId5());
                            if (dayProfile != null) {
                                suggestBoxes.updateSuggestBox5(dayProfile);
                            }
                            dayProfile = calendarData.getAssignedDayProfile(calendarSeason.getTouDayProfileId6());
                            if (dayProfile != null) {
                                suggestBoxes.updateSuggestBox6(dayProfile);
                            }
                            dayProfile = calendarData.getAssignedDayProfile(calendarSeason.getTouDayProfileId7());
                            if (dayProfile != null) {
                                suggestBoxes.updateSuggestBox7(dayProfile);
                            }
                        }
                        if (!suggestBoxes.isComplete()) {
                            complete = false;
                        }

                        parentContainer.setAssignDayProfilesComplete(complete);
                    }
                }
            });
        } else {
            calendarSeasonsGrid.resizeRows(1);
            if (suggestBoxesBySeason != null) {
                suggestBoxesBySeason.clear();
            }
        }
    }

    protected void mapFormToData() {
        SuggestBoxes suggestBoxes;
        TouCalendarSeason calendarSeason;
        if (calendarData != null) {
            if (calendarData.getAssignedSeasons() != null) {
                for (TouSeasonDateData s:calendarData.getAssignedSeasons()) {
                    suggestBoxes = suggestBoxesBySeason.get(s.getTouSeasonId());
                    calendarSeason = assignedCalendarSeasonsBySeason.get(s.getTouSeasonId());
                    if (calendarSeason == null) {
                        calendarSeason = new TouCalendarSeason();
                        calendarSeason.setTouCalendarId(calendarData.getId());
                        calendarSeason.setTouSeasonId(s.getTouSeasonId());
                    }
                    //if (suggestBoxes.selected1 != null)
                        calendarSeason.setTouDayProfileId1(suggestBoxes.selected1);
                    //if (suggestBoxes.selected2 != null)
                        calendarSeason.setTouDayProfileId2(suggestBoxes.selected2);
                    //if (suggestBoxes.selected3 != null)
                        calendarSeason.setTouDayProfileId3(suggestBoxes.selected3);
                    //if (suggestBoxes.selected4 != null)
                        calendarSeason.setTouDayProfileId4(suggestBoxes.selected4);
                    //if (suggestBoxes.selected5 != null)
                        calendarSeason.setTouDayProfileId5(suggestBoxes.selected5);
                    //if (suggestBoxes.selected6 != null)
                        calendarSeason.setTouDayProfileId6(suggestBoxes.selected6);
                    //if (suggestBoxes.selected7 != null)
                        calendarSeason.setTouDayProfileId7(suggestBoxes.selected7);

                    assignedCalendarSeasonsBySeason.put(s.getTouSeasonId(), calendarSeason);
                }
            }
        }

    }

    @UiHandler("btnSave")
    void handleSaveButton(ClickEvent event) {
        // Map form fields to data object
        mapFormToData();
        if (validate()) {
            SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                @Override
                public void callback(SessionCheckResolution resolution) {
                    if (clientFactory != null) {
                        clientFactory.getCalendarRpc().updateCalendarSeasons(new ArrayList<TouCalendarSeason>(assignedCalendarSeasonsBySeason.values()),  new  ClientCallback<Boolean>() {

                            @Override
                            public void onSuccess(Boolean result) {
                                if (result) {
                                    hasDirtyData.setDirtyData(false);
                                    Dialogs.displayInformationMessage(
                                            MessagesUtil.getInstance().getMessage("calendar.assign.dayprofile.saved"),
                                            MediaResourceUtil.getInstance().getInformationIcon(),
                                            btnSave.getAbsoluteLeft(), btnSave.getAbsoluteTop(),null);
                                } else {
                                    Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("calendar.assign.dayprofile.error.save"), MediaResourceUtil.getInstance().getErrorIcon(), btnSave.getAbsoluteLeft()+btnSave.getOffsetWidth(), btnSave.getAbsoluteTop()-btnSave.getOffsetHeight(), null);
                                }
                                mapDataToForm();
                            }
                        });
                    }
                }
            };
            clientFactory.handleSessionCheckCallback(sessionCheckCallback);
        }
    }

    @UiHandler("btnCancel")
    void handleClearButton(ClickEvent event) {
        if(hasDirtyData.isDirtyData()) {
            Dialogs.confirmDiscardChanges(new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        hasDirtyData.setDirtyData(false);
                        mapDataToForm();
                        //Map form fields to data object
                        Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("calendar.assign.dayprofile.cleared"), MediaResourceUtil.getInstance().getInformationIcon(), btnCancel.getAbsoluteLeft()+btnCancel.getOffsetWidth(), btnCancel.getAbsoluteTop()-btnCancel.getOffsetHeight(), null);
                    }
                }});
        } else {
            mapDataToForm();
        }
    }
    public ArrayList<TouCalendarSeason> getCalendarSeasons() {
        mapFormToData();
        ArrayList<TouCalendarSeason> cslist = new ArrayList<TouCalendarSeason>(assignedCalendarSeasonsBySeason.size());
        if (assignedCalendarSeasonsBySeason != null) {
            cslist.addAll(assignedCalendarSeasonsBySeason.values());
        }
        return cslist;
    }

    public boolean validate() {
        boolean complete = true; //FIXME: We need true validation implementation here!
        return complete;
    }

    private class SuggestBoxes {
        private SuggestBox suggestBox1;
        private SuggestBox suggestBox2;
        private SuggestBox suggestBox3;
        private SuggestBox suggestBox4;
        private SuggestBox suggestBox5;
        private SuggestBox suggestBox6;
        private SuggestBox suggestBox7;

        private Long selected1;
        private Long selected2;
        private Long selected3;
        private Long selected4;
        private Long selected5;
        private Long selected6;
        private Long selected7;

        public SuggestBoxes(DayProfileSuggestionsOracle dayProfileSuggestionsOracle) {
            suggestBox1 = new SuggestBox(dayProfileSuggestionsOracle);
            suggestBox1.setWidth("3em");
            suggestBox1.setAutoSelectEnabled(true);
            suggestBox1.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
            suggestBox1.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> event) {
                    Suggestion selectedItem = event.getSelectedItem();
                    //cast returned suggestion
                    selected1 = ((DayProfileSuggestion)selectedItem).getId();
                    // FIXME not sure why the value change handler not picking up selections from
                    // mouse click, only picks up text inputted
                    hasDirtyData.setDirtyData(true);
                }
            });
            suggestBox1.getValueBox().addFocusHandler(new FocusHandler() {

                @Override
                public void onFocus(FocusEvent event) {
                    suggestBox1.showSuggestionList();
                }
            });
            suggestBox1.getValueBox().addBlurHandler(new BlurHandler() {

                @Override
                public void onBlur(BlurEvent event) {
                    if (selected1==null && suggestBox1.getText() != null && !suggestBox1.getText().isEmpty()) {
                        suggestBox1.setText("");
                        suggestBox1.setFocus(true);
                    }
                    if (suggestBox1.getText().isEmpty()) {
                        selected1 = null;
                    }

                }
            });

            suggestBox2 = new SuggestBox(dayProfileSuggestionsOracle);
            suggestBox2.setWidth("3em");
            suggestBox2.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
            suggestBox2.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> event) {
                    Suggestion selectedItem = event.getSelectedItem();
                    //cast returned suggestion
                    selected2 = ((DayProfileSuggestion)selectedItem).getId();
                    // FIXME not sure why the value change handler not picking up selections from
                    // mouse click, only picks up text inputted
                    hasDirtyData.setDirtyData(true);
                }
            });
            suggestBox2.getValueBox().addFocusHandler(new FocusHandler() {

                @Override
                public void onFocus(FocusEvent event) {
                    suggestBox2.showSuggestionList();
                }
            });
            suggestBox2.getValueBox().addBlurHandler(new BlurHandler() {

                @Override
                public void onBlur(BlurEvent event) {
                    if (selected2==null && suggestBox2.getText() != null && !suggestBox2.getText().isEmpty()) {
                        suggestBox2.setText("");
                        suggestBox2.setFocus(true);
                    }
                    if (suggestBox2.getText().isEmpty()) {
                        selected2 = null;
                    }

                }
            });
            suggestBox3 = new SuggestBox(dayProfileSuggestionsOracle);
            suggestBox3.setWidth("3em");
            suggestBox3.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
            suggestBox3.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> event) {
                    Suggestion selectedItem = event.getSelectedItem();
                    //cast returned suggestion
                    selected3 = ((DayProfileSuggestion)selectedItem).getId();
                    // FIXME not sure why the value change handler not picking up selections from
                    // mouse click, only picks up text inputted
                    hasDirtyData.setDirtyData(true);
                }
            });
            suggestBox3.getValueBox().addFocusHandler(new FocusHandler() {

                @Override
                public void onFocus(FocusEvent event) {
                    suggestBox3.showSuggestionList();
                }
            });
            suggestBox3.getValueBox().addBlurHandler(new BlurHandler() {

                @Override
                public void onBlur(BlurEvent event) {
                    if (selected3==null && suggestBox3.getText() != null && !suggestBox3.getText().isEmpty()) {
                        suggestBox3.setText("");
                        suggestBox3.setFocus(true);
                    }
                    if (suggestBox3.getText().isEmpty()) {
                        selected3 = null;
                    }
                }
            });
            suggestBox4 = new SuggestBox(dayProfileSuggestionsOracle);
            suggestBox4.setWidth("3em");
            suggestBox4.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
            suggestBox4.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> event) {
                    Suggestion selectedItem = event.getSelectedItem();
                    //cast returned suggestion
                    selected4 = ((DayProfileSuggestion)selectedItem).getId();
                    // FIXME not sure why the value change handler not picking up selections from
                    // mouse click, only picks up text inputted
                    hasDirtyData.setDirtyData(true);
                }
            });
            suggestBox4.getValueBox().addFocusHandler(new FocusHandler() {

                @Override
                public void onFocus(FocusEvent event) {
                    suggestBox4.showSuggestionList();
                }
            });
            suggestBox4.getValueBox().addBlurHandler(new BlurHandler() {

                @Override
                public void onBlur(BlurEvent event) {
                    if (selected4==null && suggestBox4.getText() != null && !suggestBox4.getText().isEmpty()) {
                        suggestBox4.setText("");
                        suggestBox4.setFocus(true);
                    }
                    if (suggestBox4.getText().isEmpty()) {
                        selected4 = null;
                    }
                }
            });
            suggestBox5 = new SuggestBox(dayProfileSuggestionsOracle);
            suggestBox5.setWidth("3em");
            suggestBox5.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
            suggestBox5.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> event) {
                    Suggestion selectedItem = event.getSelectedItem();
                    //cast returned suggestion
                    selected5 = ((DayProfileSuggestion)selectedItem).getId();
                    // FIXME not sure why the value change handler not picking up selections from
                    // mouse click, only picks up text inputted
                    hasDirtyData.setDirtyData(true);
                }
            });
            suggestBox5.getValueBox().addFocusHandler(new FocusHandler() {

                @Override
                public void onFocus(FocusEvent event) {
                    suggestBox5.showSuggestionList();
                }
            });
            suggestBox5.getValueBox().addBlurHandler(new BlurHandler() {

                @Override
                public void onBlur(BlurEvent event) {
                    if (selected5==null && suggestBox5.getText() != null && !suggestBox5.getText().isEmpty()) {
                        suggestBox5.setText("");
                        suggestBox5.setFocus(true);
                    }
                    if (suggestBox5.getText().isEmpty()) {
                        selected5 = null;
                    }
                }
            });

            suggestBox6 = new SuggestBox(dayProfileSuggestionsOracle);
            suggestBox6.setWidth("3em");
            suggestBox6.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
            suggestBox6.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> event) {
                    Suggestion selectedItem = event.getSelectedItem();
                    //cast returned suggestion
                    selected6 = ((DayProfileSuggestion)selectedItem).getId();
                    // FIXME not sure why the value change handler not picking up selections from
                    // mouse click, only picks up text inputted
                    hasDirtyData.setDirtyData(true);
                }
            });
            suggestBox6.getValueBox().addFocusHandler(new FocusHandler() {

                @Override
                public void onFocus(FocusEvent event) {
                    suggestBox6.showSuggestionList();
                }
            });
            suggestBox6.getValueBox().addBlurHandler(new BlurHandler() {

                @Override
                public void onBlur(BlurEvent event) {
                    if (selected6==null && suggestBox6.getText() != null && !suggestBox6.getText().isEmpty()) {
                        suggestBox6.setText("");
                        suggestBox6.setFocus(true);
                    }
                    if (suggestBox6.getText().isEmpty()) {
                        selected6 = null;
                    }
                }
            });
            suggestBox7 = new SuggestBox(dayProfileSuggestionsOracle);
            suggestBox7.setWidth("3em");
            suggestBox7.addValueChangeHandler(new FormDataValueChangeHandler<String>(hasDirtyData));
            suggestBox7.addSelectionHandler(new SelectionHandler<SuggestOracle.Suggestion>() {
                @Override
                public void onSelection(SelectionEvent<Suggestion> event) {
                    Suggestion selectedItem = event.getSelectedItem();
                    //cast returned suggestion
                    selected7 = ((DayProfileSuggestion)selectedItem).getId();
                    // FIXME not sure why the value change handler not picking up selections from
                    // mouse click, only picks up text inputted
                    hasDirtyData.setDirtyData(true);
                }
            });
            suggestBox7.getValueBox().addFocusHandler(new FocusHandler() {

                @Override
                public void onFocus(FocusEvent event) {
                    suggestBox7.showSuggestionList();
                }
            });
            suggestBox7.getValueBox().addBlurHandler(new BlurHandler() {

                @Override
                public void onBlur(BlurEvent event) {
                    if (selected7==null && suggestBox7.getText() != null && !suggestBox7.getText().isEmpty()) {
                        suggestBox7.setText("");
                        suggestBox7.setFocus(true);
                    }
                    if (suggestBox7.getText().isEmpty()) {
                        selected7 = null;
                    }
                }
            });


        }

        public boolean isComplete() {
            return (selected1 != null && selected2 != null && selected3 != null
                    && selected4 != null && selected5 != null && selected6 != null
                    && selected7 != null);
        }

        public void updateSuggestBox1(TouDayProfile dayProfile) {
            suggestBox1.setText(dayProfile.getCode());
            suggestBox1.setTitle(dayProfile.getName());
            selected1 = dayProfile.getId();
        }

        public void updateSuggestBox2(TouDayProfile dayProfile) {
            suggestBox2.setText(dayProfile.getCode());
            suggestBox2.setTitle(dayProfile.getName());
            selected2 = dayProfile.getId();
        }

        public void updateSuggestBox3(TouDayProfile dayProfile) {
            suggestBox3.setText(dayProfile.getCode());
            suggestBox3.setTitle(dayProfile.getName());
            selected3 = dayProfile.getId();
        }

        public void updateSuggestBox4(TouDayProfile dayProfile) {
            suggestBox4.setText(dayProfile.getCode());
            suggestBox4.setTitle(dayProfile.getName());
            selected4 = dayProfile.getId();
        }

        public void updateSuggestBox5(TouDayProfile dayProfile) {
            suggestBox5.setText(dayProfile.getCode());
            suggestBox5.setTitle(dayProfile.getName());
            selected5 = dayProfile.getId();
        }

        public void updateSuggestBox6(TouDayProfile dayProfile) {
            suggestBox6.setText(dayProfile.getCode());
            suggestBox6.setTitle(dayProfile.getName());
            selected6 = dayProfile.getId();
        }

        public void updateSuggestBox7(TouDayProfile dayProfile) {
            suggestBox7.setText(dayProfile.getCode());
            suggestBox7.setTitle(dayProfile.getName());
            selected7 = dayProfile.getId();
        }

        public void setReadOnly(boolean readOnly) {
            suggestBox1.setEnabled(!readOnly);
            suggestBox2.setEnabled(!readOnly);
            suggestBox3.setEnabled(!readOnly);
            suggestBox4.setEnabled(!readOnly);
            suggestBox5.setEnabled(!readOnly);
            suggestBox6.setEnabled(!readOnly);
            suggestBox7.setEnabled(!readOnly);
        }

    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
        SuggestBoxes suggestBoxes;
        for (TouSeasonDateData s : calendarData.getAssignedSeasons()) {
            suggestBoxes = suggestBoxesBySeason.get(s.getTouSeasonId());
            if (suggestBoxes != null) {
                suggestBoxes.setReadOnly(readOnly);
            }
        }
        buttons.setVisible(!readOnly);
    }

}
