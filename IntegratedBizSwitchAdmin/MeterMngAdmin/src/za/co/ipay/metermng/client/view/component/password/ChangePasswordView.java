package za.co.ipay.metermng.client.view.component.password;

import java.util.logging.Logger;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.resource.StyleNames;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleFormView;
import za.co.ipay.metermng.client.event.SelectAccessGroupEvent;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.i18n.UiMessagesUtil;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.shared.dto.user.MeterMngUser;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Image;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.Widget;

public class ChangePasswordView extends BaseComponent {

    private PopupPanel parent;
    @UiField SimpleFormView view;
    private ChangePasswordPanel panel;
    private boolean forceChange = false;
    private boolean changedPassword = false;
    private MeterMngUser user;

    private static Logger logger = Logger.getLogger(ChangePasswordView.class.getName());

    private static ChangePasswordViewUiBinder uiBinder = GWT.create(ChangePasswordViewUiBinder.class);

    interface ChangePasswordViewUiBinder extends UiBinder<Widget, ChangePasswordView> {
    }

    public ChangePasswordView(ClientFactory clientFactory, PopupPanel popupPanel) {
        setClientFactory(clientFactory);
        parent = popupPanel;
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void initUi() {
        initForm();
    }

    private void initForm() {
        panel = new ChangePasswordPanel(view.getForm());
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.save"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        onChangePassword();
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("savePasswordBtn");

        view.getForm().getOtherBtn().setVisible(false);
        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                onCancel();
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("cancelPasswordButton");

        view.getForm().getBackBtn().setVisible(true);
        view.getForm().getBackBtn().setText(UiMessagesUtil.getInstance().getLogoutLink());
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                Window.Location.assign("j_spring_security_logout");
            }
        });
        view.getForm().getBackBtn().ensureDebugId("logoutPassword");
    }

    public void displayCurrentUser(boolean forceChange) {
        logger.info("displayCurrentUser: "+forceChange+" clientFactory:"+clientFactory);
        this.forceChange = forceChange;

        final PopupPanel waiting = Dialogs.displayWaitDialog(MediaResourceUtil.getInstance().getWaitIcon());

        if (!forceChange) {
            view.getForm().getOtherBtn().setVisible(true);
            view.getForm().getBackBtn().setVisible(false);
        }

        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getUserRpc().getCurrentUser(new ClientCallback<MeterMngUser>() {
                    @Override
                    public void onSuccess(MeterMngUser result) {
                        user = result;
                        waiting.hide(true);
                        displayUser();
                    }
                });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    public void displayUser(MeterMngUser user, boolean forceChange) {
        logger.info("displayUser: "+user+" "+forceChange);
        this.forceChange = forceChange;
        this.user = user;
        if (!forceChange) {
            view.getForm().getOtherBtn().setVisible(true);
            view.getForm().getBackBtn().setVisible(false);
        }
        displayUser();
    }

    private void displayUser() {
        view.getForm().getBackBtn().setText(UiMessagesUtil.getInstance().getLogoutLink()+" "+user.getUserName());
        if (user.isLdapAuth()) {
            Dialogs.displayMessage(MessagesUtil.getInstance().getMessage("password.ldap"),
                                   new Image(MediaResourceUtil.getInstance().getErrorIcon()),
                                   StyleNames.POPUP_MESSAGE_ERROR,
                                   null, null,
                                   MessagesUtil.getInstance().getMessage("button.ok"),
                                   new ClickHandler() {
                                        @Override
                                        public void onClick(ClickEvent event) {
                                            parent.hide();
                                   }});
        } else {
            if (user.isPasswordExpired()) {
                panel.instructions.setText(MessagesUtil.getInstance().getMessage("password.login.expired"));
                panel.instructions.setVisible(true);
            } else if (user.isPasswordRequiresReset()) {
                panel.instructions.setText(MessagesUtil.getInstance().getMessage("password.login.reset"));
                panel.instructions.setVisible(true);
            }
        }
    }

    private void onCancel() {
        if (forceChange && !changedPassword) {
            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("password.user.change"), MediaResourceUtil.getInstance().getErrorIcon());
        } else {
            parent.hide();
            user = null;
            view.getForm().getOtherBtn().setVisible(false);
            view.getForm().getBackBtn().setText(UiMessagesUtil.getInstance().getLogoutLink());
            panel.clearErrors();
            panel.clearFields();
        }
    }

    private boolean isValidInput() {
        boolean valid = true;
        panel.clearErrors();

        String currentPassword = panel.currentPasswordBox.getText();
        String newPassword = panel.newPasswordBox.getText();
        String confirmPassword = panel.confirmPasswordBox.getText();

        if (currentPassword == null || currentPassword.trim().equals("")) {
            valid = false;
            panel.currentPasswordElement.setErrorMsg(MessagesUtil.getInstance().getMessage("password.old.required"));
        }
        if (newPassword == null || newPassword.trim().equals("")) {
            valid = false;
            panel.newPasswordElement.setErrorMsg(MessagesUtil.getInstance().getMessage("password.new.required"));
        }
        if (confirmPassword == null || confirmPassword.trim().equals("")) {
            valid = false;
            panel.confirmPasswordElement.setErrorMsg(MessagesUtil.getInstance().getMessage("password.confirm.required"));
        } else if (!confirmPassword.equals(newPassword)) {
            valid = false;
            panel.newPasswordElement.setErrorMsg(MessagesUtil.getInstance().getMessage("password.new.nonmatching"));
        }

        return valid;
    }

    private void onChangePassword() {
        if (isValidInput()) {
            clientFactory.getUserRpc().updateCurrentUserPassword(
                    Long.valueOf(user.getId()),
                    panel.currentPasswordBox.getText(),
                    panel.newPasswordBox.getText(),
                    new ClientCallback<MeterMngUser>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                        @Override
                        public void onSuccess(MeterMngUser result) {
                            clientFactory.setUser(user);
                            changedPassword = true;
                            parent.hide(true);
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("password.changed",
                                    new String[]{user.getUserName()}),
                                                              MediaResourceUtil.getInstance().getInformationIcon());
                            panel.clearErrors();
                            panel.clearFields();
                            if (clientFactory.getUser().isChooseCurrentGroupleafNode()) {
                                clientFactory.getEventBus().fireEvent(new SelectAccessGroupEvent());
                            }
                        }
                    });
        }
    }
}
