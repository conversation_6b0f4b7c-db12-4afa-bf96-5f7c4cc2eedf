package za.co.ipay.metermng.client.view.component.tariff;

import java.util.ArrayList;
import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortList.ColumnSortInfo;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.datatypes.RecordStatus;
import za.co.ipay.metermng.shared.dto.pricing.PricingStructureDto;

public class PricingStructureFilterPanel extends BaseComponent {

    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField ListBox statusFilterDropdown;
    @UiField ListBox serviceResourceFilterDropdown;
    @UiField ListBox meterTypeFilterDropdown;
    @UiField ListBox paymentModeFilterDropdown;

    private int filterTableColIndx = 0;

	private String filterColumnName;
    private String filterString = "";

    private PricingStructureView pView;

    private static Logger logger = Logger.getLogger(PricingStructureFilterPanel.class.getName());
    private static PricingStructureFilterPanelUiBinder uiBinder = GWT.create(PricingStructureFilterPanelUiBinder.class);

    interface PricingStructureFilterPanelUiBinder extends UiBinder<Widget, PricingStructureFilterPanel> {
    }

    public PricingStructureFilterPanel(ClientFactory clientFactory, PricingStructureView pView) {
        this.clientFactory = clientFactory;
        this.pView = pView;
        initWidget(uiBinder.createAndBindUi(this));
    }

    //*******************************************************************************************************

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent event) {

        if (filterDropdown.getSelectedIndex() < 1 ) {
            filterColumnName = null;
            filterString = "";
            filterTableColIndx = -1;
            changeFilter(0);
        } else {
            filterString = txtbxfilter.getText().trim();
            changeFilter(filterTableColIndx);
        }

        if (filterString.equals("")) {
        	pView.getPricingStructuresCount(false);
        }
    }

    @UiHandler("statusFilterDropdown")
    void handleStatusFilterChange(ChangeEvent changeEvent) {
        int sfDropdownSelection = statusFilterDropdown.getSelectedIndex();

        RecordStatus recordStatus;

        switch(sfDropdownSelection) {
        case 1:
        	recordStatus = RecordStatus.ACT;
        	filterString = recordStatus.toString();
        	break;
        case 2:
        	recordStatus = RecordStatus.DAC;
        	filterString = recordStatus.toString();
        	break;
        default:
        	filterString = "";
        	break;
        }

        changeFilter(2);

        if (filterString.equals("")) {
        	pView.getPricingStructuresCount(false);
        }
    }

    @UiHandler("serviceResourceFilterDropdown")
    void handleServiceResourceFilterChange(ChangeEvent changeEvent) {
        int selectionIndex = serviceResourceFilterDropdown.getSelectedIndex();
        if(selectionIndex > 0) {
            filterString = serviceResourceFilterDropdown.getValue(selectionIndex);
        } else {
            filterString = "";
        }
        changeFilter(4);
        if (filterString.equals("")) {
            pView.getPricingStructuresCount(false);
        }
    }

    @UiHandler("meterTypeFilterDropdown")
    void handleMeterTypeFilterChange(ChangeEvent changeEvent) {
        int selectionIndex = meterTypeFilterDropdown.getSelectedIndex();
        if(selectionIndex > 0) {
            filterString = meterTypeFilterDropdown.getValue(selectionIndex);
        } else {
            filterString = "";
        }
        changeFilter(5);
        if (filterString.equals("")) {
            pView.getPricingStructuresCount(false);
        }
    }

    @UiHandler("paymentModeFilterDropdown")
    void handlePaymentModeFilterChange(ChangeEvent changeEvent) {
        int selectionIndex = paymentModeFilterDropdown.getSelectedIndex();
        if(selectionIndex > 0) {
            filterString = paymentModeFilterDropdown.getValue(selectionIndex);
        } else {
            filterString = "";
        }
        changeFilter(6);
        if (filterString.equals("")) {
            pView.getPricingStructuresCount(false);
        }
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
        filterColumnName = null;
        clearFilter();
        txtbxfilter.setVisible(true);
        statusFilterDropdown.setVisible(false);
        serviceResourceFilterDropdown.setVisible(false);
        meterTypeFilterDropdown.setVisible(false);
        paymentModeFilterDropdown.setVisible(false);

        //If have cleared the filter dropdown, leave filterColumnName null and reset the filtering
        if (filterDropdown.getSelectedIndex() < 1) {
            filterTableColIndx = 0;
            changeFilter(0);
            return;
        }

        //establish WHICH column to filter on; using db field names to pass to group select
        if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("pricingstructure.field.name"))) {
            filterTableColIndx = 0;
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("pricingstructure.field.description"))) {
            filterTableColIndx = 1;
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("pricingstructure.field.status"))) {
        	txtbxfilter.setVisible(false);
            statusFilterDropdown.setVisible(true);
        	filterTableColIndx = 2;
        	//skip tariff column == 3
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("ptr.serviceresource"))) {
        	txtbxfilter.setVisible(false);
            serviceResourceFilterDropdown.setVisible(true);
        	filterTableColIndx = 4;
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("ptr.metertype"))) {
        	txtbxfilter.setVisible(false);
            meterTypeFilterDropdown.setVisible(true);
        	filterTableColIndx = 5;
        } else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("ptr.paymentmode"))) {
        	txtbxfilter.setVisible(false);
            paymentModeFilterDropdown.setVisible(true);
        	filterTableColIndx = 6;
        } else {
            return;
        }

        //Only set filterColumnName if there WAS actually a selection.
        filterColumnName=pView.getColumnName(filterTableColIndx);
    }

    private void changeFilter(int tableColIndx) {
        final int start = 0;      //table.getVisibleRange().getStart();
        Integer colIndxInSortList = findColIndxInSortList(tableColIndx);

        if (colIndxInSortList != null) {
            ColumnSortInfo colSortInfo = pView.getView().getTable().getColumnSortList().get(colIndxInSortList);
            pView.getView().getTable().getColumnSortList().push(colSortInfo);
        } else {
        	pView.getView().getTable().getColumnSortList().push(pView.getView().getTable().getColumn(tableColIndx));
        }
        // Set the range to display
        pView.getView().getTable().setVisibleRange(0, getPageSize());
        getTableData(start, tableColIndx, pView.getView().getTable().getColumnSortList().get(0).isAscending());
    }

    private Integer findColIndxInSortList(int tableColIndx) {
        for (int i=0; i< pView.getView().getTable().getColumnSortList().size(); i++) {
            @SuppressWarnings("unchecked")
            Column<PricingStructureDto, ?> sColumn = (Column<PricingStructureDto, ?>) pView.getView().getTable().getColumnSortList().get(i).getColumn();
            if (pView.getView().getTable().getColumnIndex(sColumn) == tableColIndx) {
                return i;
            }
        }
        return null;
    }

    private void getTableData(final int start, int tableColIndex, final boolean isAscending) {

    	final String sortColumnName = pView.getColumnName(tableColIndex);
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getPricingStructureRpc().getPricingStructures(start, getPageSize(), sortColumnName, filterTableColIndx, filterString, isAscending, null,
                        new ClientCallback<ArrayList<PricingStructureDto>>() {
                            @Override
                            public void onSuccess(ArrayList<PricingStructureDto> result) {

                                pView.getView().getTable().redraw();
                                pView.getView().getTable().setRowCount(0);

                                pView.getPricingStructuresCount(false);

                                pView.getDataProvider().updateRowData(start, result);

                                logger.info("Set dataProvider data: start:"+start +" size:"+result.size());
                                //no results
                                if (result.size() == 0) {
                                    Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("meter.online.bulk.search.no.results"),
                                            MediaResourceUtil.getInstance().getInformationIcon());
                                }
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    //*******************************************************************************************************

    /**
	 * @return the filterColumnName
	 */
	public String getFilterColumnName() {
		return this.filterColumnName;
	}

	/**
	 * @return the filterString
	 */
	public String getFilterString() {
		return this.filterString;
	}

	/**
	 * @return the filterTableColIndx
	 */
	public int getFilterTableColIndx() {
		return this.filterTableColIndx;
	}

	public void clearFilter() {
		this.txtbxfilter.setText("");
		this.filterString = "";
		statusFilterDropdown.setItemSelected(0, true);
		serviceResourceFilterDropdown.setItemSelected(0, true);
		meterTypeFilterDropdown.setItemSelected(0, true);
		paymentModeFilterDropdown.setItemSelected(0, true);
	}
}
