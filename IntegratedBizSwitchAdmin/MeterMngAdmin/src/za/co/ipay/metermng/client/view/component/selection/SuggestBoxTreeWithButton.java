package za.co.ipay.metermng.client.view.component.selection;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.PopupPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyDataManager;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.DirtyDataManagerAwarePopup;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.group.upgengrouptree.UpGenGroupTreePanel;
import za.co.ipay.metermng.client.view.component.onlinebulk.GroupTypeTreeInfo;
import za.co.ipay.metermng.client.widget.suggestboxtree.SuggestBoxTree;
import za.co.ipay.metermng.shared.dto.SelectionDataItem;

public class SuggestBoxTreeWithButton extends BaseComponent  {
    @UiField(provided=true) SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt;
    @UiField HTMLPanel buttonsPanel;
    @UiField Button addNewGroupBtn;

    private static Logger logger = Logger.getLogger(SuggestBoxTreeWithButton.class.getName());

    private static SuggestBoxTreeWithButtonUiBinder uiBinder = GWT.create(SuggestBoxTreeWithButtonUiBinder.class);

    interface SuggestBoxTreeWithButtonUiBinder extends UiBinder<Widget, SuggestBoxTreeWithButton> {
    }

    public SuggestBoxTreeWithButton(ClientFactory clientFactory, SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> sbt) {
        this.sbt = sbt;
        this.clientFactory = clientFactory;
        initWidget(uiBinder.createAndBindUi(this));
    }

    @UiHandler("addNewGroupBtn")
    void handleAddNewGroup(ClickEvent event) {
        final HasDirtyDataManager hasDirtyDataManager = new LocalOnlyHasDirtyDataManager();
        final PopupPanel popup = new DirtyDataManagerAwarePopup(hasDirtyDataManager);

        popup.setTitle(MessagesUtil.getInstance().getMessage("meter.online.bulk.add.group.title"));
        popup.setGlassEnabled(true);
        popup.setModal(true);
        popup.setAutoHideEnabled(true);
        popup.setPopupPosition(this.getAbsoluteLeft()+this.getOffsetWidth(), this.getAbsoluteTop());
        logger.info("Loading group type... Id=" + sbt.getTreeInfo().getGroupTypeId());
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                new UpGenGroupTreePanel(clientFactory, sbt.getTreeInfo().getGroupTypeId(), popup, hasDirtyDataManager);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    public SuggestBoxTree<Long, SelectionDataItem, GroupTypeTreeInfo> getSuggestBoxTree() {
        return sbt;
    }

    public void removeButtonPanel() {
        buttonsPanel.removeFromParent();
    }

}
