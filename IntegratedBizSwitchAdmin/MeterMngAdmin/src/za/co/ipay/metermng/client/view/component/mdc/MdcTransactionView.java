package za.co.ipay.metermng.client.view.component.mdc;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.dom.client.KeyUpEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.ColumnSortEvent;
import com.google.gwt.user.cellview.client.ColumnSortEvent.ListHandler;
import com.google.gwt.user.cellview.client.ColumnSortList;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.HorizontalPanel;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.NoSelectionModel;
import com.google.gwt.view.client.SelectionChangeEvent;
import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.factory.ResourcesFactoryUtil;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.Format.FormatUtil;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widgets.IpayListBox;
import za.co.ipay.gwt.common.client.widgets.TablePager;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.component.DateRangeFilterPanel;
import za.co.ipay.metermng.client.view.component.IPayDataProviderWithListOfFilters;
import za.co.ipay.metermng.client.view.component.IpayDataProviderFilter;
import za.co.ipay.metermng.client.view.workspace.UsagePointWorkspaceView;
import za.co.ipay.metermng.datatypes.ServiceResourceE;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.IpayResponseData;
import za.co.ipay.metermng.shared.MdcTransData;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.appsettings.AppSettings;
import za.co.ipay.metermng.shared.appsettings.PowerLimitValue;
import za.co.ipay.metermng.shared.dto.MeterData;
import za.co.ipay.metermng.shared.dto.meter.MeterModelDto;

public class MdcTransactionView extends BaseComponent implements IpayDataProviderFilter<MdcTransData>{
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static MdcTransactionWidgetUiBinder uiBinder = GWT.create(MdcTransactionWidgetUiBinder.class);

    interface MdcTransactionWidgetUiBinder extends UiBinder<Widget, MdcTransactionView> {
    }

    @UiField(provided=true) CellTable<MdcTransData> clltblTransactions;
    @UiField TablePager smplpgrTransactions;
    @UiField TextBox txtbxfilter;
    @UiField ListBox filterDropdown;
    @UiField DateRangeFilterPanel dateFilter;
    @UiField HTML dataName;
    @UiField HTML dataDescription;
    @UiField CheckBox showConnectDisconnect;
    @UiField CheckBox showBalanceMessages;

    @UiField HTMLPanel connectDisconnectPanel;
    @UiField FormElement connectDisconnectElement;
    @UiField IpayListBox lstbxConnectDisconnect;
    @UiField FormRowPanel mdcOverridePanel;
    @UiField IpayListBox lstbxMdcOverride;
    @UiField HorizontalPanel mdcMessageButtons;
    @UiField Button btnSend;
    @UiField Button btnCancel;

    @UiField IpayListBox powerLimitListBox;
    @UiField FormElement powerLimitElement;
    @UiField RadioButton relayRadioMain;
    @UiField RadioButton relayRadioAuxOne;
    @UiField FormElement relaySelectionElement;
    RadioButton relayRadioAuxTwo;

    private TextColumn<MdcTransData> reqReceivedDateColumn;
    private TextColumn<MdcTransData> statusColumn;

    private MeterData meterData;
    private IPayDataProviderWithListOfFilters<MdcTransData> dataProvider = new IPayDataProviderWithListOfFilters<MdcTransData>(this);
    private ArrayList<MdcTransData> theTransactiondata;
    private ListHandler<MdcTransData> columnSortHandler;
    private ColumnSortList columnSortList;
    int l;
    int t;

    private boolean viewConstructed = false;
    private static final String PAN_DISPLAY = "PAN_DISPLAY";

    public MdcTransactionView(ClientFactory clientFactory) {
        setClientFactory(clientFactory);
        createTable();
        initWidget(uiBinder.createAndBindUi(this));
        initUi();
    }

    private void initUi() {
        initView();
        initTable();
        viewConstructed = true;
    }

    public boolean isViewConstructed() {
        return viewConstructed;
    }


    private void initView() {
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("mdc.txn.reqreceived"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("mdc.txn.reqtype"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("mdc.txn.controltype"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("mdc.txn.status"));
        filterDropdown.addItem(MessagesUtil.getInstance().getMessage("mdc.txn.timecompld"));
        dataName.setText(MessagesUtil.getInstance().getMessage("mdc.txn.messages"));
    	dataDescription.setText(MessagesUtil.getInstance().getMessage("mdc.txn.messages.description"));
    	relayRadioAuxTwo = new RadioButton("relayRadioGroup", MessagesUtil.getInstance().getMessage("mdc.txn.relay.aux.two"));
    	relayRadioAuxTwo.ensureDebugId("relayAuxOptionTwo");
    }

    protected void createTable() {
    	 clltblTransactions = new CellTable<MdcTransData>(DEFAULT_PAGE_SIZE, ResourcesFactoryUtil.getInstance().getCellTableResources());
    }

    private void initTable() {
        TextColumn<MdcTransData> meterNumColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return data.getMeterNum();
            }
        };

        TextColumn<MdcTransData> usagePointNameColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return data.getUsagePointName();
            }
        };

       reqReceivedDateColumn = new TextColumn<MdcTransData>() {
           @Override
           public String getValue(MdcTransData data) {
               return FormatUtil.getInstance().formatDateTime(data.getReqReceived());
           }
       };
       reqReceivedDateColumn.setSortable(true);
       reqReceivedDateColumn.setDefaultSortAscending(false);

       TextColumn<MdcTransData> reqTypeColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return data.getReqType();
            }
        };

        TextColumn<MdcTransData> overrideColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return data.getOverride();
            }
        };

        TextColumn<MdcTransData> controlTypeColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return getTranslatedControlType(data.getControltype());
            }
        };

        TextColumn<MdcTransData> paramsColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return data.getParams();
            }
        };

        TextColumn<MdcTransData> repCountColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return Integer.toString(data.getRepCount());
            }
        };

        statusColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                if (data.getTimeCompleted() == null) {
                    return MessagesUtil.getInstance().getMessage("mdc.txn.pending");
                } else if (data.getReqSent() == null) {
                    return MessagesUtil.getInstance().getMessage("mdc.txn.discarded");
                } else if (data.isSuccessful()) {
                    return MessagesUtil.getInstance().getMessage("mdc.txn.successful");
                } else {
                    return MessagesUtil.getInstance().getMessage("mdc.txn.failed");
                }
            }
        };

        Column<MdcTransData, String> timeCompletedDateColumn = new TextColumn<MdcTransData>() {
            @Override
            public String getValue(MdcTransData data) {
                return FormatUtil.getInstance().formatDateTime(data.getTimeCompleted());
            }
        };

        clltblTransactions.addColumn(meterNumColumn, MessagesUtil.getInstance().getMessage("mdc.txn.meter"));
        clltblTransactions.addColumn(usagePointNameColumn, MessagesUtil.getInstance().getMessage("mdc.txn.usagepoint"));
        clltblTransactions.addColumn(reqReceivedDateColumn, MessagesUtil.getInstance().getMessage("mdc.txn.reqreceived"));
        clltblTransactions.addColumn(reqTypeColumn, MessagesUtil.getInstance().getMessage("mdc.txn.reqtype"));
        clltblTransactions.addColumn(overrideColumn, MessagesUtil.getInstance().getMessage("mdc.txn.override"));
        clltblTransactions.addColumn(controlTypeColumn, MessagesUtil.getInstance().getMessage("mdc.txn.controltype"));
        clltblTransactions.addColumn(paramsColumn, MessagesUtil.getInstance().getMessage("mdc.txn.params"));
        clltblTransactions.addColumn(repCountColumn, MessagesUtil.getInstance().getMessage("mdc.txn.repcount"));
        clltblTransactions.addColumn(statusColumn, MessagesUtil.getInstance().getMessage("mdc.txn.status"));
        clltblTransactions.addColumn(timeCompletedDateColumn, MessagesUtil.getInstance().getMessage("mdc.txn.timecompld"));

        dataProvider.addDataDisplay(clltblTransactions);
        smplpgrTransactions.setDisplay(clltblTransactions);


        final NoSelectionModel<MdcTransData> mySelectionModel = new NoSelectionModel<MdcTransData>();
        clltblTransactions.setSelectionModel(mySelectionModel);
        mySelectionModel.addSelectionChangeHandler( new SelectionChangeEvent.Handler() {

            @Override
            public void onSelectionChange(SelectionChangeEvent event) {
                MdcTransactionItemsWidget transactionItemsWidget = new MdcTransactionItemsWidget(clltblTransactions);
                transactionItemsWidget.setMdcTransactionList(mySelectionModel.getLastSelectedObject());
                transactionItemsWidget.show(l,t);
            }
        });
        clltblTransactions.addDomHandler(new ClickHandler()
        {
            @Override
            public void onClick(ClickEvent event)
            {
                l = clltblTransactions.getAbsoluteLeft() - 15;
                t = Window.getScrollTop() + 80;     //event.getClientY()+18;
            }
        }, ClickEvent.getType());
        dateFilter.setDataProvider(dataProvider);
        handleFilterDropdownSelection(null);
    }

    private String getTranslatedControlType(String incoming) {
        String controlType = "";
        if (incoming.equals("CONNECT")) {
            controlType = MessagesUtil.getInstance().getMessage("controltype.connect");
        }
        if (incoming.equals("DISCONNECT")) {
            controlType = MessagesUtil.getInstance().getMessage("controltype.disconnect");
        }
        if (incoming.equals("DISCONNECT_ENABLE")) {
            controlType = MessagesUtil.getInstance().getMessage("controltype.disconnect.enable");
        }
        if (incoming.equals(PAN_DISPLAY)) {
            controlType = MessagesUtil.getInstance().getMessage("controltype.pan.display");
        }
        if (incoming.equals("SYNC_BALANCE")) {
            controlType = MessagesUtil.getInstance().getMessage("controltype.sync.balance");
        }
        if (incoming.equals("ADJUST_BALANCE")) {
            controlType = MessagesUtil.getInstance().getMessage("controltype.adjust.balance");
        }
        if (incoming.equals("POWER_LIMIT")) {
            controlType = MessagesUtil.getInstance().getMessage("controltype.power.limit");
        }
        return controlType;
    }

    public void setMdcTransactions(MeterData meterData, UsagePointWorkspaceView usagePointWorkspaceView) {
        this.meterData = meterData;
        if (checkAllowPermissions(usagePointWorkspaceView)) {
            initConnectDisconnect();
            initMdcOverride();
            handleRelayUserInterface();
            initPowerLimitListBox();
        }else {
            removeConnectDisconnectPanel();
        }
        fetchMdcData();
    }

    private void fetchMdcData() {
        if (meterData != null && meterData.getId() != null) {
            clientFactory.getMeterRpc().getMdcTransByMeterId(meterData.getId(), new ClientCallback<ArrayList<MdcTransData>>() {
                @Override
                public void onSuccess(ArrayList<MdcTransData> result) {
                    if (result != null) {
                        setMdcTransactionList(result);
                    }else {
                        setMdcTransactionList(null);
                    }
                    clearFilterCheckBoxes();
                    dataProvider.resetFilter();
                }
            });
        } else {
            setMdcTransactionList(null);
        }
    }

    public void setMdcTransactionList(ArrayList<MdcTransData> thedata) {
        // If the connected MDC has been removed -> we need to clear the list or set it to empty.
        if (thedata != null && meterData != null
                && (meterData.getMeterModelData() == null || meterData.getMeterModelData().getMdcId() == null)) {
            ArrayList<MdcTransData> emptyList = new ArrayList<MdcTransData>();
            if (columnSortHandler != null){
                columnSortHandler.setList(emptyList);
            }
        }else {
            theTransactiondata = thedata;
            dataProvider.setList(theTransactiondata);
            if (columnSortHandler == null || columnSortHandler.getList() == null) {
                columnSortHandler = new ListHandler<MdcTransData>(dataProvider.getList());
                columnSortHandler.setComparator(reqReceivedDateColumn, new Comparator<MdcTransData>() {
                    public int compare(MdcTransData o1, MdcTransData o2) {
                        if (o1 != null && o2 != null && o1 == o2) {
                            return 0;
                        }
                        if (o1 != null && o1.getReqReceived() != null) {
                            return (o2 != null && o2.getReqReceived() != null) ? o1.getReqReceived().compareTo(o2.getReqReceived()) : 1;
                        }
                        return -1;
                    }
                });
                clltblTransactions.addColumnSortHandler(columnSortHandler);

                //clltblTransactions.addColumnSortHandler(columnSortHandler);
                // We know that the data is sorted alphabetically by default.
                columnSortList = clltblTransactions.getColumnSortList();
                columnSortList.push(reqReceivedDateColumn);
                ColumnSortEvent.fire(clltblTransactions, columnSortList);
            } else {
                columnSortHandler.setList(dataProvider.getList());
                ColumnSortEvent.fire(clltblTransactions, columnSortList);
            }
            clltblTransactions.setPageStart(0);
        }
    }

    @UiHandler("txtbxfilter")
    void handleFilterChange(KeyUpEvent e) {
        if (txtbxfilter.getText().trim().isEmpty()) {
            dataProvider.resetFilter();
        } else {
            List<String> filterList = new ArrayList<String>();
            filterList.add(txtbxfilter.getText().trim());
            dataProvider.setFilter(filterList);
        }
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrTransactions.firstPage();
        clearFilterCheckBoxes();
        dataProvider.refresh();
    }

    @Override
    public boolean isValid(MdcTransData value, String filter) {
        if (showConnectDisconnect.getValue() || showBalanceMessages.getValue()) {
            if (value.getControltype() == null || value.getControltype().isEmpty()) {
                return false;
            }
            return value.getControltype().toLowerCase().contains(filter.toLowerCase());
        }
    	if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("mdc.txn.controltype"))) {
    	    if (value.getControltype() == null || value.getControltype().isEmpty()) {
    	        return false;
    	    }
    	    return value.getControltype().toLowerCase().contains(filter.toLowerCase());
    	} else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("mdc.txn.status"))) {
    	    String status = "";
            if (value.getTimeCompleted() == null) {
                status = MessagesUtil.getInstance().getMessage("mdc.txn.pending");
            } else if (value.getReqSent() == null) {
                status = MessagesUtil.getInstance().getMessage("mdc.txn.discarded");
            } else if (value.isSuccessful()) {
                status = MessagesUtil.getInstance().getMessage("mdc.txn.successful");
            } else {
                status = MessagesUtil.getInstance().getMessage("mdc.txn.failed");
            }
    	   return status.toLowerCase().contains(filter.toLowerCase());
    	} else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("mdc.txn.timecompld"))) {
    	    if (value.getTimeCompleted() == null) {
    	        return false;
    	    }
            return dateFilter.isValid(value.getTimeCompleted(), filter);
    	} else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("mdc.txn.reqreceived"))) {
    	    if (value.getReqReceived() == null) {
                return false;
            }
            return dateFilter.isValid(value.getReqReceived(), filter);
    	} else if (filterDropdown.getValue(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("mdc.txn.reqtype"))) {
    	    return value.getReqType().toLowerCase().contains(filter.toLowerCase());
    	} else {
    	    return false;
    	}
    }

    @UiHandler("filterDropdown")
    void handleFilterDropdownSelection(ChangeEvent changeEvent) {
		dataProvider.resetFilter();
		txtbxfilter.setText("");
		boolean dateSort = false;
		if (filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("mdc.txn.reqreceived"))
		    || filterDropdown.getItemText(filterDropdown.getSelectedIndex()).equals(MessagesUtil.getInstance().getMessage("mdc.txn.timecompld"))) {
		    dateSort = true;
		}
        dateFilter.changeFilter(dateSort);
		txtbxfilter.setVisible(!dateSort);
		clearFilterCheckBoxes();
    }

    @UiHandler("showConnectDisconnect")
    void handleShowConnectDisconnect(ClickEvent event) {
        if (!showConnectDisconnect.getValue()) {
            dataProvider.resetFilter();
        } else {
            List<String> filterList = new ArrayList<String>();
            filterList.add(MessagesUtil.getInstance().getMessage("controltype.connect"));
            filterList.add(MessagesUtil.getInstance().getMessage("controltype.disconnect"));
            filterList.add(MessagesUtil.getInstance().getMessage("controltype.disconnect.enable"));
            dataProvider.setFilter(filterList);
            showBalanceMessages.setValue(false);
        }
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrTransactions.firstPage();
        txtbxfilter.setText("");
        dateFilter.changeFilter(false);
    }

    @UiHandler("showBalanceMessages")
    void handleShowBalanceMessages(ClickEvent event) {
        if (!showBalanceMessages.getValue()) {
            dataProvider.resetFilter();
        } else {
            List<String> filterList = new ArrayList<String>();
            filterList.add(MessagesUtil.getInstance().getMessage("controltype.sync.balance"));
            filterList.add(MessagesUtil.getInstance().getMessage("controltype.adjust.balance"));
            dataProvider.setFilter(filterList);
            showConnectDisconnect.setValue(false);
        }
        dataProvider.setList(theTransactiondata);
        columnSortHandler.setList(dataProvider.getList());
        smplpgrTransactions.firstPage();
        txtbxfilter.setText("");
        dateFilter.changeFilter(false);
    }

    private void clearFilterCheckBoxes() {
        showConnectDisconnect.setValue(false);
        showBalanceMessages.setValue(false);
    }

    //---------------------------------------------------------------------------------------------------

    public boolean checkAllowPermissions(UsagePointWorkspaceView upwView) {
        boolean allow = false;

        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_CONNECT)
                || clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_DISCONNECT)
                || clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_DISCONNECT_ENABLE)
                || clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_PANDISPLAY)
                || clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_GEN_POWER_LIMIT)) {
                    allow = true;
        }

        boolean groupHasGlobal = UsagePointWorkspaceView.hasGlobalContractElement(upwView.getUsagePoint(), clientFactory.isGroupGroupUser());
        if (clientFactory.isEnableAccessGroups() && (groupHasGlobal || clientFactory.isGroupGlobalUser())) {
            allow = false;
        }
        return allow;
    }

    private void initConnectDisconnect() {
        lstbxConnectDisconnect.clear();
        ArrayList<LookupListItem> lli = new ArrayList<LookupListItem>();
        lli.add(new LookupListItem("", ""));
        if (meterData != null && meterData.getMeterModelData() != null && meterData.getMeterModelData().getMdcId() != null) {
            if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_CONNECT)) {
                lli.add(new LookupListItem("CONNECT", MessagesUtil.getInstance().getMessage("mdc.txn.connect")));
            }
            if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_DISCONNECT)){
                lli.add(new LookupListItem("DISCONNECT", MessagesUtil.getInstance().getMessage("mdc.txn.disconnect")));
            }
            if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_DISCONNECT_ENABLE)){
                lli.add(new LookupListItem("DISCONNECT_ENABLE", MessagesUtil.getInstance().getMessage("mdc.txn.disconnect.enable")));
            }
            if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_PANDISPLAY)
                    && meterData != null && meterData.getMeterModelData() != null && meterData.getMeterModelData().isDisplayMessage()){
                lli.add(new LookupListItem(PAN_DISPLAY, MessagesUtil.getInstance().getMessage("mdc.txn.pandisplay")));
            }
            if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_GEN_POWER_LIMIT)
                    && meterData != null && meterData.getMeterModelData() != null // For validating ServiceResource = ELEC
                    && ServiceResourceE.fromId(meterData.getMeterModelData().getServiceResourceId()).equals(ServiceResourceE.ELEC))
                lli.add(new LookupListItem("POWER_LIMIT", MessagesUtil.getInstance().getMessage("mdc.txn.power.limit")));
        }
        lstbxConnectDisconnect.setLookupItems(lli);
        lstbxConnectDisconnect.setSelectedIndex(0);
    }

    private void initMdcOverride() {
        if (clientFactory.getUser().hasPermission(MeterMngStatics.ACCESS_PERMISSION_MM_MDC_CONTROLREQ_OVERRIDE)) {
            lstbxMdcOverride.clear();
            if (meterData != null && meterData.getMeterModelData() != null && meterData.getMeterModelData().getMdcId() != null) {
                ArrayList<LookupListItem> lli = new ArrayList<LookupListItem>();
                lli.add(new LookupListItem("NONE", MessagesUtil.getInstance().getMessage("mdc.txn.override.none")));
                lli.add(new LookupListItem("ALL", MessagesUtil.getInstance().getMessage("mdc.txn.override.all")));
                lstbxMdcOverride.setLookupItems(lli);
                lstbxMdcOverride.setSelectedIndex(0);
            }
        } else {
            mdcOverridePanel.removeFromParent();
        }
    }

    public void removeConnectDisconnectPanel(){
        connectDisconnectPanel.removeFromParent();
        mdcMessageButtons.removeFromParent();
    }

    @UiHandler("lstbxConnectDisconnect")
    void handleMdcMessageChange(ChangeEvent event) {
        LookupListItem item = lstbxConnectDisconnect.getItem(lstbxConnectDisconnect.getSelectedIndex());
        if (item == null ) {
            return;
        }
        if (item.getValue().equals(PAN_DISPLAY)) {
            lstbxMdcOverride.setSelectedIndex(0);
            lstbxMdcOverride.setEnabled(false);
        }else
            lstbxMdcOverride.setEnabled(true);
        if (item.getValue().equals("CONNECT") || item.getValue().equals("DISCONNECT"))
            handleRelayUserInterface();
        else
            relaySelectionElement.setVisible(false);
        if (item.getValue().equals("POWER_LIMIT")) {
            powerLimitListBox.setSelectedIndex(0);
            powerLimitElement.setVisible(true);
        }else
            powerLimitElement.setVisible(false);
    }

    @UiHandler("btnCancel")
    void handleCancelMdcMessage(ClickEvent event) {
        lstbxConnectDisconnect.setSelectedIndex(0);
        relaySelectionElement.setVisible(false);
        powerLimitElement.setVisible(false);
    }

    @UiHandler("btnSend")
    void handleSendMdcMessage(ClickEvent event) {
        connectDisconnectElement.clearErrorMsg();
        if (lstbxConnectDisconnect.getSelectedIndex() == 0) {
            return;
        }
        if (lstbxConnectDisconnect.getSelectedIndex() > 0) {
            lstbxConnectDisconnect.setEnabled(false);
            lstbxMdcOverride.setEnabled(false);
            powerLimitListBox.setEnabled(false);
            btnSend.setEnabled(false);
            btnCancel.setVisible(false);
            final int left = btnSend.getAbsoluteLeft()+btnSend.getOffsetWidth();
            final int top = btnSend.getAbsoluteTop()-btnSend.getOffsetHeight();
            // Map form fields to data object
            if (clientFactory != null) {
                final ClientCallback<IpayResponseData> connectDisconnectSvcAsyncCallback = new ClientCallback<IpayResponseData>() {
                    @Override
                    public void onSuccess(IpayResponseData result) {
                        if (result==null) {
                            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meter.connect.disconnect.connection.error"),
                                    MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                                    MessagesUtil.getInstance().getMessage("button.close"));
                        } else if (!result.getResCode().equals("mdc000") && !result.getResCode().equals("mdc010") && !result.getResCode().equals("mdc011")) {
                            Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage("meter.connect.disconnect.error", new String[]{result.getResRef()}),
                                                        MediaResourceUtil.getInstance().getErrorIcon(), left, top,
                                                        MessagesUtil.getInstance().getMessage("button.close"));
                        } else {
                            String mesg = "meter.connect.disconnect.ok.mdc000";
                            if (result.getResCode().equals("mdc010")) {
                                mesg = "meter.connect.disconnect.ok.mdc010";
                            } else if (result.getResCode().equals("mdc011")) {
                                mesg = "meter.connect.disconnect.ok.mdc011";
                            }
                            Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage(mesg, new String[]{lstbxConnectDisconnect.getValue(lstbxConnectDisconnect.getSelectedIndex()),
                                                        result.getResRef()}),
                                                        MediaResourceUtil.getInstance().getInformationIcon(), left, top, null);
                        }
                        lstbxConnectDisconnect.setEnabled(true);
                        lstbxConnectDisconnect.setSelectedIndex(0);
                        lstbxMdcOverride.setEnabled(true);
                        lstbxMdcOverride.setSelectedIndex(0);
                        btnSend.setEnabled(true);
                        btnCancel.setVisible(true);
                        powerLimitElement.setVisible(false);
                        powerLimitListBox.setEnabled(true);
                        powerLimitListBox.setSelectedIndex(0);
                        relaySelectionElement.setVisible(false);
                        fetchMdcData();
                    }

                    @Override
                    public void onFailureClient() {
                        lstbxMdcOverride.setEnabled(true);
                        lstbxConnectDisconnect.setEnabled(true);
                        btnSend.setEnabled(true);
                        btnCancel.setVisible(true);
                        powerLimitListBox.setEnabled(true);
                    }
                };
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        if (!lstbxConnectDisconnect.getValue(lstbxConnectDisconnect.getSelectedIndex()).equals("POWER_LIMIT"))
                            clientFactory.getMeterRpc().sendConnectDisconnectMsg(meterData,
                                    lstbxConnectDisconnect.getValue(lstbxConnectDisconnect.getSelectedIndex()),
                                    lstbxMdcOverride.getValue(lstbxMdcOverride.getSelectedIndex()), getRelayId(),
                                    connectDisconnectSvcAsyncCallback);
                        else if (!powerLimitListBox.getValue(powerLimitListBox.getSelectedIndex()).equals("-1")) {
                            clientFactory.getMeterRpc().sendPowerLimitMsg(meterData,
                                    lstbxMdcOverride.getValue(lstbxMdcOverride.getSelectedIndex()),
                                    powerLimitListBox.getValue(powerLimitListBox.getSelectedIndex()),
                                    connectDisconnectSvcAsyncCallback);
                        }else {
                            lstbxMdcOverride.setEnabled(true);
                            lstbxConnectDisconnect.setEnabled(true);
                            btnSend.setEnabled(true);
                            btnCancel.setVisible(true);
                            powerLimitListBox.setEnabled(true);
                        }
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            }
        }
    }

    private void handleRelayUserInterface() {
        String tempLookupListItem = lstbxConnectDisconnect.getItem(
                lstbxConnectDisconnect.getSelectedIndex()).getValue();
        // Remove relay selection if Meter Model gets updated and lstbxConnectDisconnect-selection gets reset
        if (!tempLookupListItem.equals("CONNECT") && !tempLookupListItem.equals("DISCONNECT")) {
            relaySelectionElement.setVisible(false);
        }
        if (meterData != null) {
            final Long meterModelId = meterData.getMeterModelId();
            if (meterModelId != null && meterData.getMeterModelData() != null
                    && meterData.getMeterModelData().getMdcId() != null) {
                SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                    @Override
                    public void callback(SessionCheckResolution resolution) {
                        clientFactory.getMeterModelRpc().getMeterModelDtoForMeterModelId(meterModelId,
                                new ClientCallback<MeterModelDto>() {
                                    @Override
                                    public void onSuccess(MeterModelDto result) {
                                        if (result.getMeterDataDecoder() != null) {
                                            String decoderClass = result.getMeterDataDecoder().getDecoderClass();
                                            if ("za.co.ipay.hydra.drivers.meters.KamstrupSinglePhase".equals(decoderClass)
                                                    || "za.co.ipay.hydra.drivers.meters.KamstrupThreePhase".equals(decoderClass)
                                                    || "za.co.ipay.hydra.drivers.meters.KamstrupCT".equals(decoderClass)) {
                                                addRelayButtons(decoderClass);
                                            } else {
                                                relaySelectionElement.setVisible(false);
                                            }
                                        } else {
                                            relaySelectionElement.setVisible(false);
                                        }
                                    }
                                }
                        );
                    }
                };
                clientFactory.handleSessionCheckCallback(sessionCheckCallback);
            } else {
                relaySelectionElement.setVisible(false);
            }
        } else {
            relaySelectionElement.setVisible(false);
        }
    }

    private void initPowerLimitListBox() {
        updatePowerLimits();
        if (lstbxConnectDisconnect.getItem(
                lstbxConnectDisconnect.getSelectedIndex()).getValue().equals("POWER_LIMIT")) {
            powerLimitElement.setVisible(true);
        }
        else {
            powerLimitElement.setVisible(false);
        }
    }

    public void updatePowerLimits() {
        clientFactory.getAppSettingRpc().getAppSettingByKey(AppSettings.POWER_LIMIT_SETTINGS, new ClientCallback<AppSetting>() {
            @Override
            public void onSuccess(AppSetting result) {
                powerLimitListBox.clearAll();
                List<LookupListItem> limitList = new ArrayList<LookupListItem>();
                for (PowerLimitValue limit : PowerLimitValue.fromAppSetting(result))
                    limitList.add(new LookupListItem(String.valueOf(limit.getValue()), limit.getLabel()));
                powerLimitListBox.setLookupItemsWithEmptyFirst(limitList);
            }
        });
    }

    private void addRelayButtons(String decoderClass) {
        relaySelectionElement.remove(relayRadioAuxTwo);
        if (!decoderClass.equalsIgnoreCase("za.co.ipay.hydra.drivers.meters.KamstrupCT"))
            relaySelectionElement.add(relayRadioAuxTwo);
        else
            relaySelectionElement.remove(relayRadioAuxTwo);
        // Default selection set to 'Main' -> functions as a mandatory group
        relayRadioMain.setValue(true);
        relayRadioAuxOne.setValue(false);
        String tempLookupListItem = lstbxConnectDisconnect.getItem(
                lstbxConnectDisconnect.getSelectedIndex()).getValue();
        if (tempLookupListItem.equals("CONNECT") || tempLookupListItem.equals("DISCONNECT"))
            relaySelectionElement.setVisible(true);
    }

    private String getRelayId() {
        if(relaySelectionElement.isVisible()) {
            if(relayRadioAuxOne.getValue().equals(true))
                return "1";
            if (relayRadioAuxTwo.getValue().equals(true))
                return "2";
        }
        return null;
    }
}
