package za.co.ipay.metermng.client.view.component.group;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.user.cellview.client.Column;
import com.google.gwt.user.cellview.client.TextColumn;
import com.google.gwt.user.client.ui.Anchor;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.view.client.ListDataProvider;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.form.FormManager;
import za.co.ipay.gwt.common.client.handler.ConfirmHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.validation.ClientValidatorUtil;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.SimpleTableView;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.view.workspace.group.type.GroupTypeWorkspaceView;
import za.co.ipay.metermng.mybatis.generated.model.GroupType;
import za.co.ipay.metermng.shared.GroupHierarchyData;

public class GroupHierarchyView extends BaseComponent implements FormManager<GroupHierarchyData> {

    private GroupTypeWorkspaceView parentWorkspace;
    private GroupType groupType;
    private GroupHierarchyData groupHierarchyData;

    private ListDataProvider<GroupHierarchyData> dataProvider;
    private SimpleTableView<GroupHierarchyData> view;
    private GroupHierarchyPanel panel;
    private Button deleteBtn;

    private static Logger logger = Logger.getLogger(GroupHierarchyView.class.getName());
    private TextColumn<GroupHierarchyData> accessGroupColumn;

    public GroupHierarchyView(ClientFactory clientFactory, GroupTypeWorkspaceView parentWorkspace, SimpleTableView<GroupHierarchyData> view) {
        this.clientFactory = clientFactory;
        this.parentWorkspace = parentWorkspace;
        this.view = view;
        initView();
        initForm();
        createTable();
    }

    private void initView() {
        view.setFormManager(this);
    }

    public void setGroupType(GroupType type) {
        clear();
        this.groupType = type;
        view.setDataDetails(groupType.getName(), groupType.getDescription());
        // since table and form is shared between instances of group type
        // we have to remove/add fields as applicable to different types
        if(type.isLocationGroup() && clientFactory.isEnableAccessGroups()) {
            panel.isAccessGroupElement.setVisible(true);
            if (view.getTable().getColumnIndex(accessGroupColumn) == -1) {
                view.getTable().addColumn(accessGroupColumn, MessagesUtil.getInstance().getMessage("grouphierarchy.field.is_access_group"));
            }
        } else {
            panel.isAccessGroupElement.setVisible(false);
            if (view.getTable().getColumnIndex(accessGroupColumn) > 0) {
                view.getTable().removeColumn(accessGroupColumn);
            }
        }
        loadHierarchies();
    }

    private void initForm() {
        Anchor back = new Anchor(MessagesUtil.getInstance().getMessage("grouptypes.header"));
        back.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            parentWorkspace.showGroupTypes();
                        }
                    }});
            }
        });
        view.getPageHeader().addPageHeaderLink(back);

        panel = new GroupHierarchyPanel(view.getForm());
        panel.ensureDebugId("groupHierarchyViewPanel");
        if(! clientFactory.isEnableAccessGroups()) {
            panel.isAccessGroupElement.setVisible(false);
        }
        view.getForm().setHasDirtyDataManager(parentWorkspace);
        view.getForm().getFormFields().add(panel);

        view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        view.getForm().getSaveBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                saveGroupHierarchy();
            }
        });
        view.getForm().getSaveBtn().ensureDebugId("hierarchySaveBtn");

        view.getForm().getOtherBtn().setText(MessagesUtil.getInstance().getMessage("button.cancel"));
        view.getForm().getOtherBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            onCancelClick();
                        }
                    }
                });
            }
        });
        view.getForm().getOtherBtn().ensureDebugId("hierarchyCancelBtn");

        view.getForm().getBackBtn().setVisible(true);
        view.getForm().getBackBtn().setText(MessagesUtil.getInstance().getMessage("button.back"));
        view.getForm().getBackBtn().addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                view.getForm().checkDirtyData(new ConfirmHandler() {
                    @Override
                    public void confirmed(boolean confirm) {
                        if (confirm) {
                            view.getForm().setDirtyData(false);
                            parentWorkspace.showGroupTypes();
                        }
                    }});
            }
        });
        view.getForm().getBackBtn().ensureDebugId("hierarchyBackBtn");

        deleteBtn = new Button(MessagesUtil.getInstance().getMessage("button.delete"));
        deleteBtn.addClickHandler(new ClickHandler() {
            @Override
            public void onClick(ClickEvent event) {
                deleteGroupHierarchy();
            }
        });
        deleteBtn.setVisible(false);
        deleteBtn.ensureDebugId("hierarchyDeleteBtn");
        view.getForm().getSecondaryButtons().add(deleteBtn);
        view.getForm().getSecondaryButtons().setVisible(true);

        view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("grouphierarchy.title.add"));
    }

    private void createTable() {
        if (dataProvider == null) {
            view.getTable().addColumn(createLevelColumn(), MessagesUtil.getInstance().getMessage("grouphierarchy.field.level"));
            view.getTable().addColumn(createNameColumn(), MessagesUtil.getInstance().getMessage("grouphierarchy.field.name"));
            view.getTable().addColumn(createAccessGroupColumn(), MessagesUtil.getInstance().getMessage("grouphierarchy.field.is_access_group"));

            view.getTable().ensureDebugId("hierarchyTable");
            view.getTable().setVisibleRange(0, getPageSize());

            dataProvider = new ListDataProvider<GroupHierarchyData>();
            dataProvider.addDataDisplay(view.getTable());
            view.getPager().setDisplay(view.getTable());
        }
    }

    private Column<GroupHierarchyData, ?> createLevelColumn() {
        TextColumn<GroupHierarchyData> column = new TextColumn<GroupHierarchyData>() {
            @Override
            public String getValue(GroupHierarchyData data) {
                return Integer.toString(data.getLevel());
            }
        };
        return column;
    }

    private Column<GroupHierarchyData, ?> createNameColumn() {
        TextColumn<GroupHierarchyData> column = new TextColumn<GroupHierarchyData>() {
            @Override
            public String getValue(GroupHierarchyData data) {
                return data.getName();
            }
        };
        return column;
    }

    private Column<GroupHierarchyData, ?> createAccessGroupColumn() {
        accessGroupColumn = new TextColumn<GroupHierarchyData>() {
            @Override
            public String getValue(GroupHierarchyData object) {
                if (object.isAccessGroup()) {
                    return MessagesUtil.getInstance().getMessage("button.yes");
                } else {
                    return "";
                }
            }
        };
        return accessGroupColumn;
    }

    private void displayGroupHierarchy(GroupHierarchyData data) {
        clearFields();
        clearErrors();
        this.groupHierarchyData = data;
        if (groupHierarchyData == null) {
            this.groupHierarchyData = new GroupHierarchyData();
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("grouphierarchy.title.add"));
            deleteBtn.setVisible(false);
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.create"));
        } else {
            view.getForm().getFormPanel().setHeadingText(MessagesUtil.getInstance().getMessage("grouphierarchy.title.update"));
            deleteBtn.setVisible(true);
            view.getForm().getSaveBtn().setText(MessagesUtil.getInstance().getMessage("button.update"));
        }
        panel.nameBox.setText(groupHierarchyData.getName());
        panel.isAccessGroupBox.setValue(groupHierarchyData.isAccessGroup());
        view.setDataDetails(groupType.getName(), groupType.getDescription());
    }

    public void deleteGroupHierarchy() {
        if (groupHierarchyData != null) {
            ConfirmHandler handler = new ConfirmHandler() {
                @Override
                public void confirmed(boolean confirm) {
                    if (confirm) {
                        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
                            @Override
                            public void callback(SessionCheckResolution resolution) {
                                clientFactory.getGroupRpc().deleteGroupHierarchy(groupHierarchyData.getId(),
                                        new ClientCallback<Void>(deleteBtn.getAbsoluteLeft(), deleteBtn.getAbsoluteTop()) {
                                            @Override
                                            public void onSuccess(Void result) {
                                                clear();
                                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getMessage("grouphierarchy.deleted"),
                                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                                        deleteBtn.getAbsoluteLeft(),
                                                        deleteBtn.getAbsoluteTop(),
                                                        MessagesUtil.getInstance().getMessage("button.close"));
                                                loadHierarchies();
                                                onCancelClick();
                                            }
                                        });
                            }
                        };
                        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
                    }
                }
            };
            Dialogs.confirm(MessagesUtil.getInstance().getMessage("grouphierarchy.delete.confirm"),
                    MessagesUtil.getInstance().getMessage("button.yes"),
                    MessagesUtil.getInstance().getMessage("button.no"),
                    MediaResourceUtil.getInstance().getQuestionIcon(),
                    handler,
                    deleteBtn.getAbsoluteLeft(), deleteBtn.getAbsoluteTop());
        }
    }

    protected void loadHierarchies() {
        clientFactory.getGroupRpc().getGroupHierarchies(groupType.getId(), new ClientCallback<ArrayList<GroupHierarchyData>>() {
            @Override
            public void onSuccess(ArrayList<GroupHierarchyData> data) {
                displayData(data);
            }
        });
    }

    private void displayData(List<GroupHierarchyData> data) {
        logger.info("Displaying retrieved group hierarchies: "+data.size());
        if (dataProvider != null && dataProvider.getList() != null) {
            dataProvider.getList().clear();
            dataProvider.getList().addAll(data);
        }
        view.getTable().setPageStart(0);
    }

    public void onCancelClick() {
        clear();
        displayGroupHierarchy(null);
    }

    private void saveGroupHierarchy() {
        if (isValidInput()) {
            updateGroupHierarchy();
            if (groupHierarchyData.getId() == null) {
                //New instance
                saveOK();
            } else {
              //Cannot save a change to hierarchy item if other tabs (Except for DashBoard & this one) are open
                GroupProcessTabs processTabs = new GroupProcessTabs() {
                    @Override
                    protected void continueProcess() {
                        saveOK();
                    }
                };
                processTabs.processOpenTabs(clientFactory, view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop(), parentWorkspace);
            }
        }
    }
    private void saveOK() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution resolution) {
                clientFactory.getGroupRpc().updateGroupHierarchy(groupHierarchyData,
                        new ClientCallback<GroupHierarchyData>(view.getForm().getSaveBtn().getAbsoluteLeft(), view.getForm().getSaveBtn().getAbsoluteTop()) {
                            @Override
                            public void onSuccess(GroupHierarchyData result) {
                                view.getForm().setDirtyData(false);
                                groupHierarchyData = null;
                                onCancelClick();
                                Dialogs.displayInformationMessage(MessagesUtil.getInstance().getSavedMessage(new String[] { MessagesUtil.getInstance().getMessage("grouphierarchy.title") }),
                                        MediaResourceUtil.getInstance().getInformationIcon(),
                                        view.getForm().getSaveBtn().getAbsoluteLeft(),
                                        view.getForm().getSaveBtn().getAbsoluteTop(),
                                        MessagesUtil.getInstance().getMessage("button.close"));
                                loadHierarchies();
                            }
                        });
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    private void clearFields() {
        panel.clearFields();
    }

    private void clearErrors() {
        panel.clearErrors();
    }

    private boolean isValidInput() {
        boolean valid = true;
        clearErrors();
        if (groupType == null) {
            valid = false;
        }
        //making a copy as don't want to wipe out the original data's name if it is invalid and/or not saved
        GroupHierarchyData hierarchy = new GroupHierarchyData();
        hierarchy.setName(panel.nameBox.getText());
        hierarchy.setAccessGroup(panel.isAccessGroupBox.getValue());
        if (!ClientValidatorUtil.getInstance().validateField(hierarchy, "name", panel.nameElement)) {
            valid = false;
        }
        if(hierarchy.isAccessGroup() && hasOrgGroupAlready()) {
            panel.isAccessGroupElement.showErrorMsg(MessagesUtil.getInstance().getMessage("grouphierarchy.field.is_access_group.error.already_assigned"));
            valid = false;
        }
        return valid;
    }

    private void updateGroupHierarchy() {
        logger.info("Updating groupHierarchyData from panel: "+groupHierarchyData);
        if (groupHierarchyData == null) {
            this.groupHierarchyData = new GroupHierarchyData();
            view.clearTableSelection();
        }
        //New instance so set the parentId as the last current hierarchy
        if (groupHierarchyData.getId() == null) {
            groupHierarchyData.setParentId(getParentHierarchyId());
        }
        groupHierarchyData.setGroupTypeId(groupType.getId());
        groupHierarchyData.setName(panel.nameBox.getText());
        groupHierarchyData.setAccessGroup(panel.isAccessGroupBox.getValue());
    }

    private Long getParentHierarchyId() {
        if (dataProvider.getList() != null && dataProvider.getList().size() > 0) {
            GroupHierarchyData last = dataProvider.getList().get(dataProvider.getList().size()-1);
            return last.getId();
        } else {
            return null;
        }
    }

    private boolean hasOrgGroupAlready() {
        if (dataProvider.getList() != null && dataProvider.getList().size() > 0) {
            List<GroupHierarchyData> hierarchies = dataProvider.getList();
            for (GroupHierarchyData groupHierarchyData : hierarchies) {
                if(groupHierarchyData.isAccessGroup()) {
                    return true;
                }
            }
        }
        return false;
    }


    public void clear() {
        view.getForm().setDirtyData(false);
        this.groupHierarchyData = null;
        clearFields();
        clearErrors();
        view.clearTableSelection();
    }

    @Override
    public void displaySelected(GroupHierarchyData selected) {
        displayGroupHierarchy(selected);
    }
}
