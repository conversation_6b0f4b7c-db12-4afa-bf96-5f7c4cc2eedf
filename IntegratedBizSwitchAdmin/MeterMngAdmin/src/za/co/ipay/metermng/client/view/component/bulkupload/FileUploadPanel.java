package za.co.ipay.metermng.client.view.component.bulkupload;

import java.util.logging.Logger;

import com.google.gwt.core.client.GWT;
import com.google.gwt.dom.client.BrowserEvents;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.place.shared.Place;
import com.google.gwt.safehtml.shared.SafeHtmlUtils;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.cellview.client.CellTable;
import com.google.gwt.user.client.Window;
import com.google.gwt.user.client.ui.Button;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.FileUpload;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.FormPanel;
import com.google.gwt.user.client.ui.FormPanel.SubmitCompleteEvent;
import com.google.gwt.user.client.ui.FormPanel.SubmitEvent;
import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Hidden;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;
import com.google.gwt.view.client.CellPreviewEvent;
import com.google.gwt.view.client.DefaultSelectionEventManager;
import com.google.gwt.view.client.SelectionChangeEvent;
import com.google.gwt.view.client.SingleSelectionModel;

import za.co.ipay.gwt.common.client.Dialogs;
import za.co.ipay.gwt.common.client.dataexport.GetRequestBuilder;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.PageHeader;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.workspace.IPayDataProvider;
import za.co.ipay.gwt.common.client.workspace.IpayDataProviderFilter;
import za.co.ipay.gwt.common.client.workspace.SessionCheckCallback;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog;
import za.co.ipay.gwt.common.client.workspace.WaitingDialog.WaitingDialogUtil;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification;
import za.co.ipay.gwt.common.client.workspace.WorkspaceNotification.NotificationType;
import za.co.ipay.metermng.client.MeterMngAdmin;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.resource.image.MediaResource.MediaResourceUtil;
import za.co.ipay.metermng.client.view.component.login.LoginView.LoginViewUtil;
import za.co.ipay.metermng.client.view.workspace.BaseWorkspace;
import za.co.ipay.metermng.client.view.workspace.bulkupload.ParentUpload;
import za.co.ipay.metermng.shared.MeterMngStatics;


/*
 * RC: Set HEading
 * set helpmsg & text FormElement ui:field="importDataFileElement" helpMsg="{msg.getTransUploadFileHelp}" labelText="{msg.getTransUploadFile}:"
 * set heading Button ui:field="btnUploadCsv" text="{msg.getTransUploadCsvButton}"
 * set htableheading ui:field="tableHeading"
 */

public class FileUploadPanel<T> extends BaseWorkspace implements IpayDataProviderFilter<T> {

    @UiField PageHeader pageHeader;
    @UiField HTML dataTitle;

    //@UiField VerticalPanel downloadSamplePanel;
    //@UiField Button btnUploadSample;
    @UiField VerticalPanel genTemplateButtonPanel;

	@UiField FlowPanel  mainPanel;
    @UiField VerticalPanel uploadCsvPanel;
    @UiField HTML uploadCsvDescription;
    @UiField FormPanel importDataFormPanel;
    @UiField Hidden actionParam;
    @UiField Hidden ignoreDupsParam;
    @UiField FormElement importDataFileElement;
    @UiField FileUpload importDataFile;
    @UiField FormElement ignoreDupsElement;
    @UiField CheckBox ignoreDupsCheckBox;
    @UiField Button btnUploadCsv;
    @UiField Button btnDownloadTemplate;

    @UiField VerticalPanel processCsvPanel;
    @UiField HTML processCsvDescription;
    @UiField Label tableHeading;
    @UiField(provided=true)  CellTable<T> clltblTransactions;
    @UiField Label errorMsg;
    @UiField Button btnCancel;
    @UiField Button btnProcessTrans;

    private SingleSelectionModel<T> selectionModel;

    private IPayDataProvider<T> dataProvider;
    private ParentUpload<T> parentUpload;
    private String uploadType;                // eg. meter for MeterBulk Upload.... use to construct specific key for error message

    //private static final int DEFAULT_PAGE_SIZE = 15;
    private int messagePosTop = 0;
    private int messagePosLeft = 0;
    private int scrollyPosTop = 0;
    private int scrollyPosLeft = 0;
    private String uploadedFile;
    private Boolean uploadedIgnoreDupsSetting = false;
    private String filenameForMessage;
    private int popuptop;

    private static Logger logger = Logger.getLogger(FileUploadPanel.class.getName());

    private static FileUploadPanelUiBinder uiBinder = GWT.create(FileUploadPanelUiBinder.class);

    @SuppressWarnings("rawtypes")
    interface FileUploadPanelUiBinder extends UiBinder<Widget, FileUploadPanel> {
    }

//    public FileUploadPanel(String uploadType, String header, String descrip, String urlHandlerMapping, CellTable<T> clltblTransactions, Column<T, String> errorColumn) {
    public FileUploadPanel(ClientFactory clientFactory, String uploadType, ParentUpload<T> parentUpload) {
        this.clientFactory = clientFactory;
        this.parentUpload = parentUpload;
        this.uploadType = uploadType;
        initTable();
        initWidget(uiBinder.createAndBindUi(this));
        initHeaders();
        initMapping();
        initTemplateDownloadButton();
    }

    private void initTemplateDownloadButton() {
        if (uploadType.equals(MeterMngStatics.METER_UPLOAD)
                || uploadType.equals(MeterMngStatics.CUST_TRANSACTION_UPLOAD)
                || uploadType.equals(MeterMngStatics.AUX_TRANSACTION_UPLOAD)
                || uploadType.equals(MeterMngStatics.AUX_ACCOUNT_UPLOAD)) {
            btnDownloadTemplate.setVisible(true);
        }
    }

	private void initHeaders() {
        String pageHeaderValue = MessagesUtil.getInstance().getMessage(parentUpload.getPageHeaderKey());
        String header = MessagesUtil.getInstance().getMessage(parentUpload.getDataTitleKey());

        String descrip = MessagesUtil.getInstance().getMessage(parentUpload.getUploadDescriptionKey());
        if (pageHeaderValue != null) {
            pageHeader.setHeading(pageHeaderValue);
        }
        if (header != null) {
            dataTitle.setText(header);
        }
        if (descrip != null) {
            uploadCsvDescription.setText(descrip);
            uploadCsvDescription.setVisible(true);
        } else {
            uploadCsvDescription.setVisible(false);
        }
    }

    private void initMapping() {
        if (uploadType.equals(MeterMngStatics.METER_UPLOAD)) {
            ignoreDupsElement.setVisible(true);
            ignoreDupsCheckBox.setValue(false);
        } else {
            ignoreDupsCheckBox.removeFromParent();
        }
        importDataFormPanel.setAction(GWT.getModuleBaseURL()+parentUpload.getUrlHandlerMapping());        //"secure/importcustomertrans.do");
        importDataFormPanel.setEncoding(FormPanel.ENCODING_MULTIPART);
        importDataFormPanel.setMethod(FormPanel.METHOD_POST);
        importDataFile.setName("file");
    }

    protected void initTable() {
        // Set the data provider for the table
        clltblTransactions = parentUpload.getTable();

        if (dataProvider == null) {
            dataProvider= new IPayDataProvider<T>(this) ;
            dataProvider.addDataDisplay(clltblTransactions);
        }

        selectionModel = new SingleSelectionModel<T>();
        CellPreviewEvent.Handler<T> handler = new CellPreviewEvent.Handler<T>() {
            final CellPreviewEvent.Handler<T> selectionEventManager = DefaultSelectionEventManager.createDefaultManager();
            @Override
            public void onCellPreview(final CellPreviewEvent<T> event) {
                if (BrowserEvents.CLICK.equals(event.getNativeEvent().getType())) {
                    if (!event.isCanceled()) {
                        selectionEventManager.onCellPreview(event);
                    }
                } else {
                    selectionEventManager.onCellPreview(event);
                }
            }};
            clltblTransactions.setSelectionModel(selectionModel, handler);
            selectionModel.addSelectionChangeHandler(new SelectionChangeEvent.Handler() {
                public void onSelectionChange(SelectionChangeEvent event) {

                    final T selected = selectionModel.getSelectedObject();
                    if (selected != null) {
                        displaySelected(selected);
                    }
                }
            });
            clltblTransactions.addDomHandler(new ClickHandler()
            {
                @Override
                public void onClick(ClickEvent event)
                {
                    popuptop = event.getClientY()+20;
                }
            }, ClickEvent.getType());

            popuptop = clltblTransactions.getAbsoluteTop()-(clltblTransactions.getBodyHeight()+clltblTransactions.getHeaderHeight());

    }

    /*
     * If many fields that don't fit in the width, like Meter_Customer-UsagePoint upload --> can here create a popup with scrollpanel showing the whole row
     */
    public void displaySelected(final T selected) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                parentUpload.displaySelected(selected, processCsvPanel.getAbsoluteLeft(), popuptop);
                selectionModel.clear();
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    public void setTable(CellTable<T> table) {
        this.clltblTransactions = table;
    }

    @UiHandler("btnDownloadTemplate")
    void handleDownloadImportTemplate(ClickEvent clickEvent) {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                String encodedUrl = new GetRequestBuilder().withBaseUrl(GWT.getHostPageBaseURL())
                        .withTargetUrl("templateexport")
                        .addParam("import-type", uploadType)
                        .toEncodedUrl();

                if (encodedUrl != null) {
                    Window.Location.assign(encodedUrl);
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("btnUploadCsv")
    void handleUploadCsvButton(ClickEvent clickEvent) {
        //recreate table in case did a processTrans inbetween - which then removes 1st column
        if (clltblTransactions.getColumn(0) != parentUpload.getErrorColumn()) {
            clltblTransactions.insertColumn(0, parentUpload.getErrorColumn(),
                    SafeHtmlUtils.fromSafeConstant("<span class=\"error\">" + MessagesUtil.getInstance().getMessage("bulk.upload.errors") + "</span>"));
        }

        importDataFileElement.clearErrorMsg();
        //Validate a filename WAS selected
        if (importDataFile.getFilename() == null || importDataFile.getFilename().isEmpty()) {
            importDataFileElement.showErrorMsg(MessagesUtil.getInstance().getMessage("bulk.upload.file.none"));
            clear();
            return;
        }
        //Validate format of filename is xxxxxxxxxxx-reference.csv where <reference> is saved as 'our Ref' on transactions.
        String fn = importDataFile.getFilename();
        int indexOfHyphen = fn.lastIndexOf('-');
        int indexOfPeriod = fn.indexOf('.');
        if (indexOfHyphen < 0 || indexOfPeriod < 0 || !(indexOfPeriod - indexOfHyphen > 1)) {
            importDataFileElement.showErrorMsg(MessagesUtil.getInstance().getMessage("bulk.upload.invalid.filename"));
            clear();
            return;
        }

        messagePosTop = btnUploadCsv.getAbsoluteTop();
        messagePosLeft = btnUploadCsv.getAbsoluteLeft();
        scrollyPosTop = btnUploadCsv.getAbsoluteTop();
        scrollyPosLeft = btnUploadCsv.getAbsoluteLeft();
        actionParam.setValue("uploadCsv");
        ignoreDupsParam.setValue(ignoreDupsCheckBox.getValue().toString());
        uploadedIgnoreDupsSetting = ignoreDupsCheckBox.getValue();
        btnUploadCsv.setEnabled(false);
        logger.info("****FileUpload UploadCsv selection: " + importDataFormPanel.getAction() + " : " + importDataFile.getFilename());
        //downloadSamplePanel.setVisible(false);
        submitUpload();
    }

    @UiHandler("btnProcessTrans")
    void handleProcessTransButton(ClickEvent clickEvent) {
        if (!importDataFile.getFilename().equals(uploadedFile)) {
            importDataFileElement.showErrorMsg(MessagesUtil.getInstance().getMessage("bulk.upload.invalid.filename.changed", new String[] {uploadedFile, importDataFile.getFilename()}));
            clear();
            return;
        }
        if (!uploadedIgnoreDupsSetting.equals(ignoreDupsCheckBox.getValue())) {
            importDataFileElement.showErrorMsg(MessagesUtil.getInstance().getMessage("bulk.upload.ignore.meter.dups.changed", new String[] {uploadedIgnoreDupsSetting.toString(), ignoreDupsCheckBox.getValue().toString()}));
            clear();
            return;
        }
        importDataFileElement.clearErrorMsg();
        messagePosTop += 50;
        scrollyPosTop = btnProcessTrans.getAbsoluteTop();
        scrollyPosLeft = btnProcessTrans.getAbsoluteLeft();
        actionParam.setValue("processTrans");
        ignoreDupsParam.setValue(ignoreDupsCheckBox.getValue().toString());
        btnProcessTrans.setEnabled(false);
        logger.info("****FileUpload processTRans selection: " + importDataFormPanel.getAction());
        submitUpload();
    }

    @UiHandler("btnCancel")
    public void cancel(ClickEvent e) {
        clear();
        importDataFormPanel.reset();
        importDataFileElement.clearErrorMsg();
    }

    private void submitUpload() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                WaitingDialogUtil.getInstance(MediaResourceUtil.getInstance().getWaitIcon(), scrollyPosLeft, scrollyPosTop);
                importDataFile.setEnabled(true);         //fileUpload must be enabled for the submit process to access the data!!
                String uploadUrl = GWT.getModuleBaseURL()+parentUpload.getUrlHandlerMapping();     //"secure/importcustomertrans.do";
                filenameForMessage = importDataFile.getFilename().replaceAll("/", "\\/");
                filenameForMessage = filenameForMessage.substring(filenameForMessage.lastIndexOf("\\") + 1);
                importDataFormPanel.setAction(uploadUrl);
                importDataFormPanel.submit();
                importDataFile.setEnabled(false);
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @UiHandler("importDataFormPanel")
    void handleProcessTransFormSubmit(SubmitEvent submitEvent) {
        logger.info("****FileUpload selection: " + importDataFormPanel.getAction() + " : " + importDataFile.getFilename());
    }

    @UiHandler("importDataFormPanel")
    void handleProcessTransFormSubmitComplete(SubmitCompleteEvent submitCompleteEvent) {
        WaitingDialog waiting = WaitingDialogUtil.getCurrentInstance();
        if (waiting != null) {
            waiting.hide();
        }
        importDataFile.setEnabled(true);
        String result = submitCompleteEvent.getResults();
        if (result == null || result.contains("<div id=\"main\">")) {
            if (clientFactory.getUser() == null) {
                sessionTimeout();
                return;
            }
        }
        logger.info("result=" + result);
        result = result.replace("<pre style=\"word-wrap: break-word; white-space: pre-wrap;\">", "").replace("</pre>", "").replace("&lt;", "<").replace("&gt;", ">").replace("&amp;", "").replace("&nbsp;","");
        logger.info("actionParam.getValue()=" + actionParam.getValue());
        String filenameMessage = MessagesUtil.getInstance().getMessage("bulk.upload.filename", new String[] {filenameForMessage} );


        /*
         * Cannot use GWT RPC calls when doing a File Upload
         * result will come back either with:   15 error transactions (ex actionParam="uploadCsv" and failed validations)
         *                                or:   up to 15 successful transactions (ex actionParam "uploadCsv" and successful validations)
         *                                or:   "EXCEPTION: ServiceException:" plus the messages.properties key to error message
         *                                or:   "EXCEPTION: " plus the messages.properties key to error message
         *                                or:   "BulkException:,messages.properties key,total,noSuccessfulTrans,noDuplicates,identifierType,identifier,accountRef"
         *                                                       (ex actionParam="processTrans" and failure on serverside)
         *                                or:   "SUCCESS:,,total,noSuccessfulTrans,noDuplicates"
         *                                                      (ex actionParam="processTrans" and successfully processed on serverside)
         */

        if (result.contains("EXCEPTION:")) {
            //this is messages.properties message
            String error = result.substring(result.indexOf(':') + 1, result.length()).trim();  //Strip off "EXCEPTION"
            if (error.contains("ServiceException")) {
                logger.info("ServiceException trapped: " + error);
                error=error.substring(error.indexOf(':') + 1); //Strip off "ServiceException"
                Dialogs.displayErrorMessage(MessagesUtil.getInstance().getMessage(error),
                        MediaResourceUtil.getInstance().getErrorIcon(),
                        MessagesUtil.getInstance().getMessage("button.close"));
            } else if (error.contains("BadHeading")) {
                logger.info("BadHeading trapped: " + error);
                error=error.substring(error.indexOf(':') + 1); //strip off "BadHeading"
                String errorKey = error.substring(0,error.indexOf('_')); //Strips out the key
                String[] paramArr= new String[] {error.substring(error.indexOf('_') + 1)};  //create param
                Dialogs.displayErrorMessage(
                        MessagesUtil.getInstance().getMessage(errorKey, paramArr),
                        MediaResourceUtil.getInstance().getErrorIcon(),
                        messagePosLeft,
                        messagePosTop + 50,
                        MessagesUtil.getInstance().getMessage("button.close"));
            } else {
                Dialogs.displayErrorMessage(
                        MessagesUtil.getInstance().getMessage(error),
                        MediaResourceUtil.getInstance().getErrorIcon(),
                        messagePosLeft,
                        messagePosTop + 50,
                        MessagesUtil.getInstance().getMessage("button.close"));
            }
            clear();

        } else if (actionParam.getValue().equals("uploadCsv")){
            displayUploadResults(result);

        } else if (actionParam.getValue().equals("processTrans")) {
            clear();

            if (result.contains("BulkException")) {
                //"BulkException:," + e.getMessage() + " : " + messageString already translated
                String error = result.substring(result.indexOf(",")+1);
                Dialogs.displayErrorMessage(
                        error,
                        MediaResourceUtil.getInstance().getErrorIcon(),
                        messagePosLeft,
                        messagePosTop + 50,
                        MessagesUtil.getInstance().getMessage("button.close"));
            } else {
                //success
                //just returns already translated SUCCESS message (meter upload only works with count of totals; account balance upload works with no dups as well!
                //so return the message already set up
                importDataFormPanel.reset();
                sendNotification();
                Dialogs.displayInformationMessage(
                        result + " " + filenameMessage,
                        MediaResourceUtil.getInstance().getInformationIcon(),
                        messagePosLeft,
                        messagePosTop,
                        MessagesUtil.getInstance().getMessage("button.close"));
            }
        } else {
            //unknown result!! What now??
            Window.alert(result);
            clear();
        }
    }

    private void sessionTimeout() {
        logger.info("Allowing user to login again: from NoCurrentUserException ex Bulk Upload");
        clear();
        LoginViewUtil.getInstance(MeterMngAdmin.getClientFactory()).displayLoginView();
    }

    private void clear() {
        btnUploadCsv.setEnabled(true);
        btnUploadCsv.setVisible(true);

        processCsvPanel.setVisible(false);

        btnProcessTrans.setVisible(false);
        btnProcessTrans.setEnabled(false);

        btnCancel.setVisible(false);
        btnCancel.setEnabled(false);

        importDataFile.setEnabled(true);
        uploadedFile = null;
        ignoreDupsCheckBox.setValue(false);
        uploadedIgnoreDupsSetting = false;

        errorMsg.setText("");
        errorMsg.setVisible(false);

        dataProvider.getList().clear();
        dataProvider.refresh();

        //downloadSamplePanel.setVisible(true);
    }

    private void displayUploadResults(String result) {
        processCsvPanel.setVisible(true);
        tableHeading.setVisible(true);
        clltblTransactions.setVisible(true);

        dataProvider.getList().clear();
        dataProvider.getList().addAll(parentUpload.getTransCsvList(result));
        dataProvider.refresh();

        if (parentUpload.isCsvDataValid()) {
            importDataFile.setEnabled(false);
            uploadedFile = importDataFile.getFilename();

            btnUploadCsv.setVisible(false);
            btnUploadCsv.setEnabled(false);

            btnProcessTrans.setEnabled(true);
            btnProcessTrans.setVisible(true);

            errorMsg.setText("");
            errorMsg.setVisible(false);

            tableHeading.setText(MessagesUtil.getInstance().getMessage("bulk.upload.table.heading.valid"));
            clltblTransactions.removeColumn(0);
        } else {
            btnUploadCsv.setEnabled(true);
            errorMsg.setText(MessagesUtil.getInstance().getMessage("bulk.upload.trans.validation.errors"));
            errorMsg.setVisible(true);
            tableHeading.setText(MessagesUtil.getInstance().getMessage("bulk.upload.table.heading.errors"));
        }

        btnCancel.setEnabled(true);
        btnCancel.setVisible(true);
    }

    private void sendNotification() {
        SessionCheckCallback sessionCheckCallback = new SessionCheckCallback() {
            @Override
            public void callback(SessionCheckResolution sessionCheckResolution) {
                if (uploadType.equals(MeterMngStatics.AUX_ACCOUNT_UPLOAD)) {
                    clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.AUX_ACCOUNT_UPLOAD));
                }
                if (uploadType.equals(MeterMngStatics.CUST_TRANSACTION_UPLOAD)) {
                    clientFactory.getWorkspaceContainer().notifyWorkspaces(new WorkspaceNotification(NotificationType.DATA_UPDATED, MeterMngStatics.CUST_TRANSACTION_UPLOAD));
                }
            }
        };
        clientFactory.handleSessionCheckCallback(sessionCheckCallback);
    }

    @Override
    public void onArrival(Place place) {
    }

    @Override
    public void onLeaving() {

    }

    @Override
    public void onSelect() {

    }

    @Override
    public void onClose() {

    }

    @Override
    public boolean isValid(T value, String filter) {
        return true;
    }

    @Override
    public boolean handles(Place place) {
        return false;
    }
}
