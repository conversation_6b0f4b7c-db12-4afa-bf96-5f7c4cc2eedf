package za.co.ipay.metermng.client.view.component.search.usagepoint;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.KeyDownHandler;
import com.google.gwt.event.logical.shared.ValueChangeEvent;
import com.google.gwt.event.logical.shared.ValueChangeHandler;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.uibinder.client.UiHandler;
import com.google.gwt.user.client.ui.CheckBox;
import com.google.gwt.user.client.ui.ListBox;
import com.google.gwt.user.client.ui.RadioButton;
import com.google.gwt.user.client.ui.TextBox;
import com.google.gwt.user.client.ui.Widget;
import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.shared.LookupListItem;
import za.co.ipay.gwt.common.shared.dto.IdNameDto;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.history.SearchPlace;
import za.co.ipay.metermng.client.rpc.AppSettingRpcAsync;
import za.co.ipay.metermng.client.rpc.callback.ClientCallback;
import za.co.ipay.metermng.client.view.component.BaseComponent;
import za.co.ipay.metermng.client.widget.pricingstructure.PricingStructureLookup;
import za.co.ipay.metermng.client.view.component.search.Search;
import za.co.ipay.metermng.client.view.workspace.search.AdvancedSearchWorkspaceView;
import za.co.ipay.metermng.mybatis.generated.model.AppSetting;
import za.co.ipay.metermng.shared.MeterMngStatics;
import za.co.ipay.metermng.shared.SearchType;
import za.co.ipay.metermng.shared.dto.search.SearchData;
import za.co.ipay.metermng.shared.dto.search.SearchResultType;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class UsagePointSearchPanel extends BaseComponent implements Search {

    @UiField FormElement nameElement;
    @UiField FormElement pricingStructureElement;
    @UiField FormElement paymentModeElement;
    @UiField FormElement customText1Element;
    @UiField FormElement usagePointNoCustElement;
    @UiField FormElement usagePointNoMeterElement;
    @UiField FormRowPanel customText1Row;
    @UiField TextBox nameBox;
    @UiField TextBox customText1Box;
    @UiField ListBox lstbxPaymentMode;
    @UiField CheckBox chckbxUsagePointNoCust;
    @UiField CheckBox chckbxUsagePointNoMeter;
    @UiField RadioButton startWithBox;
    @UiField RadioButton containsBox;
    @UiField(provided = true) PricingStructureLookup pricingStructureLookup;

    private static final UsagePointSearchPanelUiBinder uiBinder = GWT.create(UsagePointSearchPanelUiBinder.class);

    interface UsagePointSearchPanelUiBinder extends UiBinder<Widget, UsagePointSearchPanel> {
    }

    public UsagePointSearchPanel(ClientFactory clientFactory, final AdvancedSearchWorkspaceView advancedSearchWorkspaceView) {
        this.clientFactory = clientFactory;
        pricingStructureLookup = new PricingStructureLookup(true,null, clientFactory);
        initWidget(uiBinder.createAndBindUi(this));

        chckbxUsagePointNoCust.addValueChangeHandler(new ValueChangeHandler<Boolean>() {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event) {
                if (chckbxUsagePointNoCust.getValue()) {
                    advancedSearchWorkspaceView.getCustomerSearchForm().clear();
                    advancedSearchWorkspaceView.getCustomerSearchForm().disable();
                    advancedSearchWorkspaceView.getMeterSearchForm().hideCheckbox();
                    advancedSearchWorkspaceView.getLocationSearchForm().getPanel().disableCustomerLocationChkBox();
                } else {
                    advancedSearchWorkspaceView.getCustomerSearchForm().enable();
                    advancedSearchWorkspaceView.getMeterSearchForm().showCheckbox();
                    advancedSearchWorkspaceView.getLocationSearchForm().getPanel().enableCustomerLocationChkBox();
                }
            }
        });
        chckbxUsagePointNoMeter.addValueChangeHandler(new ValueChangeHandler<Boolean>() {
            @Override
            public void onValueChange(ValueChangeEvent<Boolean> event) {
                if (chckbxUsagePointNoMeter.getValue()) {
                    advancedSearchWorkspaceView.getMeterSearchForm().clear();
                    advancedSearchWorkspaceView.getMeterSearchForm().disable();
                    advancedSearchWorkspaceView.getCustomerSearchForm().hideCheckbox();
                } else {
                    advancedSearchWorkspaceView.getMeterSearchForm().enable();
                    advancedSearchWorkspaceView.getCustomerSearchForm().showCheckbox();
                }
            }
        });

        pricingStructureLookup.updateLookupList();
        populatePaymentModeListbox();
        toggleCustomText1Visibility();
    }

    @UiHandler("pricingStructureLookup")
    void handlePricingStructureValueChange(ValueChangeEvent<String> event) {
        LookupListItem item = pricingStructureLookup.getSelectedPricingStructureItem();
        String extraInfo = null;
        if (item != null) {
            extraInfo = item.getExtraInfo();
            if (lstbxPaymentMode.getItemCount() > 0) {
                for (int i = 0; i < lstbxPaymentMode.getItemCount(); i++) {
                    if (Objects.equals(lstbxPaymentMode.getValue(i), extraInfo)) {
                        lstbxPaymentMode.setSelectedIndex(i);
                        break;
                    }
                }
            }
            lstbxPaymentMode.setEnabled(false);
        } else {
            lstbxPaymentMode.setEnabled(true);
            clearPaymentModeSelections();
        }
        handlePricingStructureChange(extraInfo);
    }

    private void handlePricingStructureChange(String extraInfo) {
        if (extraInfo == null) {
            lstbxPaymentMode.setEnabled(true);
            clearPaymentModeSelections();
        } else {
            int itemCount = lstbxPaymentMode.getItemCount();
            if (itemCount > 0) {
                for (int i = 0; i < itemCount; i++) {
                    if (lstbxPaymentMode.getValue(i).equals(extraInfo)) {
                        // get(indx - 1) because the listbox has a extra blank line in index 0
                        lstbxPaymentMode.setSelectedIndex(i);
                        break;
                    }
                }
            }
            lstbxPaymentMode.setEnabled(false);
        }
    }

    public void toggleCustomText1Visibility() {
        final AppSettingRpcAsync appSettingRpc = clientFactory.getAppSettingRpc();
        appSettingRpc.getAppSettingByKey(MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1_STATUS,
                new ClientCallback<AppSetting>() {
                    @Override
                    public void onSuccess(AppSetting customStatusResult) {
                        if (!customStatusResult.getValue().equals("UNAVAILABLE")) {
                            appSettingRpc.getAppSettingByKey(MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1_LABEL,
                                    new ClientCallback<AppSetting>() {
                                        @Override
                                        public void onSuccess(AppSetting customLabelResult) {
                                            customText1Element.setLabelText(customLabelResult.getValue());
                                        }
                                    });
                            customText1Row.setVisible(true);
                        } else {
                            customText1Row.setVisible(false);
                        }
                    }
                });
    }

    private void populatePaymentModeListbox() {
        ClientCallback<ArrayList<IdNameDto>> lookupSvcAsyncCallback = new ClientCallback<ArrayList<IdNameDto>>() {
            @Override
            public void onSuccess(ArrayList<IdNameDto> result) {
                lstbxPaymentMode.clear();
                if (result != null) {
                    for (int i = 0; i < result.size(); i++) {
                        lstbxPaymentMode.addItem(result.get(i).getName(), result.get(i).getId().toString());
                    }
                }
            }
        };
        clientFactory.getLookupRpc().getPaymentModes(lookupSvcAsyncCallback);
    }

    @Override
    public void clear() {
        clearFields();
        clearErrors();
    }

    private void clearFields() {
        nameBox.setText("");
        customText1Box.setText("");
        pricingStructureLookup.clearSelection();
        clearPaymentModeSelections();
        chckbxUsagePointNoCust.setValue(false);
        chckbxUsagePointNoMeter.setValue(false);
        startWithBox.setValue(true);
        containsBox.setValue(false);
    }

    private void clearPaymentModeSelections() {
        if (lstbxPaymentMode.getItemCount() > 0) {
            for (int i = 0; i < lstbxPaymentMode.getItemCount(); i++) {
                lstbxPaymentMode.setItemSelected(i, false);
            }
        }
    }

    private void clearErrors() {
        nameElement.showErrorMsg(null);
        customText1Element.showErrorMsg(null);
        pricingStructureElement.showErrorMsg(null);
        paymentModeElement.showErrorMsg(null);
        usagePointNoCustElement.showErrorMsg(null);
        usagePointNoMeterElement.showErrorMsg(null);
    }

    @Override
    public boolean displayCriteria(SearchPlace searchPlace) {
        if (SearchResultType.USAGE_POINT.equals(searchPlace.getDataType())
                && searchPlace.getSearchText() != null && !searchPlace.getSearchText().trim().equals("")) {
            nameBox.setText(searchPlace.getSearchText());
            containsBox.setValue(true);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean isValidInput() {
        clearErrors();
        if (isValid(nameBox.getText())) {
            return true;
        } else if (isValid(customText1Box.getText())) {
            return true;
        } else if (getPricingStructureId() != null) {
            return true;
        } else if (isMultiSelected(lstbxPaymentMode)) {
            return true;
        } else if (chckbxUsagePointNoCust.getValue()) {
            return true;
        } else if (chckbxUsagePointNoMeter.getValue()) {
            return true;
        } else {
            return false;
        }
    }

    private boolean isValid(String s) {
        if (s != null && !s.trim().equals("")) {
            return true;
        } else {
            return false;
        }
    }

    private boolean isMultiSelected(ListBox multiSelectBox) {
        for (int i = 0, l = multiSelectBox.getItemCount(); i < l; i++) {
            if (multiSelectBox.isItemSelected(i)) {
                return true;
            }
        }
        return false;
    }

    private List<Long> getMultiSelected(ListBox multiSelectBox) {
        List<Long> selectedValues = new ArrayList<Long>();
        for (int i = 0, l = multiSelectBox.getItemCount(); i < l; i++) {
            if (multiSelectBox.isItemSelected(i)) {
                selectedValues.add(Long.valueOf(multiSelectBox.getValue(i)));
            }
        }
        return selectedValues;
    }

    @Override
    public void populateSearchCriteria(SearchData searchData) {
        if (isValid(nameBox.getText())) {
            searchData.addCriteria(MeterMngStatics.USAGE_POINT_NAME_SEARCH, nameBox.getText().trim());
        }
        if (isValid(customText1Box.getText())) {
            searchData.addCriteria(MeterMngStatics.USAGE_POINT_CUSTOM_VARCHAR1, customText1Box.getText().trim());
        }
        if (pricingStructureLookup.getSelectedPricingStructureItem() != null
                && !pricingStructureLookup.getSelectedPricingStructureItem().getText().trim().isEmpty()) {
            searchData.addCriteria(MeterMngStatics.PRICING_STRUCTURE_ID_SEARCH,
                    pricingStructureLookup.getSelectedPricingStructureItem().getValue());
        }
        String pricingStructureId = getPricingStructureId();
        if (pricingStructureId != null) {
            searchData.addCriteria(MeterMngStatics.PRICING_STRUCTURE_ID_SEARCH, pricingStructureId);
        }
        List<Long> selectedPaymentModes = getMultiSelected(lstbxPaymentMode);
        if (selectedPaymentModes.size() > 0) {
            searchData.addCriteria(MeterMngStatics.PAYMENT_MODE_ID_SEARCH, selectedPaymentModes);
        }
        if (chckbxUsagePointNoCust.getValue()) {
            searchData.addCriteria(MeterMngStatics.USAGE_POINT_NO_CUSTOMER_SEARCH, "true");
        }
        if (chckbxUsagePointNoMeter.getValue()) {
            searchData.addCriteria(MeterMngStatics.USAGE_POINT_NO_METER_SEARCH, "true");
        }
        if (startWithBox.getValue()) {
            searchData.addCriteria(MeterMngStatics.USAGE_POINT_NAME_SEARCH_TYPE, SearchType.STARTS_WITH.name());
        } else {
            searchData.addCriteria(MeterMngStatics.USAGE_POINT_NAME_SEARCH_TYPE, SearchType.CONTAINS.name());
        }
    }

    @Override
    public void addDefaultKeyHandler(KeyDownHandler handler) {
        nameBox.addKeyDownHandler(handler);
        customText1Box.addKeyDownHandler(handler);
        pricingStructureLookup.addKeyDownHandler(handler);
        lstbxPaymentMode.addKeyDownHandler(handler);
        chckbxUsagePointNoCust.addKeyDownHandler(handler);
        chckbxUsagePointNoMeter.addKeyDownHandler(handler);
    }

    private String getPricingStructureId() {
        String value = null;
        if (pricingStructureLookup.getSelectedPricingStructureItem() != null) {
            value = pricingStructureLookup.getSelectedPricingStructureItem().getValue();
        }
        if (value != null && value.trim().isEmpty()) {
            value = null;
        }
        return value;
    }
}
