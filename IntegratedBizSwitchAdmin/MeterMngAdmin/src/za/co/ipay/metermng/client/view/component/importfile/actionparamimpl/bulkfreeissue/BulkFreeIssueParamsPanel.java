package za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.bulkfreeissue;

import java.math.BigDecimal;

import com.google.gwt.core.client.GWT;
import com.google.gwt.event.dom.client.ChangeEvent;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Label;
import com.google.gwt.user.client.ui.VerticalPanel;
import com.google.gwt.user.client.ui.Widget;

import za.co.ipay.gwt.common.client.form.FormElement;
import za.co.ipay.gwt.common.client.form.FormRowPanel;
import za.co.ipay.gwt.common.client.form.HasDirtyData;
import za.co.ipay.gwt.common.client.form.HasDirtyDataManager;
import za.co.ipay.gwt.common.client.form.LocalOnlyHasDirtyDataManager;
import za.co.ipay.gwt.common.client.handler.FormDataChangeHandler;
import za.co.ipay.gwt.common.client.resource.Messages.MessagesUtil;
import za.co.ipay.gwt.common.client.widget.IpayTextBox;
import za.co.ipay.gwt.common.client.widget.ProvidesResize;
import za.co.ipay.gwt.common.client.widget.RequiresResize;
import za.co.ipay.metermng.client.factory.ClientFactory;
import za.co.ipay.metermng.client.view.component.importfile.actionparamimpl.ActionParamsComponent;
import za.co.ipay.metermng.shared.dto.BulkParamRecord;
import za.co.ipay.metermng.shared.integration.bulkfreeissue.BulkFreeIssueParamRecord;

/**
 * Parameters panel for bulk free issue operations
 */
public class BulkFreeIssueParamsPanel extends ActionParamsComponent implements ProvidesResize, RequiresResize {

    @UiField VerticalPanel bulkFreeIssueParamsPanel;
    @UiField Label bulkFreeIssueParamHeading;
    @UiField FormElement maxUnitsElement;
    @UiField IpayTextBox maxUnitsTextBox;
    @UiField FormElement defaultDescriptionElement;
    @UiField IpayTextBox defaultDescriptionTextBox;
    @UiField FormElement defaultReferenceElement;
    @UiField IpayTextBox defaultReferenceTextBox;
    @UiField FormElement defaultReasonElement;
    @UiField IpayTextBox defaultReasonTextBox;

    private BulkFreeIssueParamRecord bulkFreeIssueParamRecord;
    private HasDirtyData hasDirtyData;
    HasDirtyDataManager hasDirtyDataManager;

    private static BulkFreeIssueParamsPanelUiBinder uiBinder = GWT.create(BulkFreeIssueParamsPanelUiBinder.class);

    interface BulkFreeIssueParamsPanelUiBinder extends UiBinder<Widget, BulkFreeIssueParamsPanel> {
    }

    public BulkFreeIssueParamsPanel(ClientFactory clientFactory, String fileName,
            BulkFreeIssueParamRecord bulkFreeIssueParamRecord) {
        super();
        this.clientFactory = clientFactory;
        this.bulkFreeIssueParamRecord = bulkFreeIssueParamRecord;
        hasDirtyDataManager = new LocalOnlyHasDirtyDataManager();
        hasDirtyData = hasDirtyDataManager.createAndRegisterHasDirtyData();
        initWidget(uiBinder.createAndBindUi(this));
        bulkFreeIssueParamHeading
                .setText(MessagesUtil.getInstance().getMessage("bulk.free.issue.header", new String[] { fileName }));
        addFieldHandlers();
        mapDataToForm();
    }

    private void addFieldHandlers() {
        maxUnitsTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData) {
            @Override
            public void onChange(ChangeEvent event) {
                super.onChange(event);
                clearErrorMessages();
            }
        });
        defaultDescriptionTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        defaultReferenceTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
        defaultReasonTextBox.addChangeHandler(new FormDataChangeHandler(hasDirtyData));
    }

    private void clearErrorMessages() {
        maxUnitsElement.clearErrorMsg();
        defaultDescriptionElement.clearErrorMsg();
        defaultReferenceElement.clearErrorMsg();
        defaultReasonElement.clearErrorMsg();
    }

    @Override
    public void mapDataToForm() {
        clearErrorMessages();
        if (bulkFreeIssueParamRecord != null) {
            if (bulkFreeIssueParamRecord.getMaxUnitsPerMeter() != null) {
                maxUnitsTextBox.setText(bulkFreeIssueParamRecord.getMaxUnitsPerMeter().toString());
            }
            if (bulkFreeIssueParamRecord.getDefaultDescription() != null) {
                defaultDescriptionTextBox.setText(bulkFreeIssueParamRecord.getDefaultDescription());
            }
            if (bulkFreeIssueParamRecord.getDefaultReference() != null) {
                defaultReferenceTextBox.setText(bulkFreeIssueParamRecord.getDefaultReference());
            }
            if (bulkFreeIssueParamRecord.getDefaultReason() != null) {
                defaultReasonTextBox.setText(bulkFreeIssueParamRecord.getDefaultReason());
            }
        }
    }

    @Override
    public boolean validateForm() {
        clearErrorMessages();
        boolean isValidated = true;
        
        // Validate max units
        String maxUnitsText = maxUnitsTextBox.getText().trim();
        if (!maxUnitsText.isEmpty()) {
            try {
                BigDecimal maxUnits = new BigDecimal(maxUnitsText);
                if (maxUnits.compareTo(BigDecimal.ZERO) < 0) {
                    maxUnitsElement.setErrorMsg(MessagesUtil.getInstance().getMessage("bulk.free.issue.max.units.negative"));
                    isValidated = false;
                }
            } catch (NumberFormatException e) {
                maxUnitsElement.setErrorMsg(MessagesUtil.getInstance().getMessage("bulk.free.issue.max.units.invalid"));
                isValidated = false;
            }
        }
        
        return isValidated;
    }

    @Override
    public BulkParamRecord mapFormToData() {
        bulkFreeIssueParamRecord = new BulkFreeIssueParamRecord();
        
        String maxUnitsText = maxUnitsTextBox.getText().trim();
        if (!maxUnitsText.isEmpty()) {
            try {
                bulkFreeIssueParamRecord.setMaxUnitsPerMeter(new BigDecimal(maxUnitsText));
            } catch (NumberFormatException e) {
                // Validation should have caught this
                bulkFreeIssueParamRecord.setMaxUnitsPerMeter(BigDecimal.ZERO);
            }
        }
        
        bulkFreeIssueParamRecord.setDefaultDescription(defaultDescriptionTextBox.getText().trim());
        bulkFreeIssueParamRecord.setDefaultReference(defaultReferenceTextBox.getText().trim());
        bulkFreeIssueParamRecord.setDefaultReason(defaultReasonTextBox.getText().trim());
        
        return bulkFreeIssueParamRecord;
    }

    @Override
    public boolean checkDirtyData() {
        return hasDirtyData.isDirtyData();
    }

    @Override
    public void onResize() {
        new Timer() {
            @Override
            public void run() {
                bulkFreeIssueParamsPanel.setWidth("100%");
            }
        }.schedule(100);
    }
}
